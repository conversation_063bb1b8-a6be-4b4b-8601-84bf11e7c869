#!/usr/bin/env python
import time
from pathlib import Path
import cv2
import tempfile
import json
import sys
import os
import numpy as np

# 导入自定义的PIV适配器
from src.piv.pypiv_adapter import process_with_pypiv
from src.piv.adaptivepiv_adapter import process_with_adaptivepiv

# 自定义 JSON 序列化函数来处理 NumPy 类型 (与awive_calculation.py中相同)
def convert_numpy(obj):
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    raise TypeError(f'Object of type {obj.__class__.__name__} is not JSON serializable')

def analyze_video_piv(video_path, piv_config_path, show_results=False):
    """
    对视频文件进行PIV流速分析

    Args:
        video_path: 视频文件路径
        piv_config_path: PIV算法的配置文件路径
        show_results: 是否显示处理结果 (当前主要用于日志控制) - 这个参数目前未在函数内部直接控制可视化输出，主要用于外部调用时的日志或进一步处理控制。

    Returns:
        dict: 流速分析结果 (具体结构待定)
    """
    # --- 初始化 ---
    piv_config_dict = None
    # temp_config_file = None # 根据PIV库是否需要临时文件来决定
    # adjusted_config_path = None # 根据PIV库是否需要临时文件来决定

    try:
        # 1. 加载PIV配置文件
        try:
            with open(piv_config_path, 'r') as f:
                piv_config_dict = json.load(f)
            
            # 从配置中获取要使用的PIV库和通用参数
            selected_piv_library = piv_config_dict.get("piv_library")
            if not selected_piv_library:
                print("错误: PIV配置文件中未指定 'piv_library'", file=sys.stderr)
                return None
            
            common_params = piv_config_dict.get("common_params", {})
            roi_rect = common_params.get("roi_rect", None)
            frame_interval = common_params.get("frame_interval", 1)

            print(f"选择的PIV库: {selected_piv_library}")
            print(f"通用参数: {common_params}") # 打印加载的通用参数
            print(f"ROI 区域: {roi_rect}") # 打印加载的ROI区域
            print(f"帧间隔: {frame_interval}") # 打印加载的帧间隔

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"加载PIV配置文件 {piv_config_path} 时出错: {e}", file=sys.stderr)
            return None
        except Exception as e:
            print(f"读取PIV配置文件 {piv_config_path} 时发生意外错误: {e}", file=sys.stderr)
            return None

        if piv_config_dict is None:
            raise ValueError("无法加载PIV配置文件")

    except (FileNotFoundError, json.JSONDecodeError, ValueError) as e:
        print(f"加载或处理PIV配置时出错: {e}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"处理PIV配置时发生意外错误: {e}", file=sys.stderr)
        return None

    # --- 加载视频帧 (可选, 根据PIV库需求和可视化需求) ---
    video_file_path_str = str(Path(video_path).resolve())
    cap = None # 初始化 cap 变量
    try:
        cap = cv2.VideoCapture(video_file_path_str)
        if not cap.isOpened():
            print(f"错误: 无法打开视频文件: {video_file_path_str}", file=sys.stderr)
            return None

        # 跳过第一帧（initial_frame 已在前面尝试读取，但这里需要重新读取）
        # 如果需要跳过更多帧，可以在这里循环 cap.read()
        frame_idx = 0
        ret, frame1 = cap.read() # 读取第一帧
        frame_idx += 1
        if not ret:
             print("错误: 无法读取第一帧或视频为空。", file=sys.stderr)
             return None

        # 如果指定了ROI，则裁剪第一帧
        if roi_rect:
            x, y, w, h = roi_rect
            # 确保ROI矩形有效
            if x >= 0 and y >= 0 and w > 0 and h > 0 and (x + w) <= frame1.shape[1] and (y + h) <= frame1.shape[0]:
                frame1 = frame1[y:y+h, x:x+w]
            else:
                print(f"警告: common_params 中的 roi_rect {roi_rect} 无效，将使用整个帧。", file=sys.stderr)
                roi_rect = None # 标记为无效，不再应用裁剪

        # 跳过中间帧以实现 frame_interval 间隔
        # frame_interval 至少为 1，所以跳过 frame_interval - 1 帧
        for _ in range(frame_interval - 1):
            ret, _ = cap.read()
            frame_idx += 1
            if not ret:
                print(f"警告: 视频在读取第 {frame_idx} 帧时结束，不足以提供下一帧进行PIV分析。", file=sys.stderr)
                # 如果无法读取足够的帧，需要在此处关闭cap并返回
                cap.release()
                return None

        # 读取第二帧
        ret, frame2 = cap.read()
        frame_idx += 1
        if not ret:
            print("错误: 无法读取第二帧或视频结束。", file=sys.stderr)
            # 如果无法读取第二帧，需要在此处关闭cap并返回
            cap.release()
            return None

        # 如果指定了ROI，则裁剪第二帧
        if roi_rect:
             x, y, w, h = roi_rect
             frame2 = frame2[y:y+h, x:x+w] # 此时roi_rect已验证有效

        # 转换为灰度图（PIV库通常使用灰度图）
        gray_frame1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
        gray_frame2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)

        # --- 执行PIV分析 ---
        t_process_start = time.process_time() # 在处理前开始计时

        if selected_piv_library == "pypiv":
            print("使用 pypiv 库进行处理...")
            pypiv_params = piv_config_dict.get("pypiv_params", {})
            # 调用pypiv适配器
            results_piv, output_visualization = process_with_pypiv(gray_frame1, gray_frame2, pypiv_params, common_params)

        elif selected_piv_library == "AdaptivePIV":
            print("使用 AdaptivePIV 库进行处理...")
            adaptive_piv_params = piv_config_dict.get("AdaptivePIV_params", {})
            # 调用AdaptivePIV适配器
            results_piv, output_visualization = process_with_adaptivepiv(gray_frame1, gray_frame2, adaptive_piv_params, common_params)

        else:
            print(f"错误: 未知的PIV库 '{selected_piv_library}'", file=sys.stderr)
            return None

        elapsed_time = time.process_time() - t_process_start

        # --- 输出和可视化/保存结果 ---
        if results_piv is not None:
            print("\nPIV 分析结果:")
            # TODO: 根据所使用的PIV库的实际返回结果结构，在此处进行结果的格式化输出或进一步处理。
            # 例如，如果 results_piv 是一个包含速度向量数组的结构，您可能需要遍历并打印或保存这些向量。
            # 如果库返回的是一个特定对象，您可能需要调用其方法来获取详细信息。
            # 打印原始结果作为参考
            print(f"  原始结果类型: {type(results_piv)}")
            print(f"  原始结果 (部分或摘要): {str(results_piv)[:500]}...") # 打印结果的前500字符，避免打印过大的对象

            # TODO: 根据需要添加计算平均流速等指标的逻辑。
            # 这取决于 results_piv 中速度数据的格式和单位。
            # 示例:
            # if isinstance(results_piv, np.ndarray) and results_piv.ndim == 2 and results_piv.shape[1] >= 2:
            # # 假设 results_piv 是一个 NxM 数组，其中速度分量在特定列
            # # 例如，如果速度是 (vx, vy) 在最后两列:
            # velocities = results_piv[:, -2:]
            # speeds = np.linalg.norm(velocities, axis=1) # 计算每个向量的速度大小
            # avg_velocity_magnitude = np.mean(speeds)
            # print(f"\n平均速度大小: {avg_velocity_magnitude:.4f} (单位取决于 results_piv 的单位和 scaling_factor)")

        else:
             print("\nPIV算法未返回有效结果。请检查PIV适配器和配置。")

        print(f"\nPIV 处理时间: {elapsed_time:.2f} 秒")

        # 保存 PIV 结果图像 (如果生成了)
        if output_visualization is not None:
            try:
                output_dir = Path("data/output_piv") # 使用新的输出目录
                output_dir.mkdir(parents=True, exist_ok=True)
                video_stem = Path(video_path).stem
                output_image_path = output_dir / f"{video_stem}_{selected_piv_library}_result.png"
                # 检查 output_visualization 是否是有效的图像格式（例如 NumPy 数组）
                if isinstance(output_visualization, np.ndarray):
                     cv2.imwrite(str(output_image_path), output_visualization)
                     print(f"PIV 结果图像已保存至: {output_image_path}")
                else:
                     print(f"警告: {selected_piv_library} 算法返回的可视化结果不是有效的图像格式，无法保存。", file=sys.stderr)

            except Exception as e:
                print(f"保存 PIV 图像时出错: {e}", file=sys.stderr)
        elif output_visualization is None:
             print(f"{selected_piv_library} 算法运行但未返回可保存的图像。")

        return results_piv # 返回原始的PIV结果对象/结构

    except Exception as e:
        print(f"执行PIV分析时发生错误: {e}", file=sys.stderr)
        return None
    finally:
        # 清理视频捕获对象
        if cap is not None:
            cap.release()
            # print("视频捕获对象已释放。") # 调试信息
        # 清理临时文件 (如果需要)
        # if temp_config_file and os.path.exists(temp_config_file):
        # try:
        # os.remove(temp_config_file)
        # print(f"临时配置文件已删除: {temp_config_file}")
        # except OSError as e:
        # print(f"错误: 无法删除临时文件 {temp_config_file}: {e}", file=sys.stderr)

# 使用示例
if __name__ == "__main__":
    # 设置PIV配置文件路径
    piv_config_file = Path("config/piv_config.json").resolve() # 新的配置文件名
    # 设置视频文件路径 (与awive_calculation.py中相同或不同，根据需要调整)
    video_file = Path("data/output/trimmed_20250509_133001.mp4").resolve()

    if not piv_config_file.is_file():
        print(f"错误: PIV配置文件未找到: {piv_config_file}", file=sys.stderr)
        # 自动创建一个简单的示例配置文件
        print(f"正在创建示例PIV配置文件: {piv_config_file}...")
        try:
            default_piv_config_content = {
                "piv_library": "AdaptivePIV", # 或者 "pypiv"
                "common_params": {
                    "dt": 0.01, # 时间间隔（秒），与视频帧率和 frame_interval 相关
                    "scaling_factor": 0.005, # 像素到米的缩放因子，例如，1像素等于0.005米
                    "roi_rect": None, # [x, y, width, height] 视频帧的裁剪区域，null表示不裁剪
                    "frame_interval": 1 # 处理视频帧的时间间隔，例如 1 表示连续两帧，2 表示跳过一帧
                },
                "AdaptivePIV_params": {
                    "settings_type": "AdaptStructSettings", # 或者 "WidimSettings", "AdaptSettings"
                    "init_WS": 65,
                    "final_WS": 33,
                    "WOR": 0.5,
                    "n_iter_main": 3,
                    "n_iter_ref": 1,
                    "vec_val": "NMT",
                    "interp": "struc_cub", # 或 'unstruc_cub' 根据 settings_type
                    "verbosity": 2,
                    "mask_rect": None, # [x, y, width, height] 掩码区域，null表示无掩码
                    # AdaptStructSettings/AdaptSettings 特有参数
                    "part_detect": "simple",
                    "sd_P_target": 20,
                    "target_init_NI": 20,
                    "target_fin_NI": 8,
                    # WidimSettings 特有参数 (示例，实际参数可能不同)
                    # "WOR": 0.5, # 已在上方共有参数中
                    # AdaptStructSettings 特有参数
                    "init_spacing": "auto", # 或具体数值 (像素)
                    "final_spacing": "auto", # 或具体数值 (像素)
                    # AdaptSettings 特有参数
                    "init_N_windows": 2500,
                    "final_N_windows": 10000,
                    "distribution_method": "AIS",
                    "idw": True
                    # 添加AdaptivePIV库所需的其他参数
                },
                "pypiv_params": {
                    "processing_method": "DirectPIV", # 或者 "AdaptivePIV"
                    "DirectPIV": {
                        "window_size": 32,
                        "search_size": 32,
                        "distance": 16,
                        "method": "gaussian" # 或 '9point'
                    },
                    "AdaptivePIV_settings": {
                        "window_size": 32,
                        "search_size": 32,
                        "distance": 16,
                        "deformation": "forward", # 或 'central'
                        "ipmethod": "bilinear"
                    },
                    "filters": {
                        "enable_outlier_from_local_median": True,
                        "outlier_threshold": 2.0,
                        "enable_replace_outliers": True,
                        "enable_median_filter": True
                    }
                }
            }
            piv_config_file.parent.mkdir(parents=True, exist_ok=True) #确保config目录存在
            with open(piv_config_file, 'w') as f_cfg:
                # 使用自定义的JSON序列化函数来处理可能的NumPy类型（尽管这里不需要，但保持一致性）
                json.dump(default_piv_config_content, f_cfg, indent=4, default=convert_numpy)
            print(f"示例PIV配置文件已创建。请根据需要修改参数。")
        except Exception as e_cfg:
            print(f"创建示例PIV配置文件失败: {e_cfg}", file=sys.stderr)
            sys.exit(1)

    if not video_file.is_file():
         print(f"错误: 视频文件未找到: {video_file}", file=sys.stderr)
         sys.exit(1)

    # 在调用分析函数前，检查视频和配置文件是否存在已在前面完成
    # common_params 和 roi_rect 在 analyze_video_piv 函数内部加载

    analysis_results = analyze_video_piv(
        video_path=video_file,
        piv_config_path=piv_config_file,
        show_results=True # 这个参数目前主要用于控制日志或未来可能的输出
    )

    if analysis_results is not None: # 检查是否为None，因为PIV函数可能在出错时返回None
        print("\nPIV分析完成。")
        # TODO: 在这里进一步处理 analysis_results，例如保存到文件或进行可视化
        # analysis_results 的具体结构取决于调用的PIV库适配器的返回。
        # 您可能需要根据实际返回的 results_piv 结构来调整这里的处理逻辑。
        # 示例：尝试打印结果的字符串表示或部分内容
        print("分析结果摘要或打印（完整结果请根据库的输出格式处理）：")
        try:
            # 尝试将结果转换为JSON格式打印，如果结果是基本Python类型或可以通过convert_numpy处理
            print(json.dumps(analysis_results, indent=4, default=convert_numpy))
        except TypeError as json_e:
            print(f"结果无法完全序列化为JSON进行打印: {json_e}. 结果类型: {type(analysis_results)}")
            print(f"结果的字符串表示 (部分): {str(analysis_results)[:500]}...") # 打印结果的字符串表示作为备用
        except Exception as other_e:
            print(f"尝试打印结果时发生错误: {other_e}")
            print(f"结果的字符串表示 (部分): {str(analysis_results)[:500]}...") # 打印结果的字符串表示作为备用

    else:
        print("PIV分析未能成功完成。") 