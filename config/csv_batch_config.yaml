# CSV批量视频裁剪配置文件
# 用于处理大型视频文件的批量裁剪任务

# 文件路径配置
paths:
  video_base_path: "/home/<USER>/data/xiwu/"  # 原始视频文件基础路径
  output_dir: "data/video/"                 # 裁剪后视频的输出目录
  csv_file: "config/selected.csv"           # CSV任务文件路径

# 裁剪参数配置
trimming:
  buffer_seconds: 5        # 前后缓冲时间（秒）
  min_duration: 30          # 最小裁剪时长（秒）
  skip_existing: true       # 是否跳过已存在的输出文件
  
# 输出文件配置
output:
  naming_pattern: "{original_name}_{start_time}_{end_time}.mp4"  # 输出文件命名模式
  create_log: true          # 是否创建处理日志
  log_file: "data/output/csv_batch_log.txt"  # 日志文件路径

# 处理选项
processing:
  max_concurrent: 1         # 最大并发处理数（建议为1，避免磁盘IO冲突）
  show_progress: true       # 是否显示处理进度
  add_visual_elements: false # 是否在输出视频中添加网格和ROI标记

# OCR时间戳检测配置（可选功能）
ocr:
  enabled: false            # 是否启用OCR时间戳校正
  roi_area: [0, 0, 400, 60] # OCR检测区域 [x, y, width, height]
  confidence_threshold: 0.8 # OCR置信度阈值
  
# 调试和测试选项
debug:
  test_mode: false          # 测试模式，只处理前几个条目
  test_count: 3             # 测试模式下处理的条目数量
  verbose: true             # 详细输出模式 