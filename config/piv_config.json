{"piv_library": "OpenPIV", "common_params": {"dt": 0.5, "roi_rect": null}, "openpiv_params": {"window_size": 32, "overlap": 31, "search_area_size": 38, "correlation_method": "circular", "subpixel_method": "gaussian", "sig2noise_method": "peak2peak", "sig2noise_threshold": 0, "replace_vectors": true, "std_threshold": 5, "median_threshold": 1.5, "filter_method": "gaussian", "filter_kernel_size": 1, "min_max_u": [-1000, 1000], "min_max_v": [-1000, 1000], "enable_preprocessing": false, "preprocessing_method": null}}