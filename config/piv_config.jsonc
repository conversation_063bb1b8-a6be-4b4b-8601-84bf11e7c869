{
    // PIV算法库选择
    "piv_library": "OpenPIV",
    
    // 通用参数
    "common_params": {
        // 帧间时间间隔（秒），用于将像素位移转换为物理速度
        "dt": 0.5,
        // 感兴趣区域矩形，null表示使用整个图像
        "roi_rect": null
    },
    
    // OpenPIV特定参数
    "openpiv_params": {
        // 询问窗口大小（像素），影响计算速度和空间分辨率
        // 建议值：32或64，通常为2的幂次方
        "window_size": 32,
        
        // 窗口重叠像素数，增加重叠可以提高速度场分辨率
        // 建议值：window_size/2，例如16或32
        "overlap": 16,
        
        // 搜索区域大小（像素），需要大于等于window_size
        // 建议值：通常等于window_size或稍大，例如32或64
        "search_area_size": 32,
        
        // 互相关计算方法
        // 选项：'circular'（循环相关，推荐）或'linear'（线性相关）
        "correlation_method": "circular",
        
        // 亚像素估计方法，用于提高位移精度
        // 选项：'gaussian'（高斯拟合，推荐）或其他方法
        "subpixel_method": "gaussian",
        
        // 信噪比计算方法
        // 选项：'peak2peak'（峰峰值比，推荐）或其他方法
        "sig2noise_method": "peak2peak",
        
        // 信噪比阈值，低于此值的向量将被标记为无效
        // 建议值：1.0到1.3之间，0表示不进行信噪比过滤
        "sig2noise_threshold": 0,
        
        // 是否替换无效向量
        // true：使用邻近有效向量替换无效向量；false：保留无效向量为NaN
        "replace_vectors": true,
        
        // 标准差阈值，用于全局异常值检测
        // 建议值：5，0表示不进行标准差过滤
        "std_threshold": 5,
        
        // 中值滤波阈值，用于局部异常值检测
        // 建议值：1.5，0表示不进行中值滤波
        "median_threshold": 1.5,
        
        // 滤波方法，用于平滑速度场
        // 选项：'gaussian'（高斯滤波）或其他方法
        "filter_method": "gaussian",
        
        // 滤波核大小，影响平滑程度
        // 建议值：1-3，数值越大平滑效果越强
        "filter_kernel_size": 1,
        
        // U方向速度范围限制（像素/帧）
        // 超出此范围的向量将被标记为无效
        "min_max_u": [-1000, 1000],
        
        // V方向速度范围限制（像素/帧）
        // 超出此范围的向量将被标记为无效
        "min_max_v": [-1000, 1000],
        
        // 是否启用图像预处理
        // true：在PIV分析前对图像进行预处理；false：直接使用原始图像
        "enable_preprocessing": false,
        
        // 预处理方法
        // 选项：'contrast_stretch'（对比度拉伸）、'high_pass'（高通滤波）、'dynamic_masking'（动态掩码）等
        // null表示不进行预处理
        "preprocessing_method": null
    }
} 