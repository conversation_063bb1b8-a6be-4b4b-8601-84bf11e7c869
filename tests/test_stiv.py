"""
STIV单元测试

专注于测试STIV算法的基础功能模块，确保程序功能正常运行。
不涉及算法性能评估或结果质量验证。

测试覆盖：
- 模块初始化
- 配置解析和验证
- 坐标转换计算
- 时间管理功能
- 分析线生成
- YAML配置支持
"""

import unittest
import json
import yaml
import tempfile
import numpy as np
from pathlib import Path
import sys
import os

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from analysis_algorithms.stiv import (
    CoordinateSystem, TimeManager, AnalysisLines, STIVProcessor
)
from utils.config_manager import ConfigManager


class TestCoordinateSystem(unittest.TestCase):
    """测试坐标系统模块"""
    
    def test_pixel_distance_system(self):
        """测试像素+距离坐标系统"""
        calibration_data = {
            "pixels": [[0, 0], [100, 0], [0, 100]],
            "distances": {
                "(0,1)": 10.0,  # 100像素 = 10米
                "(0,2)": 10.0   # 100像素 = 10米
            }
        }
        
        coord_sys = CoordinateSystem("pixel_distance", calibration_data)
        ppm = coord_sys.calculate_ppm()
        
        # 期望ppm = 100像素 / 10米 = 10像素/米
        self.assertAlmostEqual(ppm, 10.0, places=2)
        
        # 测试距离转换
        self.assertAlmostEqual(coord_sys.pixel_to_meters(100), 10.0, places=2)
        self.assertAlmostEqual(coord_sys.meters_to_pixels(10.0), 100.0, places=2)
    
    def test_geographic_system(self):
        """测试经纬度坐标系统"""
        calibration_data = {
            "coordinates": [
                {"pixel": [0, 0], "lat": 29.858377, "lon": 121.746625},
                {"pixel": [100, 0], "lat": 29.858377, "lon": 121.747625}  # 约100米距离
            ]
        }
        
        coord_sys = CoordinateSystem("geographic", calibration_data)
        ppm = coord_sys.calculate_ppm()
        
        # ppm应该大约是1像素/米（100像素对应约100米）
        self.assertGreater(ppm, 0.5)
        self.assertLess(ppm, 2.0)


class TestTimeManager(unittest.TestCase):
    """测试时间管理模块"""
    
    def test_time_manager_creation(self):
        """测试时间管理器创建"""
        time_mgr = TimeManager(10.0, 30.0)
        
        self.assertEqual(time_mgr.start_time, 10.0)
        self.assertEqual(time_mgr.end_time, 30.0)
        
        time_info = time_mgr.get_time_info()
        self.assertTrue(time_info["has_time_restriction"])
    
    def test_time_manager_no_restriction(self):
        """测试无时间限制的时间管理器"""
        time_mgr = TimeManager()
        
        time_info = time_mgr.get_time_info()
        self.assertFalse(time_info["has_time_restriction"])


class TestAnalysisLines(unittest.TestCase):
    """测试分析线模块"""
    
    def test_adaptive_lines_default_mode(self):
        """测试自适应分析线（默认模式）"""
        config = {
            "flow_direction": 45.0,  # 45度流向
            "line_count": 3,
            "line_spacing": 50,
            "line_length": 200,
            "drawing_mode": "default"
        }

        lines_mgr = AnalysisLines("adaptive", config)
        lines = lines_mgr.generate_lines((480, 640))  # 标准视频尺寸

        self.assertEqual(len(lines), 3)

        # 检查线的角度（应该平行于流向，即45度）
        for line in lines:
            angle_deg = np.degrees(line.angle)
            # 角度应该在合理范围内
            self.assertGreater(angle_deg, -180)
            self.assertLess(angle_deg, 180)

    def test_adaptive_lines_infinite_mode(self):
        """测试自适应分析线（无限延伸模式）"""
        config = {
            "flow_direction": 0.0,  # 水平流向
            "line_count": 2,
            "line_spacing": 100,
            "line_length": 200,
            "drawing_mode": "infinite_with_roi"
        }

        # 定义一个矩形ROI
        roi = [[100, 100], [300, 500]]  # [[y1, x1], [y2, x2]]

        lines_mgr = AnalysisLines("adaptive", config)
        lines = lines_mgr.generate_lines((480, 640), roi)

        self.assertEqual(len(lines), 2)

        # 检查线段是否被ROI裁剪
        for line in lines:
            # 起点和终点应该在ROI范围内或边界上
            start_x, start_y = line.start_point
            end_x, end_y = line.end_point

            # 检查Y坐标范围
            self.assertGreaterEqual(start_y, 100)
            self.assertLessEqual(start_y, 300)
            self.assertGreaterEqual(end_y, 100)
            self.assertLessEqual(end_y, 300)

            # 检查X坐标范围
            self.assertGreaterEqual(start_x, 100)
            self.assertLessEqual(start_x, 500)
            self.assertGreaterEqual(end_x, 100)
            self.assertLessEqual(end_x, 500)

    def test_adaptive_lines_smart_segmentation(self):
        """测试自适应分析线（智能分割模式）"""
        config = {
            "flow_direction": 0.0,  # 水平流向
            "line_count": 1,
            "line_spacing": 50,
            "line_length": 600,  # 长线段，会被分割
            "drawing_mode": "smart_segmentation",
            "optimal_length": 200,  # 最佳长度
            "interval_distance": 50  # 间隔距离
        }

        lines_mgr = AnalysisLines("adaptive", config)
        lines = lines_mgr.generate_lines((480, 640))

        # 应该生成多条分割后的线段
        self.assertGreater(len(lines), 1)

        # 检查每条线段的长度应该接近optimal_length
        for line in lines:
            self.assertLessEqual(line.length, config["optimal_length"] * 1.2)  # 允许20%的误差

    def test_drawing_mode_validation(self):
        """测试绘制模式配置验证"""
        # 测试无效的绘制模式
        config = {
            "flow_direction": 0.0,
            "line_count": 1,
            "line_spacing": 50,
            "line_length": 200,
            "drawing_mode": "invalid_mode"
        }

        with self.assertRaises(ValueError):
            AnalysisLines("adaptive", config)

        # 测试智能分割模式的参数验证
        config = {
            "flow_direction": 0.0,
            "line_count": 1,
            "line_spacing": 50,
            "line_length": 200,
            "drawing_mode": "smart_segmentation",
            "optimal_length": 0  # 无效值
        }

        with self.assertRaises(ValueError):
            AnalysisLines("adaptive", config)

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 不指定drawing_mode，应该默认使用default模式
        config = {
            "flow_direction": 45.0,
            "line_count": 3,
            "line_spacing": 50,
            "line_length": 200
            # 没有drawing_mode参数
        }

        lines_mgr = AnalysisLines("adaptive", config)
        lines = lines_mgr.generate_lines((480, 640))

        self.assertEqual(len(lines), 3)
        # 应该使用默认模式，生成固定数量的线
    
    def test_manual_lines(self):
        """测试手动分析线"""
        config = {
            "lines": [100, 200, 300],
            "lines_range": [[50, 550], [50, 550], [50, 550]]
        }
        
        lines_mgr = AnalysisLines("manual", config)
        lines = lines_mgr.generate_lines((480, 640))
        
        self.assertEqual(len(lines), 3)
        
        # 检查手动线的位置
        for i, line in enumerate(lines):
            self.assertEqual(line.start_point[1], config["lines"][i])  # Y坐标
            self.assertEqual(line.angle, 0.0)  # 水平线


class TestSTIVConfig(unittest.TestCase):
    """测试STIV配置模块"""

    def test_v2_config_creation(self):
        """测试v2配置创建"""
        config = STIVConfig()

        # 检查默认值
        self.assertEqual(config.coordinate_system.type, "pixel_distance")
        self.assertEqual(config.analysis_lines.mode, "adaptive")
        self.assertEqual(config.stiv.polar_filter_width, 10)

    def test_legacy_config_conversion(self):
        """测试旧版配置转换"""
        legacy_config = {
            "dataset": {
                "video_fp": "test.mp4",
                "gcp": {
                    "apply": True,
                    "pixels": [[0, 0], [100, 0]],
                    "distances": {"(0,1)": 10.0}
                }
            },
            "lines": [100, 200],
            "stiv": {
                "lines_range": [[50, 550], [50, 550]],
                "polar_filter_width": 15
            },
            "preprocessing": {
                "ppm": 5.0
            }
        }

        config = STIVConfig.from_dict(legacy_config)

        # 检查转换结果
        self.assertEqual(config.dataset.video_fp, "test.mp4")
        self.assertEqual(config.coordinate_system.type, "pixel_distance")
        self.assertEqual(config.analysis_lines.mode, "manual")
        self.assertEqual(config.stiv.polar_filter_width, 15)

    def test_yaml_config_support(self):
        """测试YAML配置支持"""
        yaml_config = {
            "stiv_params": {
                "coordinate_system": {
                    "type": "pixel_distance",
                    "calibration": {
                        "pixels": [[0, 0], [100, 0]],
                        "distances": {"(0,1)": 10.0}
                    }
                },
                "analysis_lines": {
                    "mode": "adaptive",
                    "flow_direction": 45.0,
                    "line_count": 3
                },
                "algorithm": {
                    "polar_filter_width": 12
                },
                "preprocessing": {
                    "resolution": 0.8
                }
            }
        }

        config = STIVConfig.from_yaml_config(yaml_config)

        # 检查YAML配置转换
        self.assertEqual(config.coordinate_system.type, "pixel_distance")
        self.assertEqual(config.analysis_lines.mode, "adaptive")
        self.assertEqual(config.analysis_lines.flow_direction, 45.0)
        self.assertEqual(config.stiv.polar_filter_width, 12)
        self.assertEqual(config.preprocessing.resolution, 0.8)

    def test_config_validation(self):
        """测试配置验证"""
        config = STIVConfig()
        config.dataset.video_fp = "nonexistent.mp4"

        is_valid, errors = config.validate()
        self.assertFalse(is_valid)
        self.assertTrue(any("不存在" in error for error in errors))


class TestConfigUtils(unittest.TestCase):
    """测试配置工具函数"""
    
    def test_legacy_to_v2_conversion(self):
        """测试旧版到v2格式转换"""
        legacy_config = {
            "dataset": {"video_fp": "test.mp4"},
            "preprocessing": {"ppm": 5.0},
            "lines": [100, 200],
            "stiv": {"lines_range": [[50, 550], [50, 550]]}
        }
        
        v2_config = convert_legacy_config_to_v2(legacy_config)
        
        self.assertEqual(v2_config["config_version"], "2.0")
        self.assertEqual(v2_config["dataset"]["video_fp"], "test.mp4")
        self.assertEqual(v2_config["analysis_lines"]["mode"], "manual")
    
    def test_v2_config_validation(self):
        """测试v2配置验证"""
        valid_config = create_example_v2_config("test.mp4")
        is_valid, errors = validate_v2_config(valid_config)
        
        # 除了文件不存在的错误，其他应该都是有效的
        self.assertTrue(len(errors) <= 1)  # 只有文件不存在的错误
    
    def test_example_config_creation(self):
        """测试示例配置创建"""
        config = create_example_v2_config("test.mp4", "geographic")
        
        self.assertEqual(config["coordinate_system"]["type"], "geographic")
        self.assertIn("coordinates", config["coordinate_system"]["calibration"])


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_config_file_operations(self):
        """测试配置文件操作"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试JSON配置
            json_config_path = Path(temp_dir) / "test_config.json"
            config = create_example_v2_config("test.mp4")

            with open(json_config_path, 'w') as f:
                json.dump(config, f)

            loaded_config = STIVConfig.from_file(str(json_config_path))
            self.assertEqual(loaded_config.dataset.video_fp, "test.mp4")
            self.assertEqual(loaded_config.coordinate_system.type, "pixel_distance")

            # 测试YAML配置
            yaml_config_path = Path(temp_dir) / "test_config.yaml"
            yaml_config = {
                "stiv_params": {
                    "coordinate_system": {
                        "type": "geographic",
                        "calibration": {
                            "coordinates": [
                                {"pixel": [100, 100], "lat": 30.0, "lon": 120.0}
                            ]
                        }
                    },
                    "analysis_lines": {"mode": "adaptive", "line_count": 2},
                    "algorithm": {"polar_filter_width": 8},
                    "preprocessing": {"resolution": 1.0}
                }
            }

            with open(yaml_config_path, 'w') as f:
                yaml.dump(yaml_config, f)

            loaded_yaml_config = STIVConfig.from_file(str(yaml_config_path))
            self.assertEqual(loaded_yaml_config.coordinate_system.type, "geographic")
            self.assertEqual(loaded_yaml_config.analysis_lines.line_count, 2)


def create_test_video(output_path: str, duration: int = 5):
    """创建测试视频文件
    
    Args:
        output_path: 输出路径
        duration: 视频时长（秒）
    """
    try:
        import cv2
        
        # 创建测试视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, 30.0, (640, 480))
        
        for i in range(duration * 30):  # 30fps
            # 创建简单的测试帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            # 添加一些移动的模式
            x = (i * 2) % 640
            cv2.circle(frame, (x, 240), 20, (255, 255, 255), -1)
            out.write(frame)
        
        out.release()
        return True
    except ImportError:
        print("警告: 无法创建测试视频，需要安装opencv-python")
        return False


def run_tests():
    """运行所有测试"""
    # 创建测试目录
    test_dir = Path("tests/temp")
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # 尝试创建测试视频
    test_video_path = test_dir / "test_video.mp4"
    if not test_video_path.exists():
        print("创建测试视频...")
        if not create_test_video(str(test_video_path)):
            print("跳过需要视频文件的测试")
    
    # 运行测试
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_tests()
