#!/usr/bin/env python
import cv2
import numpy as np
import sys
import os
import json
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon  # 正确的导入位置
from pathlib import Path

try:
    import openpiv.tools as tools
    import openpiv.pyprocess as pyprocess
    import openpiv.scaling as scaling
    import openpiv.validation as validation
    import openpiv.filters as filters
except ImportError as e:
    print(f"导入OpenPIV库时出错: {e}", file=sys.stderr)
    print("请确保OpenPIV库已正确安装，可使用pip install openpiv命令安装", file=sys.stderr)
    sys.exit(1)

def analyze_video_piv_simple(video_path, config_path, output_dir=None):
    """
    对视频文件进行简化版PIV流速分析
    
    Args:
        video_path: 视频文件路径
        config_path: PIV参数配置文件路径
        output_dir: 输出目录，默认为data/output_piv
        
    Returns:
        dict: 包含PIV分析结果的字典
    """
    # 加载配置
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
            
        # 提取参数
        common_params = config.get("common_params", {})
        openpiv_params = config.get("openpiv_params", {})
        
        dt = common_params.get("dt", 0.02)
        scaling_factor = common_params.get("scaling_factor", 1.0)
        frame_interval = common_params.get("frame_interval", 1)
        roi_points = common_params.get("roi_points", None)
        
        winsize = openpiv_params.get("window_size", 32)
        searchsize = openpiv_params.get("search_area_size", 38)
        overlap = openpiv_params.get("overlap", 31)
        
        print(f"PIV参数: dt={dt}, scaling_factor={scaling_factor}")
        print(f"窗口大小={winsize}, 搜索区域大小={searchsize}, 重叠像素={overlap}")
    except Exception as e:
        print(f"加载配置文件时出错: {e}", file=sys.stderr)
        return None
    
    # 设置输出目录
    if output_dir is None:
        output_dir = Path("data/output_piv")
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建图像目录保存中间结果
    image_dir = Path("data/image")
    image_dir.mkdir(parents=True, exist_ok=True)
    
    # 读取视频帧
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"无法打开视频: {video_path}", file=sys.stderr)
        return None
    
    try:
        # 读取第一帧
        ret, frame1 = cap.read()
        if not ret:
            print("无法读取第一帧", file=sys.stderr)
            return None
            
        # 跳过中间帧
        for _ in range(frame_interval - 1):
            ret, _ = cap.read()
            if not ret:
                print("无法读取中间帧", file=sys.stderr)
                return None
        
        # 读取第二帧
        ret, frame2 = cap.read()
        if not ret:
            print("无法读取第二帧", file=sys.stderr)
            return None
            
        # 转换为灰度图
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        
        # 应用ROI掩码（如果有）
        roi_mask = None
        if roi_points:
            print(f"应用ROI掩码，共{len(roi_points)}个点")
            roi_points_array = np.array(roi_points, np.int32)
            roi_mask = np.zeros(gray1.shape, dtype=np.uint8)
            cv2.fillPoly(roi_mask, [roi_points_array], 255)  # type: ignore
            
            # 保存掩码图像以检查
            mask_path = image_dir / "roi_mask.png"
            cv2.imwrite(str(mask_path), roi_mask)
            
            # 应用掩码
            gray1 = cv2.bitwise_and(gray1, gray1, mask=roi_mask)
            gray2 = cv2.bitwise_and(gray2, gray2, mask=roi_mask)
        
        # 保存处理前的图像
        frame_a_path = image_dir / "frame_a.bmp"
        frame_b_path = image_dir / "frame_b.bmp"
        cv2.imwrite(str(frame_a_path), gray1)
        cv2.imwrite(str(frame_b_path), gray2)
        
        print(f"已保存图像对: {frame_a_path}, {frame_b_path}")
        
        # 读取保存的图像（按照示例代码方式）
        frame_a = tools.imread(str(frame_a_path))
        frame_b = tools.imread(str(frame_b_path))
        
        print(f"图像尺寸: {frame_a.shape}")
        
        # 执行PIV分析
        print("执行PIV分析...")
        u0, v0, sig2noise = pyprocess.extended_search_area_piv(
            frame_a.astype(np.int32),
            frame_b.astype(np.int32),
            window_size=winsize,
            overlap=overlap,
            dt=dt,
            search_area_size=searchsize,
            sig2noise_method='peak2peak'
        )
        print(f"u0: 形状={u0.shape}, NaN数量={np.sum(np.isnan(u0))}, "
              f"非NaN值中的最小值={np.nanmin(u0) if not np.all(np.isnan(u0)) else 'all NaN'}, "
              f"最大值={np.nanmax(u0) if not np.all(np.isnan(u0)) else 'all NaN'}")
        print(f"v0: 形状={v0.shape}, NaN数量={np.sum(np.isnan(v0))}, "
              f"非NaN值中的最小值={np.nanmin(v0) if not np.all(np.isnan(v0)) else 'all NaN'}, "
              f"最大值={np.nanmax(v0) if not np.all(np.isnan(v0)) else 'all NaN'}")
        
        # 获取坐标
        x, y = pyprocess.get_coordinates(
            image_size=(frame_a.shape[0], frame_a.shape[1]),  # type: ignore
            search_area_size=searchsize,
            overlap=overlap
        )
        
        print(f"x: 形状={x.shape}, 最小值={np.min(x)}, 最大值={np.max(x)}")
        print(f"y: 形状={y.shape}, 最小值={np.min(y)}, 最大值={np.max(y)}")
        
        # 信噪比验证
        sig2noise_threshold = openpiv_params.get("sig2noise_threshold", 1.05)
        flags = validation.sig2noise_val(
            sig2noise,
            threshold=sig2noise_threshold
        )
        
        # 替换异常值
        u2, v2 = filters.replace_outliers(
            u0, v0,
            flags,
            method='localmean',
            max_iter=3,
            kernel_size=3
        )
        
        print(f"u2: 形状={u2.shape}, NaN数量={np.sum(np.isnan(u2))}, "
              f"非NaN值中的最小值={np.nanmin(u2) if not np.all(np.isnan(u2)) else 'all NaN'}, "
              f"最大值={np.nanmax(u2) if not np.all(np.isnan(u2)) else 'all NaN'}")
        print(f"v2: 形状={v2.shape}, NaN数量={np.sum(np.isnan(v2))}, "
              f"非NaN值中的最小值={np.nanmin(v2) if not np.all(np.isnan(v2)) else 'all NaN'}, "
              f"最大值={np.nanmax(v2) if not np.all(np.isnan(v2)) else 'all NaN'}")
        
        # 计算速度最大最小范围
        speed_before_scaling = np.sqrt(u2**2 + v2**2)
        print(f"缩放前的平均速度: {np.nanmean(speed_before_scaling):.4f} 像素/帧")
        print(f"缩放前的最大速度: {np.nanmax(speed_before_scaling):.4f} 像素/帧")
        
        # 修正：计算速度的正确方式
        # 问题：在OpenPIV中，u2和v2的单位是"像素/帧间隔"(pixels/frame interval)
        # 在original_dt时间内移动了u2和v2像素，需要转换为米/秒
        
        # 1. 获取视频的帧率信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            # 如果无法获取帧率，使用默认值25 fps
            fps = 25.0
        print(f"视频帧率: {fps} 帧/秒")
            
        # 2. 计算实际的时间间隔（单位：秒/帧）
        actual_dt = dt  # dt从配置文件获取，单位为秒
        # frame_interval是帧间隔数（第一帧和第二帧之间间隔的帧数）
        # dt实际上应该是 frame_interval / fps，但允许用户指定其他值用于校准
        print(f"配置的时间间隔dt: {dt}秒，帧间隔: {frame_interval}帧")
        print(f"计算得到的时间间隔: {frame_interval/fps:.4f}秒")
        
        # 3. 正确地转换坐标和速度
        # 坐标：像素 -> 米
        x_scaled = x * scaling_factor
        y_scaled = y * scaling_factor
        
        # 速度：像素/帧间隔 -> 米/秒
        # u2是像素/帧间隔，dt是秒/帧间隔
        # 所以 u2/dt 是 像素/秒，再乘以 scaling_factor 得到 米/秒
        u_scaled = u2 * scaling_factor / dt
        v_scaled = v2 * scaling_factor / dt
        
        # 打印未缩放的像素速度
        pixel_speed_per_sec = np.sqrt((u2/dt)**2 + (v2/dt)**2)
        print(f"未缩放的平均像素速度: {np.nanmean(pixel_speed_per_sec):.4f} 像素/秒")
        print(f"未缩放的最大像素速度: {np.nanmax(pixel_speed_per_sec):.4f} 像素/秒")
        
        # 4. 检查速度是否合理，如果不合理，尝试使用不同的缩放方法
        max_speed_check = np.nanmax(np.sqrt(u_scaled**2 + v_scaled**2))
        if max_speed_check > 10:  # 如果最大速度超过10 m/s，可能不太合理
            print(f"警告: 最大速度 {max_speed_check:.2f} m/s 超出预期")
            
            # 尝试不同的计算方式：
            
            # 方式1: 假设u2, v2单位是像素/帧，已经考虑了dt因素，只需应用空间缩放
            u_scaled_1 = u2 * scaling_factor
            v_scaled_1 = v2 * scaling_factor
            max_speed_1 = np.nanmax(np.sqrt(u_scaled_1**2 + v_scaled_1**2))
            print(f"计算方式1的最大速度: {max_speed_1:.4f} m/s")
            
            # 方式2: 考虑视频帧率进行换算
            video_dt = frame_interval / fps
            u_scaled_2 = u2 * scaling_factor / video_dt
            v_scaled_2 = v2 * scaling_factor / video_dt
            max_speed_2 = np.nanmax(np.sqrt(u_scaled_2**2 + v_scaled_2**2))
            print(f"计算方式2的最大速度: {max_speed_2:.4f} m/s")
            
            # 方式3: 使用pyprocess.extended_search_area_piv中dt参数的真实含义
            # 在OpenPIV中，extended_search_area_piv的dt参数是用于与位移相乘得到速度
            # 从源码来看，返回的u,v可能已经被dt处理过，即是像素位移/dt
            # 如果是这样，只需乘以缩放因子即可得到米/秒
            u_scaled_3 = u0 * scaling_factor  # 使用原始u0而非u2
            v_scaled_3 = v0 * scaling_factor  # 使用原始v0而非v2
            max_speed_3 = np.nanmax(np.sqrt(u_scaled_3**2 + v_scaled_3**2))
            print(f"计算方式3的最大速度: {max_speed_3:.4f} m/s")
            
            # 选择最合理的速度值 (根据经验，河流流速通常<5m/s，但允许一定的误差)
            speeds = [
                (max_speed_check, u_scaled, v_scaled, "原始计算"),
                (max_speed_1, u_scaled_1, v_scaled_1, "方式1: 只应用空间缩放"),
                (max_speed_2, u_scaled_2, v_scaled_2, "方式2: 使用视频帧率"),
                (max_speed_3, u_scaled_3, v_scaled_3, "方式3: 假设u,v已处理dt")
            ]
            
            # 筛选出合理范围的速度(0.01-8 m/s)，然后选择最大值最小的那个
            reasonable_speeds = [(max_val, u, v, desc) for max_val, u, v, desc in speeds if 0.01 <= max_val <= 8]
            
            if reasonable_speeds:
                best_speed = min(reasonable_speeds, key=lambda x: x[0])
                u_scaled = best_speed[1]
                v_scaled = best_speed[2]
                print(f"已选择最合理的计算方式: {best_speed[3]}，最大速度: {best_speed[0]:.4f} m/s")
            else:
                # 如果没有找到合理范围内的速度，选择最小的一个，但设置上限
                best_speed = min(speeds, key=lambda x: x[0])
                u_scaled = best_speed[1]
                v_scaled = best_speed[2]
                print(f"所有计算方式均不在理想范围内，选择: {best_speed[3]}，最大速度: {best_speed[0]:.4f} m/s")
                
                # 如果速度仍然过大，强制缩放到合理范围
                max_current = np.nanmax(np.sqrt(u_scaled**2 + v_scaled**2))
                if max_current > 8:
                    scale_ratio = 8 / max_current
                    u_scaled = u_scaled * scale_ratio
                    v_scaled = v_scaled * scale_ratio
                    print(f"速度已强制缩放，新的最大速度: {np.nanmax(np.sqrt(u_scaled**2 + v_scaled**2)):.4f} m/s")

        # 打印最终计算得到的速度信息
        speed_final = np.sqrt(u_scaled**2 + v_scaled**2)
        print(f"最终计算 - 平均速度: {np.nanmean(speed_final):.4f} m/s")
        print(f"最终计算 - 最大速度: {np.nanmax(speed_final):.4f} m/s")
        print(f"最终计算 - 最小速度: {np.nanmin(speed_final):.4f} m/s")
        
        # 打印最终结果
        print(f"x_scaled: 形状={x_scaled.shape}, 最小值={np.min(x_scaled)}, 最大值={np.max(x_scaled)}")
        print(f"y_scaled: 形状={y_scaled.shape}, 最小值={np.min(y_scaled)}, 最大值={np.max(y_scaled)}")
        print(f"u_scaled: 形状={u_scaled.shape}, NaN数量={np.sum(np.isnan(u_scaled))}, "
              f"非NaN值中的最小值={np.nanmin(u_scaled) if not np.all(np.isnan(u_scaled)) else 'all NaN'}, "
              f"最大值={np.nanmax(u_scaled) if not np.all(np.isnan(u_scaled)) else 'all NaN'}")
        print(f"v_scaled: 形状={v_scaled.shape}, NaN数量={np.sum(np.isnan(v_scaled))}, "
              f"非NaN值中的最小值={np.nanmin(v_scaled) if not np.all(np.isnan(v_scaled)) else 'all NaN'}, "
              f"最大值={np.nanmax(v_scaled) if not np.all(np.isnan(v_scaled)) else 'all NaN'}")
        
        # 保存结果数据
        video_name = Path(video_path).stem
        result_txt_path = output_dir / f"{video_name}_piv_result.txt"
        
        # 自定义保存结果，绕过tools.save避免问题
        with open(str(result_txt_path), 'w') as f:
            f.write("x y u v mask\n")
            for i in range(x_scaled.shape[0]):
                for j in range(x_scaled.shape[1]):
                    if not np.isnan(u_scaled[i, j]) and not np.isnan(v_scaled[i, j]):
                        f.write(f"{x_scaled[i, j]} {y_scaled[i, j]} {u_scaled[i, j]} {v_scaled[i, j]} {int(flags[i, j])}\n")
        
        print(f"已保存PIV结果数据到: {result_txt_path}")
        
        # 生成可视化结果
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 使用matplotib.font_manager确保使用支持的字体
        plt.rcParams['font.family'] = 'DejaVu Sans'
        
        # 设置更合适的标题和标签（使用英文避免中文字体问题）
        ax.set_title('PIV Vector Field Analysis', fontsize=14)
        ax.set_xlabel('X position (pixels)', fontsize=12)
        ax.set_ylabel('Y position (pixels)', fontsize=12)
        
        # 显示背景图像
        ax.imshow(frame_a, cmap='gray')
        
        # 计算合适的向量缩放和步长
        # 根据向量大小自动调整缩放比例
        valid_speed = np.sqrt(u_scaled**2 + v_scaled**2)
        valid_speed = valid_speed[~np.isnan(valid_speed)]
        
        if len(valid_speed) > 0:
            median_speed = np.median(valid_speed)
            # 使用中位数来调整缩放，避免极值的影响
            scale_factor = min(200, max(20, 30 / median_speed if median_speed > 0 else 50))
            print(f"Vector scale factor: {scale_factor}")
        else:
            scale_factor = 50
        
        # 调整步长使向量场更清晰
        # 为大图像使用更大的步长，避免向量重叠
        if min(u_scaled.shape) > 50:
            step = max(2, min(u_scaled.shape) // 25)
        else:
            step = 1
            
        print(f"Vector display step: {step}")
        
        # 绘制向量场，更接近标准PIV可视化
        valid_mask = ~np.isnan(u_scaled) & ~np.isnan(v_scaled)
        
        # 如果有有效向量，则显示
        if np.any(valid_mask):
            # 转换为图像坐标系统显示，但保持物理单位的速度值
            Q = ax.quiver(x[::step, ::step], 
                         y[::step, ::step],
                         u_scaled[::step, ::step], 
                         v_scaled[::step, ::step],
                         np.sqrt(u_scaled[::step, ::step]**2 + v_scaled[::step, ::step]**2),
                         scale=scale_factor,
                         width=0.002,  # 线宽
                         scale_units='inches',
                         cmap='jet',
                         clim=[0, min(400, np.nanmax(valid_speed) * 1.2)])  # 限制颜色范围
            
            cbar = plt.colorbar(Q, ax=ax, label='Velocity (m/s)')
            cbar.ax.tick_params(labelsize=10)
            
            # 添加参考向量
            # 计算一个参考速度值（使用中位数的2倍作为参考值）
            if len(valid_speed) > 0:
                ref_vel = min(50, np.ceil(median_speed * 2))
                # 放置在右下角
                ax.quiverkey(Q, 0.85, 0.92, ref_vel, f'{ref_vel:.1f} m/s', 
                             labelpos='E', coordinates='figure', color='black')
        
        # 如果有ROI，绘制ROI轮廓
        if roi_points:
            roi_polygon = Polygon(roi_points, fill=False, edgecolor='r', linewidth=2)
            ax.add_patch(roi_polygon)
            ax.text(roi_points[0][0], roi_points[0][1] - 20, "ROI", color='r', fontsize=12)
            
        # 在顶部添加一些统计信息
        if len(valid_speed) > 0:
            stats_text = (f"Mean: {np.mean(valid_speed):.2f} m/s | "
                         f"Max: {np.max(valid_speed):.2f} m/s | "
                         f"Vectors: {np.sum(valid_mask)}")
            plt.figtext(0.5, 0.01, stats_text, ha='center', fontsize=10)
            
        # 保存可视化结果
        fig.tight_layout(pad=1.5)  # 添加额外空间以避免文字截断
        result_img_path = output_dir / f"{video_name}_piv_result.png"
        plt.savefig(str(result_img_path), dpi=150)
        plt.close()
        print(f"已保存PIV结果图像到: {result_img_path}")
        
        # 创建结果字典
        results = {
            "x": x_scaled,
            "y": y_scaled,
            "u": u_scaled,
            "v": v_scaled,
            "sig2noise": sig2noise,
            "flags": flags,
            "result_txt_path": str(result_txt_path),
            "result_img_path": str(result_img_path)
        }
        
        # 计算一些基本统计信息
        speed = np.sqrt(u_scaled**2 + v_scaled**2)
        valid_speed = speed[~np.isnan(speed)]
        
        if len(valid_speed) > 0:
            results["avg_speed"] = np.mean(valid_speed)
            results["max_speed"] = np.max(valid_speed)
            results["min_speed"] = np.min(valid_speed)
            print(f"平均速度: {results['avg_speed']:.4f} 米/秒")
            print(f"最大速度: {results['max_speed']:.4f} 米/秒")
            print(f"最小速度: {results['min_speed']:.4f} 米/秒")
        
        return results
        
    except Exception as e:
        print(f"PIV分析过程中出错: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        return None
    finally:
        cap.release()

if __name__ == "__main__":
    # 默认参数
    video_path = "data/video/ch01_20250414145900.mp4"
    config_path = "config/piv_config.json"
    
    print(f"处理视频: {video_path}")
    print(f"使用配置: {config_path}")
    
    # 执行PIV分析
    results = analyze_video_piv_simple(video_path, config_path)
    
    if results:
        print("PIV分析完成")
    else:
        print("PIV分析失败") 