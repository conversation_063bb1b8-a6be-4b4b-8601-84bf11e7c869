#!/usr/bin/env python
import cv2
import numpy as np
import sys
import os
import json
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.patches import Polygon  # 正确的导入位置

try:
    import openpiv.tools
    import openpiv.pyprocess
    import openpiv.scaling
    import openpiv.validation
    import openpiv.filters
    import openpiv.preprocess
except ImportError as e:
    print(f"导入OpenPIV库时出错: {e}", file=sys.stderr)
    print("请确保OpenPIV库已正确安装，可使用pip install openpiv命令安装", file=sys.stderr)
    openpiv = None

def process_with_openpiv(gray_frame1, gray_frame2, openpiv_params, common_params):
    """
    使用OpenPIV库处理图像对

    Args:
        gray_frame1 (np.ndarray): 第一帧的灰度图像
        gray_frame2 (np.ndarray): 第二帧的灰度图像
        openpiv_params (dict): OpenPIV库的配置参数
        common_params (dict): 通用参数，包含dt、scaling_factor等

    Returns:
        tuple: 包含 (results_piv, output_visualization)
               results_piv是处理结果（速度向量场）
               output_visualization是可视化图像（如果生成）
    """
    print("正在使用OpenPIV进行处理...")
    results_piv = None
    output_visualization = None

    # 检查OpenPIV库是否成功导入
    if openpiv is None:
        print("OpenPIV库未成功导入，跳过处理。", file=sys.stderr)
        return None, None

    dt = common_params.get("dt", 1.0)
    scaling_factor = common_params.get("scaling_factor", 1.0)
    frame_interval = common_params.get("frame_interval", 1)
    
    # 打印原始灰度图像信息
    print("\n==== 掩码应用前的原始灰度图像信息 ====")
    print(f"gray_frame1: 形状={gray_frame1.shape}, 非零像素数={np.count_nonzero(gray_frame1)}, 最小值={np.min(gray_frame1)}, 最大值={np.max(gray_frame1)}")
    print(f"gray_frame2: 形状={gray_frame2.shape}, 非零像素数={np.count_nonzero(gray_frame2)}, 最小值={np.min(gray_frame2)}, 最大值={np.max(gray_frame2)}")
    
    # 获取ROI掩码
    roi_points = common_params.get("roi_points", None)
    roi_mask = None
    masked_gray1 = None
    masked_gray2 = None
    
    # 如果提供了ROI点，创建掩码
    if roi_points:
        try:
            # 保存原始图像的副本
            orig_gray1 = gray_frame1.copy()
            orig_gray2 = gray_frame2.copy()
            
            # 转换ROI点到图像坐标系统
            roi_points_array = np.array(roi_points, np.int32)
            
            # 检查ROI点是否在图像范围内
            img_height, img_width = gray_frame1.shape
            valid_roi = True
            
            # 检查至少一个ROI点是否在图像内
            points_in_image = 0
            for pt in roi_points_array:
                if 0 <= pt[0] < img_width and 0 <= pt[1] < img_height:
                    points_in_image += 1
            
            if points_in_image == 0:
                print(f"警告: 所有ROI点都在图像范围外! 图像尺寸: {img_width}x{img_height}, ROI点: {roi_points}")
                valid_roi = False
            
            if valid_roi:
                # 创建与图像相同大小的掩码
                roi_mask = np.zeros(gray_frame1.shape, dtype=np.uint8)
                # 填充多边形区域
                cv2.fillPoly(roi_mask, [roi_points_array], 255)  # type: ignore
                
                # 打印掩码信息
                print("\n==== ROI掩码信息 ====")
                print(f"roi_mask: 形状={roi_mask.shape}, 非零像素数={np.count_nonzero(roi_mask)}, 区域占比={np.count_nonzero(roi_mask)/(roi_mask.shape[0]*roi_mask.shape[1])*100:.2f}%")
                print(f"roi_mask像素值: 最小值={np.min(roi_mask)}, 最大值={np.max(roi_mask)}")
                
                # 检查掩码是否有效（是否包含非零像素）
                if np.count_nonzero(roi_mask) == 0:
                    print(f"警告: 生成的ROI掩码全为零！ROI点可能不正确。将使用原始图像。")
                    valid_roi = False
                else:
                    # 应用掩码到图像上
                    masked_gray1 = cv2.bitwise_and(gray_frame1, gray_frame1, mask=roi_mask)
                    masked_gray2 = cv2.bitwise_and(gray_frame2, gray_frame2, mask=roi_mask)
                    
                    # 打印应用掩码后的图像信息
                    print("\n==== 掩码应用后的图像信息 ====")
                    print(f"masked_gray1: 形状={masked_gray1.shape}, 非零像素数={np.count_nonzero(masked_gray1)}, 最小值={np.min(masked_gray1)}, 最大值={np.max(masked_gray1)}")
                    print(f"masked_gray2: 形状={masked_gray2.shape}, 非零像素数={np.count_nonzero(masked_gray2)}, 最小值={np.min(masked_gray2)}, 最大值={np.max(masked_gray2)}")
                    
                    # 检查掩码后的图像是否有效（是否包含非零像素）
                    if np.count_nonzero(masked_gray1) == 0 or np.count_nonzero(masked_gray2) == 0:
                        print(f"警告: 应用掩码后的图像全为零！ROI掩码可能不适合当前图像。将使用原始图像。")
                        valid_roi = False
                    else:
                        # 使用有掩码的图像
                        gray_frame1 = masked_gray1
                        gray_frame2 = masked_gray2
                        print(f"已应用多边形ROI掩码，共 {len(roi_points)} 个点，有效掩码像素数: {np.count_nonzero(roi_mask)}")
            
            if not valid_roi:
                # 如果ROI无效，恢复使用原始图像
                gray_frame1 = orig_gray1
                gray_frame2 = orig_gray2
                print("由于ROI无效，将使用原始图像进行分析。")
            
            # 最终确认使用的图像数据情况
            print("\n==== 最终用于PIV分析的图像信息 ====")
            print(f"gray_frame1: 形状={gray_frame1.shape}, 非零像素数={np.count_nonzero(gray_frame1)}, 最小值={np.min(gray_frame1)}, 最大值={np.max(gray_frame1)}")
            print(f"gray_frame2: 形状={gray_frame2.shape}, 非零像素数={np.count_nonzero(gray_frame2)}, 最小值={np.min(gray_frame2)}, 最大值={np.max(gray_frame2)}")
                
        except Exception as e:
            print(f"应用ROI掩码时出错: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()
            # 如果出错，继续处理但不使用掩码

    try:
        # 获取OpenPIV分析参数
        window_size = openpiv_params.get("window_size", 32)
        overlap = openpiv_params.get("overlap", 16)
        search_area_size = openpiv_params.get("search_area_size", 64)
        correlation_method = openpiv_params.get("correlation_method", "circular")
        subpixel_method = openpiv_params.get("subpixel_method", "gaussian")
        sig2noise_method = openpiv_params.get("sig2noise_method", "peak2peak")
        sig2noise_threshold = openpiv_params.get("sig2noise_threshold", 1.2)
        replace_vectors = openpiv_params.get("replace_vectors", True)
        std_threshold = openpiv_params.get("std_threshold", 5)
        median_threshold = openpiv_params.get("median_threshold", 1.5)
        filter_method = openpiv_params.get("filter_method", "gaussian")
        filter_kernel_size = openpiv_params.get("filter_kernel_size", 1)
        min_max_u = openpiv_params.get("min_max_u", (-100, 100))
        min_max_v = openpiv_params.get("min_max_v", (-100, 100))
        
        print(f"OpenPIV参数: 窗口大小={window_size}, 重叠像素={overlap}, 搜索区域大小={search_area_size}")
        print(f"相关方法: {correlation_method}, 亚像素方法: {subpixel_method}")

        # 保存PIV处理前的图像
        try:
            # 创建保存目录
            image_dir = Path("data/image")
            image_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存即将用于PIV分析的图像
            piv_ready_img1_path = image_dir / "piv_ready_frame1.png"
            piv_ready_img2_path = image_dir / "piv_ready_frame2.png"
            cv2.imwrite(str(piv_ready_img1_path), gray_frame1)
            cv2.imwrite(str(piv_ready_img2_path), gray_frame2)
            print(f"已保存PIV处理前的图像: {piv_ready_img1_path}, {piv_ready_img2_path}")
            
            # 同时保存为BMP格式以便与示例代码一致
            piv_ready_img1_bmp_path = image_dir / "piv_ready_frame1.bmp"
            piv_ready_img2_bmp_path = image_dir / "piv_ready_frame2.bmp"
            cv2.imwrite(str(piv_ready_img1_bmp_path), gray_frame1)
            cv2.imwrite(str(piv_ready_img2_bmp_path), gray_frame2)
            print(f"已保存PIV处理前的BMP图像: {piv_ready_img1_bmp_path}, {piv_ready_img2_bmp_path}")
            
            # 使用matplotlib可视化并保存图像
            fig, ax = plt.subplots(1, 2, figsize=(12, 6))
            ax[0].imshow(gray_frame1, cmap='gray')
            ax[0].set_title('Frame 1')
            ax[1].imshow(gray_frame2, cmap='gray')
            ax[1].set_title('Frame 2')
            plt.tight_layout()
            
            plt_path = image_dir / "piv_frames_visualization.png"
            plt.savefig(str(plt_path))
            plt.close()
            print(f"已保存图像对比可视化: {plt_path}")
            
        except Exception as e:
            print(f"保存PIV处理前图像时出错: {e}", file=sys.stderr)
        
        # 数据预处理（可选）
        if openpiv_params.get("enable_preprocessing", False):
            preprocessing_method = openpiv_params.get("preprocessing_method", None)
            if preprocessing_method == "contrast_stretch":
                gray_frame1 = openpiv.preprocess.contrast_stretch(gray_frame1)
                gray_frame2 = openpiv.preprocess.contrast_stretch(gray_frame2)
            elif preprocessing_method == "high_pass":
                sigma = openpiv_params.get("high_pass_sigma", 5)
                gray_frame1 = openpiv.preprocess.high_pass(gray_frame1, sigma=sigma)
                gray_frame2 = openpiv.preprocess.high_pass(gray_frame2, sigma=sigma)
            elif preprocessing_method == "dynamic_masking":
                filter_size = openpiv_params.get("dynamic_mask_filter_size", 7)
                threshold = openpiv_params.get("dynamic_mask_threshold", 0.005)
                masking_method = openpiv_params.get("dynamic_mask_method", "edges")
                gray_frame1 = openpiv.preprocess.dynamic_masking(gray_frame1, method=masking_method, 
                                                              filter_size=filter_size, threshold=threshold)
                gray_frame2 = openpiv.preprocess.dynamic_masking(gray_frame2, method=masking_method,
                                                              filter_size=filter_size, threshold=threshold)

        # 转换图像类型为int32,与示例代码一致
        gray_frame1_int32 = gray_frame1.astype(np.int32)  # type: ignore
        gray_frame2_int32 = gray_frame2.astype(np.int32)  # type: ignore
        
        print(f"\n==== 转换为int32后的图像信息 ====")
        print(f"gray_frame1_int32: 类型={gray_frame1_int32.dtype}, 非零像素数={np.count_nonzero(gray_frame1_int32)}, 最小值={np.min(gray_frame1_int32)}, 最大值={np.max(gray_frame1_int32)}")
        print(f"gray_frame2_int32: 类型={gray_frame2_int32.dtype}, 非零像素数={np.count_nonzero(gray_frame2_int32)}, 最小值={np.min(gray_frame2_int32)}, 最大值={np.max(gray_frame2_int32)}")

        # 进行PIV分析 - 使用与示例代码相同的方式
        print("\n==== 执行openpiv.pyprocess.extended_search_area_piv ====")
        print(f"参数: window_size={window_size}, overlap={overlap}, search_area_size={search_area_size}, dt={dt}")
        
        # 1. 使用extended_search_area_piv进行PIV分析
        u, v, sig2noise = openpiv.pyprocess.extended_search_area_piv(
            gray_frame1_int32, gray_frame2_int32,
            window_size=window_size,
            overlap=overlap,
            search_area_size=search_area_size,
            dt=dt, # * frame_interval,
            # correlation_method=correlation_method,
            # subpixel_method=subpixel_method,
            sig2noise_method=sig2noise_method
        )
        
        # 打印PIV分析结果的基本信息
        print("\n==== PIV分析原始结果 ====")
        print(f"u: 形状={u.shape}, 类型={u.dtype}, NaN数量={np.sum(np.isnan(u))}, 非NaN值中的最小值={np.nanmin(u) if not np.all(np.isnan(u)) else 'all NaN'}, 最大值={np.nanmax(u) if not np.all(np.isnan(u)) else 'all NaN'}")
        print(f"v: 形状={v.shape}, 类型={v.dtype}, NaN数量={np.sum(np.isnan(v))}, 非NaN值中的最小值={np.nanmin(v) if not np.all(np.isnan(v)) else 'all NaN'}, 最大值={np.nanmax(v) if not np.all(np.isnan(v)) else 'all NaN'}")
        print(f"sig2noise: 形状={sig2noise.shape}, 类型={sig2noise.dtype}, 最小值={np.min(sig2noise)}, 最大值={np.max(sig2noise)}")
        
        # 可视化原始向量场并保存
        try:
            x, y = openpiv.pyprocess.get_coordinates(
                image_size=(gray_frame1.shape[0], gray_frame1.shape[1]),  # type: ignore
                search_area_size=search_area_size,
                overlap=overlap
            )
            
            # 创建原始向量场图像
            fig, ax = plt.subplots(figsize=(10, 8))
            ax.imshow(gray_frame1, cmap='gray')
            
            # 计算掩盖NaN的掩码
            valid_mask = ~np.isnan(u) & ~np.isnan(v)
            u_valid = np.copy(u)
            v_valid = np.copy(v)
            
            if np.any(valid_mask):
                # 只绘制有效向量
                ax.quiver(x[valid_mask], y[valid_mask], u_valid[valid_mask], v_valid[valid_mask], color='r')
                ax.set_title('原始PIV向量场（红色箭头）')
                
                raw_vec_path = Path("data/image") / "raw_vector_field.png"
                plt.savefig(str(raw_vec_path))
                plt.close()
                print(f"已保存原始向量场图像: {raw_vec_path}")
            else:
                print("所有向量都是NaN，无法生成原始向量场图像")
                plt.close()
        except Exception as e:
            print(f"保存原始向量场图像时出错: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()

        # 创建一个初始掩码数组，所有位置都是False（表示无掩码）
        mask = np.zeros_like(u, dtype=bool)
        
        # 2. 使用信噪比过滤掉错误向量
        if sig2noise_threshold > 0:
            sig2noise_mask = openpiv.validation.sig2noise_val(sig2noise, threshold=sig2noise_threshold)
            mask = np.logical_or(mask, sig2noise_mask)
            u[sig2noise_mask] = np.nan
            v[sig2noise_mask] = np.nan

        # 3. 使用标准差过滤掉异常值
        if std_threshold > 0:
            std_mask = openpiv.validation.global_std(u, v, std_threshold)
            mask = np.logical_or(mask, std_mask)
            u[std_mask] = np.nan
            v[std_mask] = np.nan

        # 4. 使用全局范围过滤掉异常值
        val_mask = openpiv.validation.global_val(u, v, min_max_u, min_max_v)
        mask = np.logical_or(mask, val_mask)
        u[val_mask] = np.nan
        v[val_mask] = np.nan

        # 5. 使用局部中值过滤掉异常值
        # 注意：由于local_median_val需要MaskedArray，我们使用自己的实现或替代方法
        if median_threshold > 0:
            try:
                # 尝试将数组转换为掩码数组
                u_masked = np.ma.masked_invalid(u)
                v_masked = np.ma.masked_invalid(v)
                med_mask = openpiv.validation.local_median_val(u_masked, v_masked, median_threshold, median_threshold)
                mask = np.logical_or(mask, med_mask)
                u[med_mask] = np.nan
                v[med_mask] = np.nan
            except AttributeError:
                # 如果转换失败，用其他方法替代
                print("警告：local_median_val验证失败，跳过此步骤")
                # 这里可以实现一个简单的局部中值过滤算法作为替代
                pass

        # 6. 替换异常值
        if replace_vectors:
            u, v = openpiv.filters.replace_outliers(u, v, mask, method='localmean')
            
        # 7. 应用平滑滤波器
        if filter_method == "gaussian" and filter_kernel_size > 0:
            u, v = openpiv.filters.gaussian(u, v, filter_kernel_size)

        # 8. 应用缩放因子将像素单位转换为实际物理单位
        # 获取矢量场坐标
        x, y = openpiv.pyprocess.get_coordinates(
            image_size=(gray_frame1.shape[0], gray_frame1.shape[1]),  # type: ignore
            search_area_size=search_area_size,
            overlap=overlap
        )
        
        # 打印坐标信息
        print(f"\n==== 矢量场坐标信息 ====")
        print(f"x: 形状={x.shape}, 最小值={np.min(x)}, 最大值={np.max(x)}")
        print(f"y: 形状={y.shape}, 最小值={np.min(y)}, 最大值={np.max(y)}")
        
        # 应用缩放
        x, y, u, v = openpiv.scaling.uniform(x, y, u, v, scaling_factor=scaling_factor)
        
        # 打印缩放后的结果
        print(f"\n==== 缩放后的矢量场信息 ====")
        print(f"x: 形状={x.shape}, 最小值={np.min(x)}, 最大值={np.max(x)}")
        print(f"y: 形状={y.shape}, 最小值={np.min(y)}, 最大值={np.max(y)}")
        print(f"u: 形状={u.shape}, NaN数量={np.sum(np.isnan(u))}, 非NaN值中的最小值={np.nanmin(u) if not np.all(np.isnan(u)) else 'all NaN'}, 最大值={np.nanmax(u) if not np.all(np.isnan(u)) else 'all NaN'}")
        print(f"v: 形状={v.shape}, NaN数量={np.sum(np.isnan(v))}, 非NaN值中的最小值={np.nanmin(v) if not np.all(np.isnan(v)) else 'all NaN'}, 最大值={np.nanmax(v) if not np.all(np.isnan(v)) else 'all NaN'}")

        # 将结果整合到一个字典中
        results_piv = {
            "x": x,  # x坐标
            "y": y,  # y坐标
            "u": u,  # x方向速度
            "v": v,  # y方向速度
            "sig2noise": sig2noise  # 信噪比
        }

        # 尝试使用用户提供的代码方式直接处理图像
        try:
            print("\n==== 尝试使用参考代码方式直接处理图像 ====")
            frame_a = openpiv.tools.imread(str(Path("data/image") / "piv_ready_frame1.bmp"))
            frame_b = openpiv.tools.imread(str(Path("data/image") / "piv_ready_frame2.bmp"))
            
            print(f"frame_a: 形状={frame_a.shape}, 类型={frame_a.dtype}, 最小值={np.min(frame_a)}, 最大值={np.max(frame_a)}")
            print(f"frame_b: 形状={frame_b.shape}, 类型={frame_b.dtype}, 最小值={np.min(frame_b)}, 最大值={np.max(frame_b)}")
            
            u_ref, v_ref, sig2noise_ref = openpiv.pyprocess.extended_search_area_piv(
                frame_a.astype(np.int32),
                frame_b.astype(np.int32),
                window_size=window_size,
                overlap=overlap,
                dt=dt,
                search_area_size=search_area_size,
                sig2noise_method=sig2noise_method
            )
            
            print(f"参考代码结果 u_ref: 形状={u_ref.shape}, NaN数量={np.sum(np.isnan(u_ref))}, 非NaN值中的最小值={np.nanmin(u_ref) if not np.all(np.isnan(u_ref)) else 'all NaN'}, 最大值={np.nanmax(u_ref) if not np.all(np.isnan(u_ref)) else 'all NaN'}")
            print(f"参考代码结果 v_ref: 形状={v_ref.shape}, NaN数量={np.sum(np.isnan(v_ref))}, 非NaN值中的最小值={np.nanmin(v_ref) if not np.all(np.isnan(v_ref)) else 'all NaN'}, 最大值={np.nanmax(v_ref) if not np.all(np.isnan(v_ref)) else 'all NaN'}")
            
            # 创建参考代码向量场图像
            x_ref, y_ref = openpiv.pyprocess.get_coordinates(
                image_size=(frame_a.shape[0], frame_a.shape[1]),  # type: ignore
                search_area_size=search_area_size,
                overlap=overlap
            )
            
            fig, ax = plt.subplots(figsize=(10, 8))
            ax.imshow(frame_a, cmap='gray')
            
            # 计算掩盖NaN的掩码
            valid_mask_ref = ~np.isnan(u_ref) & ~np.isnan(v_ref)
            
            if np.any(valid_mask_ref):
                # 只绘制有效向量
                ax.quiver(x_ref[valid_mask_ref], y_ref[valid_mask_ref], u_ref[valid_mask_ref], v_ref[valid_mask_ref], color='g')
                ax.set_title('参考代码PIV向量场（绿色箭头）')
                
                ref_vec_path = Path("data/image") / "reference_vector_field.png"
                plt.savefig(str(ref_vec_path))
                plt.close()
                print(f"已保存参考代码向量场图像: {ref_vec_path}")
            else:
                print("参考代码所有向量都是NaN，无法生成向量场图像")
                plt.close()
                
        except Exception as e:
            print(f"尝试使用参考代码方式处理图像时出错: {e}")
            import traceback
            traceback.print_exc()

        # 创建可视化图像
        try:
            # 创建matplotlib图像
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # 绘制原始图像
            ax.imshow(gray_frame1, cmap='gray')
            
            # 计算速度大小用于颜色映射
            speed = np.sqrt(u**2 + v**2)
            
            # 计算掩盖NaN的掩码
            valid_mask = ~np.isnan(u) & ~np.isnan(v)
            
            if np.any(valid_mask):
                # 绘制速度矢量场
                # 使用步长以避免绘制过多箭头
                step = max(1, min(x.shape) // 25)
                Q = ax.quiver(x[::step, ::step], 
                              y[::step, ::step],
                              u[::step, ::step], 
                              v[::step, ::step],
                              speed[::step, ::step],
                              scale=20,
                              cmap='jet')
                
                plt.colorbar(Q, ax=ax, label='速度 (m/s)')
            else:
                print("警告: 所有向量都是NaN，无法绘制矢量场")
            
            # 如果有ROI点，绘制ROI轮廓
            if roi_points and roi_mask is not None and np.count_nonzero(roi_mask) > 0:
                roi_points_array = np.array(roi_points, np.int32)
                # 将多边形转换为适合matplotlib的格式
                roi_polygon = Polygon(roi_points_array, fill=False, edgecolor='r', linewidth=2)
                ax.add_patch(roi_polygon)
            
            ax.set_title('OpenPIV 速度场分析结果')
            ax.set_xlabel('x (m)')
            ax.set_ylabel('y (m)')
            
            # 保存到内存中 - 修复过时的tostring_rgb方法
            fig.tight_layout()
            fig.canvas.draw()
            
            # 使用兼容性更好的方法获取图像数据
            try:
                # 尝试新版本的方法
                from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
                canvas = FigureCanvas(fig)
                canvas.draw()
                buf = np.array(canvas.renderer.buffer_rgba())
                plot_image = cv2.cvtColor(buf, cv2.COLOR_RGBA2RGB)
            except Exception as e:
                print(f"使用新方法转换图像失败，尝试备用方法: {e}")
                # 备用方法1：使用buffer_rgba
                try:
                    buf = np.array(fig.canvas.buffer_rgba())  # type: ignore
                    plot_image = cv2.cvtColor(buf, cv2.COLOR_RGBA2RGB)
                except Exception as e2:
                    print(f"使用buffer_rgba转换失败，尝试最后的备用方法: {e2}")
                    # 备用方法2：直接保存图像再读取
                    import tempfile
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp:
                        temp_path = temp.name
                    plt.savefig(temp_path)
                    plot_image = cv2.imread(temp_path, cv2.IMREAD_COLOR)
                    try:
                        os.remove(temp_path)  # 清理临时文件
                    except:
                        pass
            
            # 转换为OpenCV格式（BGR）
            output_visualization = cv2.cvtColor(plot_image, cv2.COLOR_RGB2BGR)
            
            # 保存最终结果图像
            final_result_path = Path("data/image") / "final_piv_result.png"
            cv2.imwrite(str(final_result_path), output_visualization)
            print(f"已保存最终PIV结果图像: {final_result_path}")
            
            plt.close(fig)
            
        except Exception as viz_error:
            print(f"创建可视化输出时出错: {viz_error}", file=sys.stderr)
            import traceback
            traceback.print_exc()
            output_visualization = None

    except Exception as e:
        print(f"执行OpenPIV处理时发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        return None, None

    return results_piv, output_visualization

def analyze_video_piv(video_path, piv_config_path, show_results=False):
    """
    对视频文件进行PIV流速分析

    Args:
        video_path: 视频文件路径
        piv_config_path: PIV算法的配置文件路径
        show_results: 是否显示处理结果 (主要用于日志控制)

    Returns:
        dict: 流速分析结果
    """
    # --- 初始化 ---
    piv_config_dict = None

    try:
        # 1. 加载PIV配置文件
        try:
            with open(piv_config_path, 'r') as f:
                piv_config_dict = json.load(f)
            
            # 从配置中获取要使用的PIV库和通用参数
            selected_piv_library = piv_config_dict.get("piv_library")
            if not selected_piv_library:
                print("错误: PIV配置文件中未指定 'piv_library'", file=sys.stderr)
                return None
            
            common_params = piv_config_dict.get("common_params", {})
            roi_rect = common_params.get("roi_rect", None)
            roi_points = common_params.get("roi_points", None)
            frame_interval = common_params.get("frame_interval", 1)

            print(f"选择的PIV库: {selected_piv_library}")
            print(f"通用参数: {common_params}")
            print(f"ROI 矩形: {roi_rect}")
            print(f"ROI 多边形: {roi_points}")
            print(f"帧间隔: {frame_interval}")

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"加载PIV配置文件 {piv_config_path} 时出错: {e}", file=sys.stderr)
            return None
        except Exception as e:
            print(f"读取PIV配置文件 {piv_config_path} 时发生意外错误: {e}", file=sys.stderr)
            return None

        if piv_config_dict is None:
            raise ValueError("无法加载PIV配置文件")

    except (FileNotFoundError, json.JSONDecodeError, ValueError) as e:
        print(f"加载或处理PIV配置时出错: {e}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"处理PIV配置时发生意外错误: {e}", file=sys.stderr)
        return None

    # --- 加载视频帧 ---
    video_file_path_str = str(Path(video_path).resolve())
    cap = None
    try:
        cap = cv2.VideoCapture(video_file_path_str)
        if not cap.isOpened():
            print(f"错误: 无法打开视频文件: {video_file_path_str}", file=sys.stderr)
            return None

        # 读取第一帧
        frame_idx = 0
        ret, frame1 = cap.read()
        frame_idx += 1
        if not ret:
            print("错误: 无法读取第一帧或视频为空。", file=sys.stderr)
            return None

        # 如果指定了矩形ROI，则裁剪第一帧
        if roi_rect and not roi_points:  # 如果有多边形ROI，优先使用多边形ROI
            x, y, w, h = roi_rect
            # 确保ROI矩形有效
            if x >= 0 and y >= 0 and w > 0 and h > 0 and (x + w) <= frame1.shape[1] and (y + h) <= frame1.shape[0]:
                frame1 = frame1[y:y+h, x:x+w]
            else:
                print(f"警告: common_params 中的 roi_rect {roi_rect} 无效，将使用整个帧。", file=sys.stderr)
                roi_rect = None

        # 跳过中间帧以实现 frame_interval 间隔
        for _ in range(frame_interval - 1):
            ret, _ = cap.read()
            frame_idx += 1
            if not ret:
                print(f"警告: 视频在读取第 {frame_idx} 帧时结束，不足以提供下一帧进行PIV分析。", file=sys.stderr)
                cap.release()
                return None

        # 读取第二帧
        ret, frame2 = cap.read()
        frame_idx += 1
        if not ret:
            print("错误: 无法读取第二帧或视频结束。", file=sys.stderr)
            cap.release()
            return None

        # 如果指定了ROI矩形，则裁剪第二帧
        if roi_rect and not roi_points:  # 如果有多边形ROI，优先使用多边形ROI
            x, y, w, h = roi_rect
            frame2 = frame2[y:y+h, x:x+w]

        # 转换为灰度图
        gray_frame1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
        gray_frame2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        
        # 保存灰度图用于检查
        try:
            # 创建保存目录
            image_dir = Path("data/image")
            image_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            video_stem = Path(video_path).stem
            image1_path = image_dir / f"{video_stem}_frame1_gray.png"
            image2_path = image_dir / f"{video_stem}_frame2_gray.png"
            
            # 保存原始灰度图
            cv2.imwrite(str(image1_path), gray_frame1)
            cv2.imwrite(str(image2_path), gray_frame2)
            print(f"已保存灰度图1: {image1_path}")
            print(f"已保存灰度图2: {image2_path}")
            
            # 如果有ROI点，创建并保存带掩码的灰度图
            if roi_points:
                # 创建掩码
                roi_points_array = np.array(roi_points, np.int32)
                roi_mask = np.zeros(gray_frame1.shape, dtype=np.uint8)
                cv2.fillPoly(roi_mask, [roi_points_array], 255)  # type: ignore
                
                # 保存掩码图像
                mask_image_path = image_dir / f"{video_stem}_roi_mask.png"
                cv2.imwrite(str(mask_image_path), roi_mask)
                print(f"已保存ROI掩码: {mask_image_path}")
                print(f"掩码中非零像素数量: {np.count_nonzero(roi_mask)}, 掩码尺寸: {roi_mask.shape}")
                
                # 应用掩码
                masked_gray1 = cv2.bitwise_and(gray_frame1, gray_frame1, mask=roi_mask)
                masked_gray2 = cv2.bitwise_and(gray_frame2, gray_frame2, mask=roi_mask)
                
                # 保存带掩码的灰度图
                masked_image1_path = image_dir / f"{video_stem}_frame1_masked.png"
                masked_image2_path = image_dir / f"{video_stem}_frame2_masked.png"
                cv2.imwrite(str(masked_image1_path), masked_gray1)
                cv2.imwrite(str(masked_image2_path), masked_gray2)
                print(f"已保存带掩码灰度图1: {masked_image1_path}")
                print(f"已保存带掩码灰度图2: {masked_image2_path}")
                print(f"带掩码图1中非零像素数量: {np.count_nonzero(masked_gray1)}")
                print(f"带掩码图2中非零像素数量: {np.count_nonzero(masked_gray2)}")
                
                # 绘制带有ROI轮廓的彩色图像
                color_with_roi1 = frame1.copy()
                color_with_roi2 = frame2.copy()
                cv2.polylines(color_with_roi1, [roi_points_array], True, (0, 0, 255), 2)
                cv2.polylines(color_with_roi2, [roi_points_array], True, (0, 0, 255), 2)
                
                roi_image1_path = image_dir / f"{video_stem}_frame1_with_roi.png"
                roi_image2_path = image_dir / f"{video_stem}_frame2_with_roi.png"
                cv2.imwrite(str(roi_image1_path), color_with_roi1)
                cv2.imwrite(str(roi_image2_path), color_with_roi2)
                print(f"已保存带ROI轮廓的彩色图1: {roi_image1_path}")
                print(f"已保存带ROI轮廓的彩色图2: {roi_image2_path}")
                
        except Exception as e:
            print(f"保存灰度图时出错: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()

        # --- 执行PIV分析 ---
        if selected_piv_library == "OpenPIV":
            print("使用 OpenPIV 库进行处理...")
            openpiv_params = piv_config_dict.get("openpiv_params", {})
            # 调用OpenPIV适配器
            results_piv, output_visualization = process_with_openpiv(gray_frame1, gray_frame2, openpiv_params, common_params)
        else:
            print(f"错误: piv_library 设置为 '{selected_piv_library}'，但当前正在执行 OpenPIV 处理", file=sys.stderr)
            return None

        # --- 输出和可视化/保存结果 ---
        if results_piv is not None:
            print("\nPIV 分析结果:")
            # 打印原始结果类型
            print(f"  原始结果类型: {type(results_piv)}")
            
            # 如果结果是一个字典，可以提取一些基本统计信息
            if isinstance(results_piv, dict):
                u = results_piv.get('u')
                v = results_piv.get('v')
                
                if u is not None and v is not None:
                    # 计算速度大小
                    speed = np.sqrt(u**2 + v**2)
                    
                    # 计算平均速度、最大速度等
                    avg_speed = np.nanmean(speed)
                    max_speed = np.nanmax(speed)
                    min_speed = np.nanmin(speed)
                    
                    print(f"  平均速度: {avg_speed:.4f} m/s")
                    print(f"  最大速度: {max_speed:.4f} m/s")
                    print(f"  最小速度: {min_speed:.4f} m/s")
                    
                    # 计算速度方向分布
                    angles = np.arctan2(v, u) * 180 / np.pi
                    mean_angle = np.nanmean(angles)
                    
                    print(f"  平均方向: {mean_angle:.2f} 度")

        # 保存 PIV 结果图像 (如果生成了)
        if output_visualization is not None:
            try:
                output_dir = Path("data/output_piv")
                output_dir.mkdir(parents=True, exist_ok=True)
                video_stem = Path(video_path).stem
                output_image_path = output_dir / f"{video_stem}_OpenPIV_result.png"
                
                if isinstance(output_visualization, np.ndarray):
                    cv2.imwrite(str(output_image_path), output_visualization)
                    print(f"PIV 结果图像已保存至: {output_image_path}")
                else:
                    print(f"警告: OpenPIV 算法返回的可视化结果不是有效的图像格式，无法保存。", file=sys.stderr)

            except Exception as e:
                print(f"保存 PIV 图像时出错: {e}", file=sys.stderr)
        elif output_visualization is None:
            print(f"OpenPIV 算法运行但未返回可保存的图像。")

        return results_piv

    except Exception as e:
        print(f"执行PIV分析时发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 清理视频捕获对象
        if cap is not None:
            cap.release()

# 使用示例
if __name__ == "__main__":
    # 设置PIV配置文件路径
    piv_config_file = Path("config/piv_config.json").resolve()
    # 设置视频文件路径
    video_file = Path("data/video/ch01_20250414145900.mp4").resolve()

    if not piv_config_file.is_file():
        print(f"错误: PIV配置文件未找到: {piv_config_file}", file=sys.stderr)
        # 自动创建一个简单的示例配置文件
        print(f"正在创建示例PIV配置文件: {piv_config_file}...")
        try:
            piv_config_file.parent.mkdir(parents=True, exist_ok=True)
            default_piv_config_content = {
                "piv_library": "OpenPIV",
                "common_params": {
                    "dt": 0.01,               # 时间间隔（秒），与视频帧率和 frame_interval 相关
                    "scaling_factor": 0.0086,   # 像素到米的缩放因子，例如，1像素等于0.005米
                    "roi_rect": None,          # [x, y, width, height] 视频帧的裁剪区域，null表示不裁剪
                    "roi_points": [[550,150],[1000,150],[1250,250],[1250,500],[1100,500],[900,350],[800,350]],  # 多边形ROI点，优先级高于roi_rect
                    "frame_interval": 1        # 处理视频帧的时间间隔，例如 1 表示连续两帧，2 表示跳过一帧
                },
                "openpiv_params": {
                    "window_size": 32,              # 询问窗口大小
                    "overlap": 16,                  # 窗口重叠像素数
                    "search_area_size": 64,         # 搜索区域大小
                    "correlation_method": "circular", # 相关方法：circular或linear
                    "subpixel_method": "gaussian",   # 亚像素估计方法
                    "sig2noise_method": "peak2peak", # 信噪比计算方法
                    "sig2noise_threshold": 1.2,      # 信噪比阈值（低于此值的向量将被标记为无效）
                    "replace_vectors": True,         # 是否替换无效向量
                    "std_threshold": 5,             # 标准差阈值（用于异常值检测）
                    "median_threshold": 1.5,         # 中值滤波阈值
                    "filter_method": "gaussian",     # 过滤方法
                    "filter_kernel_size": 1,         # 过滤核大小
                    "min_max_u": [-100, 100],       # U方向速度范围限制
                    "min_max_v": [-100, 100],       # V方向速度范围限制
                    "enable_preprocessing": False,   # 是否启用预处理
                    "preprocessing_method": None     # 预处理方法（contrast_stretch, high_pass, dynamic_masking）
                }
            }
            
            with open(piv_config_file, 'w') as f:
                json.dump(default_piv_config_content, f, indent=4)
                
            print(f"已创建默认配置文件: {piv_config_file}")
        except Exception as e:
            print(f"创建配置文件时出错: {e}", file=sys.stderr)
            sys.exit(1)
    
    if not video_file.is_file():
        print(f"错误: 视频文件未找到: {video_file}", file=sys.stderr)
        print("请提供一个有效的视频文件路径进行测试。")
        sys.exit(1)
    
    # 执行PIV分析
    results = analyze_video_piv(video_file, piv_config_file, show_results=True)
    
    if results:
        print("PIV分析完成。")
    else:
        print("PIV分析失败或未返回结果。")
