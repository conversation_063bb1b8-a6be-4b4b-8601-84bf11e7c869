#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenCV Optical Track Velocimetry (OTV) Analyzer
此模块用于通过OpenCV的光流法分析视频片段并计算流速。
"""

import cv2
import numpy as np
import os
import time
import math
import csv

# 默认配置 (可以被外部配置覆盖)
DEFAULT_OTV_PARAMS = {
    'current_mode': 'real',
    'input_video_path': 'data/output/ch01_20250414145900_0140_0220.mp4',
    'check_feature_points': False,
    'expected_flow_direction': 240,
    'direction_threshold': 45,
    'pixel_to_meter': 0.0086,
    'min_feature_points_rediscover': 5,
    'roi_points': [[550,150],[1000,150],[1120,250],[1250,250],[1250,500],[1100,500],[900,350],[800,350]]
}

# 全局变量定义 (从DEFAULT_OTV_PARAMS中提取)
CURRENT_MODE = DEFAULT_OTV_PARAMS['current_mode']
INPUT_VIDEO_PATH = DEFAULT_OTV_PARAMS['input_video_path']
CHECK_FEATURE_POINTS = DEFAULT_OTV_PARAMS['check_feature_points']
EXPECTED_FLOW_DIRECTION = DEFAULT_OTV_PARAMS['expected_flow_direction']
DIRECTION_THRESHOLD = DEFAULT_OTV_PARAMS['direction_threshold']
PIXEL_TO_METER = DEFAULT_OTV_PARAMS['pixel_to_meter']

def calculate_angle(new_pt, old_pt):
    """计算特征点移动的角度，返回0-360度范围内的角度值"""
    dx = new_pt[0] - old_pt[0]
    dy = new_pt[1] - old_pt[1]
    angle = math.degrees(math.atan2(dy, dx))
    # 转换到0-360度范围
    if angle < 0:
        angle += 360
    return angle

def is_direction_valid(angle, expected_direction, threshold):
    """检查特征点移动方向是否在允许的范围内"""
    # 计算角度差，考虑角度的循环特性
    angle_diff = min(abs(angle - expected_direction), 360 - abs(angle - expected_direction))
    return angle_diff <= threshold

def sort_polygon_points_clockwise(points):
    """将多边形顶点按顺时针方向排序"""
    # 计算多边形的中心点
    center = np.mean(points, axis=0)
    
    # 计算每个点相对于中心点的角度
    def get_angle(point):
        return np.arctan2(point[1] - center[1], point[0] - center[0])
    
    # 按角度排序
    sorted_points = sorted(points, key=get_angle, reverse=True)
    return np.array(sorted_points, dtype=np.int32)

# 确保ROI_POINTS按顺时针排序
ROI_POINTS = sort_polygon_points_clockwise(DEFAULT_OTV_PARAMS['roi_points'])

def draw_grid(frame, grid_step=50, draw_direction=False):
    """在帧上绘制网格和坐标轴，以及可选的方向箭头和阈值范围"""
    h, w = frame.shape[:2]
    # 绘制垂直线和标签
    for x in range(0, w, grid_step):
        cv2.line(frame, (x, 0), (x, h), (200, 200, 200), 1)
        cv2.putText(frame, str(x), (x + 5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    # 绘制水平线和标签
    for y in range(0, h, grid_step):
        cv2.line(frame, (0, y), (w, y), (200, 200, 200), 1)
        cv2.putText(frame, str(y), (5, y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    
    # 绘制期望方向和阈值范围
    if draw_direction:
        # 在左上角绘制方向指示
        center_x, center_y = 100, 100  # 箭头中心点
        arrow_length = 50  # 箭头长度

        expected_flow_direction = DEFAULT_OTV_PARAMS['expected_flow_direction']
        direction_threshold = DEFAULT_OTV_PARAMS['direction_threshold']

        # 绘制期望方向箭头（红色）
        expected_rad = math.radians(expected_flow_direction)
        end_x = int(center_x + arrow_length * math.cos(expected_rad))
        end_y = int(center_y + arrow_length * math.sin(expected_rad))
        cv2.arrowedLine(frame, (center_x, center_y), (end_x, end_y), (0, 0, 255), 2, tipLength=0.3)
        
        # 绘制阈值角度范围（淡蓝色）
        min_angle = expected_flow_direction - direction_threshold
        max_angle = expected_flow_direction + direction_threshold
        
        # 绘制最小阈值角度线
        min_rad = math.radians(min_angle)
        min_end_x = int(center_x + arrow_length * math.cos(min_rad))
        min_end_y = int(center_y + arrow_length * math.sin(min_rad))
        cv2.line(frame, (center_x, center_y), (min_end_x, min_end_y), (255, 200, 0), 1)
        
        # 绘制最大阈值角度线
        max_rad = math.radians(max_angle)
        max_end_x = int(center_x + arrow_length * math.cos(max_rad))
        max_end_y = int(center_y + arrow_length * math.sin(max_rad))
        cv2.line(frame, (center_x, center_y), (max_end_x, max_end_y), (255, 200, 0), 1)
        
        # 绘制文字说明
        cv2.putText(frame, f"期望方向: {expected_flow_direction}°", (center_x - 90, center_y - 20), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        cv2.putText(frame, f"阈值: ±{direction_threshold}°", (center_x - 90, center_y + 70), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 200, 0), 1)
    
    return frame

def process_video_pre_mode(cap, out, total_frames):
    """处理视频的预处理模式，只绘制网格和ROI"""
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret: break
        
        output_frame = frame.copy()
        output_frame = draw_grid(output_frame, draw_direction=True)
        cv2.polylines(output_frame, [ROI_POINTS], isClosed=True, color=(255, 0, 0), thickness=2)
        
        out.write(output_frame)
        frame_count += 1
        
        # 统一打印进度
        if frame_count % 30 == 0:
            print(f"已处理 {frame_count}/{total_frames if total_frames > 0 else '?'} 帧 (Pre 模式)")
    
    return frame_count

def filter_by_quartile(data_list, values_key=None):
    """使用四分位法过滤数据，去除异常值
    
    Args:
        data_list: 要过滤的数据列表，可以是简单数值列表或字典列表
        values_key: 如果data_list是字典列表，指定要过滤的键名
        
    Returns:
        过滤后的数据列表
    """
    if not data_list:
        return []
    
    # 提取要过滤的值列表
    if values_key is not None:
        values = [item[values_key] for item in data_list]
    else:
        values = data_list
    
    # 计算四分位数
    q1 = np.percentile(values, 25)
    q3 = np.percentile(values, 75)
    iqr = q3 - q1
    
    # 设定上下限
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    
    # 过滤数据
    if values_key is not None:
        filtered_data = [item for item in data_list if lower_bound <= item[values_key] <= upper_bound]
    else:
        filtered_data = [item for item in values if lower_bound <= item <= upper_bound]
    
    # 打印过滤信息
    print(f"四分位过滤 - 原始数据数量: {len(data_list)}, 过滤后数量: {len(filtered_data)}")
    print(f"四分位范围 - Q1: {q1:.4f}, Q3: {q3:.4f}, IQR: {iqr:.4f}")
    print(f"过滤边界 - 下限: {lower_bound:.4f}, 上限: {upper_bound:.4f}")
    
    return filtered_data

def process_video_real_mode(cap, out, output_path_halfway, total_frames, check_feature_points=CHECK_FEATURE_POINTS):
    """处理视频的实际模式，进行光流计算"""
    # 初始化参数
    feature_params = dict(maxCorners=200, qualityLevel=0.3, minDistance=7)
    lk_params = dict(winSize=(15, 15), maxLevel=2, criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03))
    
    ret, prev_frame = cap.read()
    if not ret:
        print("错误: 无法读取第一帧")
        return 0
    
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到第一帧
    ret, prev_frame = cap.read()
    if not ret:
        print("错误: 无法读取第一帧")
        return 0
    
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    height, width = prev_frame.shape[:2]
    
    p0_data = []  # 存储带ID的特征点
    roi_mask = np.zeros_like(prev_gray)
    cv2.fillPoly(roi_mask, [ROI_POINTS], 255)  # type: ignore
    
    # 检测第一帧的特征点 (仅在 ROI 内)
    initial_tracked_points = cv2.goodFeaturesToTrack(prev_gray, mask=roi_mask, **feature_params)
    if initial_tracked_points is not None:
        for i, pt_coords in enumerate(initial_tracked_points):
            p0_data.append({'id': i, 'coords': pt_coords.reshape(1, 2).astype(np.float32)})
    
    if not p0_data:
        print("警告: 在指定的 ROI 中未找到初始特征点。")
        return 0
    
    # 用于视频输出的变量
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    fps = cap.get(cv2.CAP_PROP_FPS)
    halfway_video_writer = None
    halfway_video_generated = False
    mask = np.zeros_like(prev_frame)  # 用于绘制主视频轨迹的掩膜
    halfway_mask = np.zeros_like(prev_frame)  # 用于绘制中途视频轨迹的掩膜
    user_selected_ids_for_calculation = set()  # 用于存储用户选择的、仅用于计算的特征点ID
    
    flow_speeds = []  # 用于存储流速数据
    flow_speeds_window = []  # 使用滑动窗口存储最近的流速数据
    window_size = 20  # 滑动窗口大小，可以根据需要调整
    total_displacement_for_calc = 0  # 用于存储符合计算条件的总位移
    prev_time = time.time()  # 初始化 prev_time
    frame_count = 0
    avg_speed = 0
    pixel_to_meter = 0.0086  # 假设每像素代表1厘米就是0.01，根据实际情况调整    
    # 统计方向有效和无效的点数量
    direction_valid_count = 0
    direction_invalid_count = 0
    
    # 用于存储每帧数据的列表
    frame_data = []
    
    while True:
        ret, frame = cap.read()
        if not ret: break
        
        frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        output_frame = frame.copy()
        output_frame = draw_grid(output_frame, draw_direction=True)  # 添加方向绘制
        current_p0_data_for_drawing = list(p0_data)
        
        # --- 1. 中途视频帧生成 (视频前半部分) ---
        if check_feature_points and not halfway_video_generated and total_frames > 0 and frame_count < total_frames // 2:
            if halfway_video_writer is None:
                halfway_video_writer = cv2.VideoWriter(output_path_halfway, fourcc, fps, (width, height))
            
            if halfway_video_writer is not None:
                frame_for_halfway_video = frame.copy()
                frame_for_halfway_video = cv2.add(frame_for_halfway_video, halfway_mask)
                frame_for_halfway_video = draw_grid(frame_for_halfway_video, draw_direction=True)  # 添加方向绘制
                
                if current_p0_data_for_drawing:
                    for item in current_p0_data_for_drawing:
                        point_id = item['id']
                        x_f, y_f = item['coords'].ravel()
                        cv2.putText(frame_for_halfway_video, str(point_id), (int(x_f) + 7, int(y_f) - 7),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                        cv2.circle(frame_for_halfway_video, (int(x_f), int(y_f)), 5, (0, 0, 255), -1)
                
                halfway_video_writer.write(frame_for_halfway_video)
        
        # --- 2. 到达视频一半位置: 保存中途视频, 进行用户选择 ---
        if check_feature_points and not halfway_video_generated and total_frames > 0 and frame_count == total_frames // 2:
            if halfway_video_writer is not None:
                halfway_video_writer.release()
                halfway_video_writer = None
                print(f"已生成前半部分视频 (带特征点编号): {os.path.abspath(output_path_halfway)}")
            halfway_video_generated = True
            
            if p0_data:
                print("前半部分视频 (halfway_output_flow.mp4) 已保存。")
                print(f"请使用视频播放器打开 {os.path.abspath(output_path_halfway)} 查看特征点及其编号。")
                available_ids = sorted([item['id'] for item in p0_data])
                if available_ids:
                    print(f"当前帧 ({frame_count}/{total_frames if total_frames > 0 else '?'}) 的可用特征点ID为: {available_ids} (从 0 到 {max(available_ids) if available_ids else -1})。")
                    input_str = input(f"查看完毕后，请输入要用于后续流速计算的特征点ID (用逗号分隔，例如 0,1,2)，或直接回车不筛选: ")
                    if input_str.strip():
                        try:
                            selected_id_strings = [s.strip() for s in input_str.split(',')]
                            temp_chosen_ids = {int(s) for s in selected_id_strings if s.isdigit() and int(s) in available_ids}
                            
                            if len(temp_chosen_ids) != len(selected_id_strings):
                                print(f"警告: 部分输入ID无效或不在可用ID列表 {available_ids} 中。只使用有效ID。")
                            
                            if temp_chosen_ids:
                                user_selected_ids_for_calculation = temp_chosen_ids
                                print(f"已选择特征点ID: {sorted(list(user_selected_ids_for_calculation))} 用于后续流速计算。跟踪和可视化仍将显示所有点。")
                            else:
                                print("未选择任何有效特征点进行计算筛选，或输入格式不正确。将使用所有点进行计算。")
                        except ValueError:
                            print("输入格式错误。将使用所有点进行计算。")
                    else:
                        print("未输入选择，将使用所有点进行后续流速计算。")
                else:
                    print("虽然有p0_data，但未能提取可用ID。将继续跟踪并使用所有点进行计算。")
            else:
                print("处理到一半时无特征点可供选择。将继续跟踪后续自动检测的特征点。")
        
        # 如果不检查特征点，强制设置halfway_video_generated为True，避免生成中途视频
        if not check_feature_points and not halfway_video_generated:
            halfway_video_generated = True
        
        # --- 3. 'real' 模式下的常规光流处理 ---
        if p0_data:
            # 将当前帧的特征点坐标转换为 numpy 数组，并调整形状为 (N, 1, 2)，其中 N 是特征点数量
            current_coords_for_lk = np.array([item['coords'] for item in p0_data]).reshape(-1, 1, 2)
            
            # 记录当前时间，用于计算时间差 dt
            current_time = time.time()
            dt = current_time - prev_time
            prev_time = current_time
            
            # 使用光流法计算特征点在当前帧中的新位置
            p1, st, err = cv2.calcOpticalFlowPyrLK(prev_gray, frame_gray, current_coords_for_lk, None, **lk_params)  # type: ignore
            
            # 初始化变量，用于存储下一帧的特征点数据
            next_p0_data = []
            # 用于存储成功跟踪的特征点的新旧坐标，用于可视化
            good_new_coords_for_viz = []
            good_old_coords_for_viz = []
            # 用于存储方向有效的特征点的新旧坐标
            direction_valid_new_coords = []
            direction_valid_old_coords = []
            # 用于存储方向无效的特征点的新旧坐标
            direction_invalid_new_coords = []
            direction_invalid_old_coords = []
            
            # 初始化变量，用于计算当前帧中特征点的总位移和有效点数
            frame_displacement_sum_this_frame_for_calc = 0
            valid_points_this_frame_for_calc = 0
            
            if p1 is not None and st is not None:
                for k in range(len(st)):
                    if st[k][0] == 1:  # 如果特征点被成功跟踪
                        original_id = p0_data[k]['id']
                        new_coord_point_arr = p1[k].reshape(1, 2)
                        old_coord_point_arr = current_coords_for_lk[k].reshape(1, 2)
                        
                        # 更新 next_p0_data 以便下一帧跟踪 (如果点在ROI内)
                        x_coord, y_coord = new_coord_point_arr.ravel()
                        if cv2.pointPolygonTest(ROI_POINTS, (x_coord, y_coord), False) >= 0:
                            next_p0_data.append({'id': original_id, 'coords': new_coord_point_arr})
                            good_new_coords_for_viz.append(new_coord_point_arr.ravel())
                            good_old_coords_for_viz.append(old_coord_point_arr.ravel())
                            
                            # 计算特征点移动的角度并检查方向是否在允许范围内
                            new_pt = new_coord_point_arr.ravel()
                            old_pt = old_coord_point_arr.ravel()
                            movement_angle = calculate_angle(new_pt, old_pt)
                            expected_flow_direction = EXPECTED_FLOW_DIRECTION
                            direction_threshold = DIRECTION_THRESHOLD
                            is_valid_direction = is_direction_valid(movement_angle, expected_flow_direction, direction_threshold)
                            
                            # 根据方向是否有效分类特征点
                            if is_valid_direction:
                                direction_valid_new_coords.append(new_pt)
                                direction_valid_old_coords.append(old_pt)
                                direction_valid_count += 1
                            else:
                                direction_invalid_new_coords.append(new_pt)
                                direction_invalid_old_coords.append(old_pt)
                                direction_invalid_count += 1
                            
                            # 判断此点是否用于当前帧的流速计算
                            proceed_with_this_point_for_calc = False
                            if not check_feature_points:  # 若不检查特征点，使用所有点
                                proceed_with_this_point_for_calc = True
                            elif frame_count < total_frames // 2:  # 前半段视频，总是计算
                                proceed_with_this_point_for_calc = True
                            elif not user_selected_ids_for_calculation:  # 后半段，但用户未筛选，总是计算
                                proceed_with_this_point_for_calc = True
                            elif original_id in user_selected_ids_for_calculation:  # 后半段，用户已筛选，且此点被选中
                                proceed_with_this_point_for_calc = True
                            
                            # 只有方向有效的点才参与计算
                            if proceed_with_this_point_for_calc and is_valid_direction:
                                a_calc, b_calc = new_coord_point_arr.ravel()
                                c_calc, d_calc = old_coord_point_arr.ravel()
                                displacement = np.sqrt((a_calc - c_calc) ** 2 + (b_calc - d_calc) ** 2)
                                frame_displacement_sum_this_frame_for_calc += displacement
                                valid_points_this_frame_for_calc += 1
            
            p0_data = next_p0_data  # 更新p0_data以供下一帧使用
            
            # 使用 direction_valid_new_coords 和 direction_valid_old_coords 绘制有效方向点的轨迹（绿色）
            for i, (new_coords, old_coords) in enumerate(zip(direction_valid_new_coords, direction_valid_old_coords)):
                a, b = new_coords
                c, d = old_coords
                
                mask = cv2.line(mask, (int(a), int(b)), (int(c), int(d)), (0, 255, 0), 2)  # 绿色表示有效方向
                if check_feature_points and not halfway_video_generated:
                    halfway_mask = cv2.line(halfway_mask, (int(a), int(b)), (int(c), int(d)), (0, 255, 0), 2)
                
                # 有效轨迹终点绘制为绿色圆点
                output_frame = cv2.circle(output_frame, (int(a), int(b)), 5, (0, 255, 0), -1)
            
            # 使用 direction_invalid_new_coords 和 direction_invalid_old_coords 绘制无效方向点的轨迹（红色）
            for i, (new_coords, old_coords) in enumerate(zip(direction_invalid_new_coords, direction_invalid_old_coords)):
                a, b = new_coords
                c, d = old_coords
                
                mask = cv2.line(mask, (int(a), int(b)), (int(c), int(d)), (0, 0, 255), 1)  # 红色表示无效方向
                if check_feature_points and not halfway_video_generated:
                    halfway_mask = cv2.line(halfway_mask, (int(a), int(b)), (int(c), int(d)), (0, 0, 255), 1)
                
                # 无效轨迹终点绘制为红色圆点
                output_frame = cv2.circle(output_frame, (int(a), int(b)), 4, (0, 0, 255), -1)
            
            # 基于筛选条件计算并记录当前帧的平均流速
            if valid_points_this_frame_for_calc > 0 and dt > 0:
                avg_frame_displacement_for_calc = frame_displacement_sum_this_frame_for_calc / valid_points_this_frame_for_calc
                # print(f"当前帧平均位移: {avg_frame_displacement_for_calc} 像素, "
                #       f"总位移: {frame_displacement_sum_this_frame_for_calc:.2f} 像素，"
                #       f"有效点数: {valid_points_this_frame_for_calc}")
                speed = (avg_frame_displacement_for_calc * pixel_to_meter) / dt
                flow_speeds.append(speed)
                flow_speeds_window.append(speed)
                # 保持滑动窗口大小
                if len(flow_speeds_window) > window_size:
                    flow_speeds_window.pop(0)
                # 累加用于最终报告的总位移 (基于计算中使用的点)
                total_displacement_for_calc += avg_frame_displacement_for_calc
                
                # 记录当前帧的数据
                frame_data.append({
                    'frame_number': frame_count,
                    'avg_displacement': avg_frame_displacement_for_calc,
                    'total_displacement': frame_displacement_sum_this_frame_for_calc,
                    'valid_points': valid_points_this_frame_for_calc,
                    'speed': speed
                })
            
            if len(flow_speeds_window) > 0:
                avg_speed = sum(flow_speeds_window) / len(flow_speeds_window)
            
            # 显示当前平均流速和方向有效/无效的点数
            cv2.putText(output_frame, f"Avg Speed (Window): {avg_speed:.2f} m/s", (30, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            cv2.putText(output_frame, f"Valid Points: {direction_valid_count}", (30, 60),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(output_frame, f"Invalid Points: {direction_invalid_count}", (30, 90),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                        
            cv2.polylines(output_frame, [ROI_POINTS], isClosed=True, color=(255, 0, 0), thickness=2)  # type: ignore
            output_frame = cv2.add(output_frame, mask)
        
        # 如果特征点太少，重新在 ROI 内检测
        if not p0_data or len(p0_data) < 5:
            print("特征点过少或丢失，正在 ROI 内重新检测...")
            newly_detected_points = cv2.goodFeaturesToTrack(frame_gray, mask=roi_mask, **feature_params)
            p0_data = []  # 清空旧的，即使里面有几个点
            if newly_detected_points is not None:
                for i, pt_coords in enumerate(newly_detected_points):
                    p0_data.append({'id': i, 'coords': pt_coords.reshape(1, 2).astype(np.float32)})
            
            mask = np.zeros_like(prev_frame)
            if check_feature_points and not halfway_video_generated:
                halfway_mask = np.zeros_like(prev_frame)
            if not p0_data:
                print("重新检测后仍然没有特征点。")
        
        out.write(output_frame)
        
        # 更新前一帧的灰度图
        prev_gray = frame_gray.copy()
        frame_count += 1
        
        # 统一打印进度
        # if frame_count % 30 == 0:
        #     print(f"已处理 {frame_count}/{total_frames if total_frames > 0 else '?'} 帧 (Real 模式), "
        #           f"当前滑动窗口平均流速: {avg_speed:.2f} m/s, 方向有效点: {len(direction_valid_new_coords)}, "
        #           f"方向无效点: {len(direction_invalid_new_coords)}")
    
    # 确保中途视频写入器已释放
    if halfway_video_writer is not None:
        halfway_video_writer.release()
    
    # 输出结果
    if flow_speeds:
        avg_flow_speed = sum(flow_speeds) / len(flow_speeds)
        # 最后一个窗口的平均流速
        final_window_avg_speed = sum(flow_speeds_window) / len(flow_speeds_window) if flow_speeds_window else 0
        max_flow_speed = max(flow_speeds)
        min_flow_speed = min(flow_speeds)
        
        # 使用四分位法过滤帧数据，并计算过滤后的平均流速
        filtered_frame_data = filter_by_quartile(frame_data, 'avg_displacement')
        # 创建一个集合，包含所有被保留的帧号
        filtered_frame_numbers = {item['frame_number'] for item in filtered_frame_data} if filtered_frame_data else set()
        
        if filtered_frame_data:
            filtered_avg_displacement = sum(item['avg_displacement'] for item in filtered_frame_data) / len(filtered_frame_data)
            filtered_avg_flow_speed = sum(item['speed'] for item in filtered_frame_data) / len(filtered_frame_data)
        else:
            filtered_avg_displacement = 0
            filtered_avg_flow_speed = 0
    else:
        avg_flow_speed = 0
        final_window_avg_speed = 0
        max_flow_speed = 0
        min_flow_speed = 0
        filtered_avg_displacement = 0
        filtered_avg_flow_speed = 0
        filtered_frame_numbers = set()
        print("警告: 未收集到任何符合计算条件的流速数据。")
    
    print("\n流速计算结果:")
    print(f"ROI 顶点: {ROI_POINTS.tolist()}")
    print(f"期望流动方向: {EXPECTED_FLOW_DIRECTION}° ± {DIRECTION_THRESHOLD}°")
    if check_feature_points and user_selected_ids_for_calculation:
        print(f"计算基于用户选择的特征点ID: {sorted(list(user_selected_ids_for_calculation))} (在视频后半部分)")
    else:
        print("计算基于所有成功跟踪的特征点" + (" (或用户未进行有效筛选)" if check_feature_points else ""))
    print(f"全局平均流速 (所有帧): {avg_flow_speed:.4f} m/s")
    print(f"四分位过滤后平均流速: {filtered_avg_flow_speed:.4f} m/s")
    print(f"最终窗口平均流速 (最近{window_size}帧): {final_window_avg_speed:.4f} m/s")
    print(f"最大流速: {max_flow_speed:.4f} m/s")
    print(f"最小流速: {min_flow_speed:.4f} m/s")
    print(f"总位移 (符合计算条件的特征点平均): {total_displacement_for_calc * pixel_to_meter:.4f} m")
    print(f"总帧数 (处理的): {frame_count}")
    print(f"方向有效点总数: {direction_valid_count}")
    print(f"方向无效点总数: {direction_invalid_count}")
    
    # 将流速数据保存到文本文件
    output_dir = os.path.dirname(output_path_halfway)
    result_file = os.path.join(output_dir, 'flow_speed_results.txt')
    
    # 从视频路径提取文件名（不包括扩展名）用于CSV文件名
    video_basename = os.path.splitext(os.path.basename(INPUT_VIDEO_PATH))[0]
    csv_filename = os.path.join(output_dir, f'{video_basename}_frame_data.csv')
    
    # 将帧数据保存到CSV文件
    # if frame_data:
    #     with open(csv_filename, 'w', newline='') as csvfile:
    #         fieldnames = ['frame_number', 'avg_displacement', 'total_displacement', 'valid_points', 'speed', 'kept_after_filtering']
    #         writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    #         writer.writeheader()
    #         for data in frame_data:
    #             # 添加一个新列，标记该帧是否在四分位过滤后被保留
    #             data_copy = data.copy()
    #             data_copy['kept_after_filtering'] = 1 if data['frame_number'] in filtered_frame_numbers else 0
    #             writer.writerow(data_copy)
    #     print(f"帧数据已保存到CSV文件: {os.path.abspath(csv_filename)}")
    
    with open(result_file, 'w') as f:
        f.write(f"模式: real\n")
        f.write(f"输入视频: {os.path.basename(INPUT_VIDEO_PATH)}\n")
        f.write(f"ROI 顶点: {ROI_POINTS.tolist()}\n")
        f.write(f"期望流动方向: {EXPECTED_FLOW_DIRECTION}° ± {DIRECTION_THRESHOLD}°\n")
        f.write(f"是否中途检查特征点: {'是' if check_feature_points else '否'}\n")
        if check_feature_points and user_selected_ids_for_calculation:
            f.write(f"计算基于用户选择的特征点ID: {sorted(list(user_selected_ids_for_calculation))} (在视频后半部分)\n")
        else:
            f.write("计算基于所有成功跟踪的特征点" + (" (或用户未进行有效筛选)" if check_feature_points else "") + "\n")
        f.write(f"全局平均流速 (所有帧): {avg_flow_speed:.4f} m/s\n")
        f.write(f"四分位过滤后平均流速: {filtered_avg_flow_speed:.4f} m/s\n")
        f.write(f"最终窗口平均流速 (最近{window_size}帧): {final_window_avg_speed:.4f} m/s\n")
        f.write(f"最大流速: {max_flow_speed:.4f} m/s\n")
        f.write(f"最小流速: {min_flow_speed:.4f} m/s\n")
        f.write(f"总位移 (符合计算条件的特征点平均): {total_displacement_for_calc * pixel_to_meter:.4f} m\n")
        f.write(f"总帧数 (处理的): {frame_count}\n")
        f.write(f"方向有效点总数: {direction_valid_count}\n")
        f.write(f"方向无效点总数: {direction_invalid_count}\n")
        f.write("\n帧流速数据 (基于筛选条件):\n")
        for i, speed in enumerate(flow_speeds):
            f.write(f"帧 {i + 1}: {speed:.4f} m/s\n")
    
    print(f"流速数据已保存到: {os.path.abspath(result_file)}")
    
    return frame_count

def process_video(input_video_path, mode, check_features=CHECK_FEATURE_POINTS):
    """处理视频，根据模式执行预处理或光流计算"""
    # 初始化视频捕获
    cap = cv2.VideoCapture(input_video_path)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {input_video_path}")
        return

    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))  # 获取总帧数
    
    ret, first_frame = cap.read()
    if not ret:
        print("错误: 无法读取第一帧")
        cap.release()
        return
    height, width = first_frame.shape[:2]
    
    # 重置到第一帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)

    # 设置视频输出
    output_dir = 'data/output'
    os.makedirs(output_dir, exist_ok=True)
    
    if mode == 'pre':
        output_path = os.path.join(output_dir, 'pre_flow.mp4')
    else:  # real mode
        output_path = os.path.join(output_dir, 'output_flow.mp4')
        output_path_halfway = os.path.join(output_dir, 'halfway_output_flow.mp4')  # 中途视频路径

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    fps = cap.get(cv2.CAP_PROP_FPS)
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 根据模式调用不同的处理函数
    frame_count = 0
    if mode == 'pre':
        frame_count = process_video_pre_mode(cap, out, total_frames)
    else:  # real mode
        frame_count = process_video_real_mode(cap, out, output_path_halfway, total_frames, check_features)
    
    # 释放资源
    cap.release()
    out.release()
    print(f"视频已保存到: {os.path.abspath(output_path)}")

if __name__ == "__main__":
    # 直接使用配置变量调用处理函数
    process_video(INPUT_VIDEO_PATH, CURRENT_MODE, CHECK_FEATURE_POINTS)