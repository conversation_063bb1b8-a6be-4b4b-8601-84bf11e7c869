"""
通用配置管理模块

适配所有算法（PIV、OTV、STIV）的统一配置管理系统
支持完整的YAML配置文件解析和验证
"""

import yaml
import logging
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class GlobalConfig:
    """全局配置"""
    debug: Dict[str, Any]
    output_dir: str
    result_csv: str
    pixel_to_meter: float


@dataclass
class PIVConfig:
    """PIV算法配置"""
    window_size: int
    overlap: int
    search_area_size: int
    dt: float
    validation_method: str
    validation_threshold: float
    interpolation_method: str
    smoothing_method: str
    smoothing_size: int


@dataclass
class OTVConfig:
    """OTV算法配置"""
    optical_flow: Dict[str, Any]
    tracking: Dict[str, Any]
    velocity: Dict[str, Any]
    feature_params: Dict[str, Any]


@dataclass
class STIVCoordinateSystem:
    """STIV坐标系统配置"""
    type: str  # "pixel_distance" 或 "geographic"
    calibration: Dict[str, Any]


@dataclass
class STIVAnalysisLines:
    """STIV分析线配置"""
    mode: str  # "adaptive" 或 "manual"
    flow_direction: float
    line_count: int
    line_spacing: int
    line_length: int
    center_point: Optional[List[int]] = None
    lines: Optional[List[int]] = None
    lines_range: Optional[List[List[int]]] = None
    # 新增绘制模式相关字段
    drawing_mode: str = "default"  # "default" | "infinite_with_roi" | "smart_segmentation"
    optimal_length: int = 200      # 分析线的最佳长度（像素）
    interval_distance: int = 30    # 分割后各段分析线之间的间隔距离（像素）


@dataclass
class STIVAlgorithm:
    """STIV算法参数配置"""
    window_shape: List[int]
    filter_window: int
    overlap: int
    polar_filter_width: int
    ksize: int
    method: str


@dataclass
class STIVPreprocessing:
    """STIV预处理配置"""
    rotate_image: bool
    pre_roi: Optional[List[List[int]]]
    roi: Optional[List[List[int]]]
    resolution: float
    image_correction: Dict[str, Any]


@dataclass
class STIVConfig:
    """STIV算法配置"""
    coordinate_system: STIVCoordinateSystem
    analysis_lines: STIVAnalysisLines
    algorithm: STIVAlgorithm
    preprocessing: STIVPreprocessing


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_path: str):
        """初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        # 加载配置
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self._raw_config = yaml.safe_load(f)
        
        # 解析各部分配置
        self.global_config = self._parse_global_config()
        self.preprocessing_config = self._parse_preprocessing_config()
        self.piv_config = self._parse_piv_config()
        self.otv_config = self._parse_otv_config()
        self.stiv_config = self._parse_stiv_config()
        
        logger.info(f"配置文件加载完成: {config_path}")
    
    def _parse_global_config(self) -> GlobalConfig:
        """解析全局配置"""
        return GlobalConfig(
            debug=self._raw_config.get("debug", {}),
            output_dir=self._raw_config.get("output_dir", "data/output"),
            result_csv=self._raw_config.get("result_csv", "flow_speed_results.csv"),
            pixel_to_meter=self._raw_config.get("pixel_to_meter", 0.0084)
        )

    def _parse_preprocessing_config(self) -> STIVPreprocessing:
        """解析通用预处理配置"""
        prep_data = self._raw_config.get("preprocessing", {})
        return STIVPreprocessing(
            rotate_image=prep_data.get("rotate_image", False),
            pre_roi=prep_data.get("pre_roi"),
            roi=prep_data.get("roi"),
            resolution=prep_data.get("resolution", 1.0),
            image_correction=prep_data.get("image_correction", {
                "apply": False,
                "k1": 0.0,
                "c": 0.0,
                "f": 0.0
            })
        )
    
    def _parse_piv_config(self) -> Optional[PIVConfig]:
        """解析PIV配置"""
        piv_params = self._raw_config.get("piv_params")
        if not piv_params:
            return None
        
        return PIVConfig(
            window_size=piv_params.get("window_size", 32),
            overlap=piv_params.get("overlap", 16),
            search_area_size=piv_params.get("search_area_size", 64),
            dt=piv_params.get("dt", 0.1),
            validation_method=piv_params.get("validation_method", "std_threshold"),
            validation_threshold=piv_params.get("validation_threshold", 2.0),
            interpolation_method=piv_params.get("interpolation_method", "linear"),
            smoothing_method=piv_params.get("smoothing_method", "gaussian"),
            smoothing_size=piv_params.get("smoothing_size", 3)
        )
    
    def _parse_otv_config(self) -> Optional[OTVConfig]:
        """解析OTV配置"""
        otv_params = self._raw_config.get("otv_params")
        if not otv_params:
            return None
        
        return OTVConfig(
            optical_flow=otv_params.get("optical_flow", {}),
            tracking=otv_params.get("tracking", {}),
            velocity=otv_params.get("velocity", {}),
            feature_params=otv_params.get("feature_params", {})
        )
    
    def _parse_stiv_config(self) -> Optional[STIVConfig]:
        """解析STIV配置"""
        stiv_params = self._raw_config.get("stiv_params")
        if not stiv_params:
            return None
        
        # 坐标系统配置
        coord_system_data = stiv_params.get("coordinate_system", {})
        coordinate_system = STIVCoordinateSystem(
            type=coord_system_data.get("type", "pixel_distance"),
            calibration=coord_system_data.get("calibration", {})
        )
        
        # 分析线配置
        lines_data = stiv_params.get("analysis_lines", {})
        analysis_lines = STIVAnalysisLines(
            mode=lines_data.get("mode", "adaptive"),
            flow_direction=lines_data.get("flow_direction", 0.0),
            line_count=lines_data.get("line_count", 3),
            line_spacing=lines_data.get("line_spacing", 50),
            line_length=lines_data.get("line_length", 200),
            center_point=lines_data.get("center_point"),
            lines=lines_data.get("lines"),
            lines_range=lines_data.get("lines_range"),
            # 新增绘制模式相关字段
            drawing_mode=lines_data.get("drawing_mode", "default"),
            optimal_length=lines_data.get("optimal_length", 200),
            interval_distance=lines_data.get("interval_distance", 30)
        )
        
        # 算法参数配置
        algo_data = stiv_params.get("algorithm", {})
        algorithm = STIVAlgorithm(
            window_shape=algo_data.get("window_shape", [51, 51]),
            filter_window=algo_data.get("filter_window", 64),
            overlap=algo_data.get("overlap", 0),
            polar_filter_width=algo_data.get("polar_filter_width", 10),
            ksize=algo_data.get("ksize", 7),
            method=algo_data.get("method", "fft")
        )
        
        return STIVConfig(
            coordinate_system=coordinate_system,
            analysis_lines=analysis_lines,
            algorithm=algorithm,
            preprocessing=self.preprocessing_config  # 使用通用预处理配置
        )
    
    def get_algorithm_config(self, algorithm: str) -> Optional[Any]:
        """获取指定算法的配置
        
        Args:
            algorithm: 算法名称，"piv", "otv", "stiv"
            
        Returns:
            对应的算法配置对象
        """
        if algorithm.lower() == "piv":
            return self.piv_config
        elif algorithm.lower() == "otv":
            return self.otv_config
        elif algorithm.lower() == "stiv":
            return self.stiv_config
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
    
    def validate_config(self, algorithm: str) -> Tuple[bool, List[str]]:
        """验证指定算法的配置
        
        Args:
            algorithm: 算法名称
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证全局配置
        if not self.global_config.output_dir:
            errors.append("输出目录不能为空")
        
        # 验证算法特定配置
        algo_config = self.get_algorithm_config(algorithm)
        if algo_config is None:
            errors.append(f"未找到{algorithm.upper()}算法配置")
            return False, errors
        
        if algorithm.lower() == "stiv":
            errors.extend(self._validate_stiv_config(algo_config))
        
        return len(errors) == 0, errors
    
    def _validate_stiv_config(self, config: STIVConfig) -> List[str]:
        """验证STIV配置"""
        errors = []
        
        # 验证坐标系统
        if config.coordinate_system.type not in ["pixel_distance", "geographic"]:
            errors.append(f"不支持的坐标系统类型: {config.coordinate_system.type}")
        
        # 验证分析线配置
        if config.analysis_lines.mode not in ["adaptive", "manual"]:
            errors.append(f"不支持的分析线模式: {config.analysis_lines.mode}")
        
        if config.analysis_lines.mode == "manual":
            if not config.analysis_lines.lines or not config.analysis_lines.lines_range:
                errors.append("手动模式下必须指定lines和lines_range")
            elif len(config.analysis_lines.lines) != len(config.analysis_lines.lines_range):
                errors.append("手动模式下lines和lines_range的数量必须一致")
        
        # 验证算法参数
        if config.algorithm.method not in ["fft", "gmt"]:
            errors.append(f"不支持的运动计算方法: {config.algorithm.method}")
        
        return errors
    
    def get_debug_config(self) -> Dict[str, Any]:
        """获取调试配置"""
        return self.global_config.debug
    
    def is_debug_enabled(self) -> bool:
        """检查是否启用调试模式"""
        return self.global_config.debug.get("save_debug_images", False)
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.global_config.debug.get("log_level", "INFO")
    
    def get_preprocessing_config(self) -> STIVPreprocessing:
        """获取预处理配置"""
        return self.preprocessing_config

    def get_raw_config(self) -> Dict[str, Any]:
        """获取原始配置字典"""
        return self._raw_config.copy()
    
    def save_config(self, output_path: str):
        """保存配置到文件
        
        Args:
            output_path: 输出文件路径
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._raw_config, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)
        
        logger.info(f"配置已保存到: {output_path}")


def load_config(config_path: str) -> ConfigManager:
    """加载配置文件的便捷函数
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        ConfigManager实例
    """
    return ConfigManager(config_path)
