#!/usr/bin/env python3
"""
自动视频截取工具
用于AWIVE分析前的智能视频预处理
"""

import cv2
import os
from pathlib import Path
from typing import Optional, Tuple
import tempfile

def get_video_info(video_path: str) -> Tuple[float, int, float]:
    """
    获取视频基本信息
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        tuple: (总时长(秒), 总帧数, 帧率)
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return 0, 0, 0
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps if fps > 0 else 0
    
    cap.release()
    return duration, frame_count, fps

def auto_trim_for_awive(video_path: str, 
                       max_duration: float = 40.0,
                       start_offset: float = 10.0,
                       output_dir: Optional[str] = None) -> Optional[str]:
    """
    为AWIVE分析自动截取视频
    
    Args:
        video_path: 输入视频路径
        max_duration: 最大分析时长（秒）
        start_offset: 开始截取的偏移时间（秒）
        output_dir: 输出目录，None则使用临时目录
        
    Returns:
        截取后的视频路径，失败时返回None
    """
    video_path = Path(video_path)
    if not video_path.exists():
        print(f"错误: 视频文件不存在: {video_path}")
        return None
    
    # 获取视频信息
    duration, frame_count, fps = get_video_info(str(video_path))
    if duration == 0:
        print(f"错误: 无法获取视频信息: {video_path}")
        return None
    
    print(f"视频信息: 时长={duration:.1f}秒, 帧数={frame_count}, 帧率={fps:.2f}")
    
    # 判断是否需要截取
    if duration <= max_duration:
        print(f"视频时长({duration:.1f}秒) <= 最大时长({max_duration}秒)，无需截取")
        return str(video_path)
    
    # 计算截取参数
    end_time = start_offset + max_duration
    if end_time > duration:
        # 如果计算的结束时间超过视频长度，从后往前计算
        end_time = duration
        start_time = max(0, end_time - max_duration)
    else:
        start_time = start_offset
    
    print(f"自动截取: {start_time:.1f}秒 - {end_time:.1f}秒 (共{end_time-start_time:.1f}秒)")
    
    # 设置输出路径
    if output_dir is None:
        output_dir = tempfile.gettempdir()
    else:
        os.makedirs(output_dir, exist_ok=True)
    
    output_filename = f"{video_path.stem}_auto_trimmed_{int(start_time)}_{int(end_time)}.mp4"
    output_path = Path(output_dir) / output_filename
    
    # 执行截取
    success = trim_video_segment(str(video_path), start_time, end_time, str(output_path))
    
    if success:
        print(f"自动截取完成: {output_path}")
        return str(output_path)
    else:
        print("自动截取失败")
        return None

def trim_video_segment(input_path: str, start_seconds: float, end_seconds: float, output_path: str) -> bool:
    """
    截取视频片段
    
    Args:
        input_path: 输入视频路径
        start_seconds: 开始时间（秒）
        end_seconds: 结束时间（秒）
        output_path: 输出视频路径
        
    Returns:
        是否成功
    """
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        return False
    
    # 获取视频属性
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # 计算帧范围
    start_frame = int(start_seconds * fps)
    end_frame = int(end_seconds * fps)
    
    # 设置起始位置
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
    
    # 创建输出视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    current_frame = start_frame
    processed_frames = 0
    
    try:
        while current_frame < end_frame:
            ret, frame = cap.read()
            if not ret:
                break
            
            out.write(frame)
            current_frame += 1
            processed_frames += 1
            
            # 显示进度
            if processed_frames % 100 == 0:
                progress = (current_frame - start_frame) / (end_frame - start_frame) * 100
                print(f"截取进度: {progress:.1f}%")
        
        success = True
        
    except Exception as e:
        print(f"截取过程中出错: {e}")
        success = False
    
    finally:
        cap.release()
        out.release()
    
    return success

def smart_video_preprocessing(video_path: str, 
                            config: dict,
                            auto_trim: bool = True,
                            max_duration: float = 40.0) -> Tuple[str, bool]:
    """
    智能视频预处理
    
    Args:
        video_path: 原始视频路径
        config: AWIVE配置字典
        auto_trim: 是否启用自动截取
        max_duration: 最大分析时长
        
    Returns:
        tuple: (处理后的视频路径, 是否进行了截取)
    """
    if not auto_trim:
        return video_path, False
    
    duration, _, _ = get_video_info(video_path)
    if duration <= max_duration:
        return video_path, False
    
    # 创建临时目录用于存储截取的视频
    temp_dir = Path("data/temp/auto_trimmed")
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    trimmed_path = auto_trim_for_awive(
        video_path, 
        max_duration=max_duration,
        start_offset=10.0,
        output_dir=str(temp_dir)
    )
    
    if trimmed_path:
        # 更新配置中的视频路径
        config['dataset']['video_fp'] = trimmed_path
        return trimmed_path, True
    else:
        return video_path, False

# 使用示例
if __name__ == "__main__":
    # 测试自动截取功能
    test_video = "data/video/0812_speed_720.mp4"
    
    if Path(test_video).exists():
        result = auto_trim_for_awive(
            test_video,
            max_duration=30.0,
            start_offset=10.0,
            output_dir="data/temp"
        )
        
        if result:
            print(f"测试成功: {result}")
        else:
            print("测试失败")
    else:
        print(f"测试视频不存在: {test_video}")
