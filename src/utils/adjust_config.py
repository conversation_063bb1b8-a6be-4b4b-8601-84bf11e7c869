import json
import copy
from pathlib import Path
import sys
import numpy as np
import cv2


def adjust_coords_gcp_crop_using_refs(config: dict, lines: list, lines_range: list) -> tuple[list, list]:
    """
    根据 GCP 的 _crop_using_refs 逻辑调整 lines 和 lines_range。
    
    该函数模拟了 lib/awive/awive/preprocess/correct_image.py 中 _crop_using_refs 的行为：
    根据 GCP 像素坐标的范围裁剪图像，并调整坐标。
    
    参数:
        config: 原始配置字典。
        lines: 需要调整的 lines 列表。
        lines_range: 需要调整的 lines_range 列表。
        
    返回:
        调整后的 lines 和 lines_range。
    """
    # 获取 GCP 像素坐标
    gcp_pixels = config.get('dataset', {}).get('gcp', {}).get('pixels', [])
    if not gcp_pixels or not isinstance(gcp_pixels, list) or len(gcp_pixels) < 2:
        print("  - 警告: GCP 像素坐标无效或不足，无法调整。", file=sys.stderr)
        return lines, lines_range
    
    # 计算 GCP 像素坐标的最小和最大值
    gcp_pixels_array = np.array(gcp_pixels)
    x_min, y_min = np.min(gcp_pixels_array, axis=0)
    x_max, y_max = np.max(gcp_pixels_array, axis=0)
    
    print(f"  - GCP 坐标范围: x_min={x_min}, y_min={y_min}, x_max={x_max}, y_max={y_max}")
    
    # 调整 lines (y 坐标)
    adjusted_lines = []
    for y in lines:
        # 减去 y_min (与图像裁剪对应)
        adj_y = max(0, y - y_min)
        adjusted_lines.append(adj_y)
    
    # 调整 lines_range (x 坐标范围)
    adjusted_lines_range = []
    for x_range in lines_range:
        x_start, x_end = x_range
        # 减去 x_min (与图像裁剪对应)
        adj_x_start = max(0, x_start - x_min)
        adj_x_end = max(adj_x_start, x_end - x_min)
        adjusted_lines_range.append([adj_x_start, adj_x_end])
    
    return adjusted_lines, adjusted_lines_range


def adjust_roi_coordinates(config: dict) -> dict:
    """
    根据 'preprocessing.pre_roi' 调整 'preprocessing.roi' 的坐标。

    假设坐标格式为 awive 库使用的 [[y1, x1], [y2, x2]]。

    参数:
        config: 原始配置字典。

    返回:
        包含调整后 'preprocessing.roi' 的新配置字典。
        如果无法或不需要调整，则返回原始配置字典。
    """
    try:
        preprocessing_cfg = config.get('preprocessing', {})
        pre_roi = preprocessing_cfg.get('pre_roi')
        roi = preprocessing_cfg.get('roi')

        # 检查必要的键和有效的格式是否存在
        if not (pre_roi and roi and
                isinstance(pre_roi, list) and len(pre_roi) == 2 and
                isinstance(pre_roi[0], list) and len(pre_roi[0]) == 2 and
                isinstance(pre_roi[1], list) and len(pre_roi[1]) == 2 and
                isinstance(roi, list) and len(roi) == 2 and
                isinstance(roi[0], list) and len(roi[0]) == 2 and
                isinstance(roi[1], list) and len(roi[1]) == 2):
            print("警告: 配置中 'pre_roi' 或 'roi' 缺失或格式无效。跳过调整。", file=sys.stderr)
            return config # 如果键/格式错误，返回原始配置

        # 创建深拷贝以避免修改原始配置字典
        adjusted_config = copy.deepcopy(config)

        # 提取坐标 [[y1, x1], [y2, x2]]
        pre_y1, pre_x1 = pre_roi[0]
        orig_y1, orig_x1 = roi[0]
        orig_y2, orig_x2 = roi[1]

        # 计算相对于预裁剪区域的新坐标
        new_y1 = max(0, orig_y1 - pre_y1)
        new_x1 = max(0, orig_x1 - pre_x1)
        # 确保停止坐标是相对的，并且不小于起始坐标
        new_y2 = max(new_y1, orig_y2 - pre_y1)
        new_x2 = max(new_x1, orig_x2 - pre_x1)

        # 更新调整后配置中的 roi
        adjusted_config['preprocessing']['roi'] = [[new_y1, new_x1], [new_y2, new_x2]]
        # 保存调整后的 roi 的左上角坐标，用于后续计算
        adj_roi_y1, adj_roi_x1 = new_y1, new_x1

        print(f"原始 roi: {roi}")
        print(f"基于 pre_roi {pre_roi} 调整后的 roi: {adjusted_config['preprocessing']['roi']}")

        # --- 新增：调整其他依赖 pre_roi 和 调整后roi 的坐标 ---

        # 检查 GCP 是否启用
        gcp_enabled = False
        try:
            gcp_enabled = config.get('dataset', {}).get('gcp', {}).get('apply', False)
        except Exception:
            # 忽略检查过程中的任何错误，默认为未启用
            pass
            
        # 获取 lines 和 lines_range
        lines = adjusted_config.get('lines', [])
        stiv_cfg = adjusted_config.get('stiv', {})
        lines_range = stiv_cfg.get('lines_range', [])
        
        # 检查格式有效性
        valid_lines = lines and isinstance(lines, list) and all(isinstance(y, int) for y in lines)
        valid_lines_range = (lines_range and isinstance(lines_range, list) and
            all(isinstance(r, list) and len(r) == 2 and isinstance(r[0], int) and isinstance(r[1], int) for r in lines_range))
            
        if not valid_lines and 'lines' in adjusted_config:
            print("  - 警告: lines 格式无效 (应为整数列表)，跳过调整。", file=sys.stderr)
        
        if not valid_lines_range and 'stiv' in adjusted_config and 'lines_range' in stiv_cfg:
            print("  - 警告: stiv.lines_range 格式无效 (应为 [[x_start, x_end], ...])，跳过调整。", file=sys.stderr)
        
        # 如果 lines 和 lines_range 格式有效，根据 GCP 状态进行不同的调整
        if valid_lines or valid_lines_range:
            if gcp_enabled:
                print("  - 信息: 检测到 GCP 已启用 (dataset.gcp.apply = true)。")
                print("  - 将应用两阶段调整: 首先按 GCP 裁剪逻辑，然后按常规 pre_roi/roi 调整。")
                
                # 第一阶段：根据 GCP 的 _crop_using_refs 逻辑调整
                if valid_lines and valid_lines_range:
                    adjusted_lines, adjusted_lines_range = adjust_coords_gcp_crop_using_refs(
                        config, lines, lines_range)
                    
                    # 更新 lines 和 lines_range 为第一阶段调整后的值，准备第二阶段调整
                    lines = adjusted_lines
                    lines_range = adjusted_lines_range
                    
                    print(f"  - 基于 GCP 裁剪逻辑调整后 lines: {adjusted_lines}")
                    print(f"  - 基于 GCP 裁剪逻辑调整后 lines_range: {adjusted_lines_range}")
                elif valid_lines:
                    adjusted_lines, _ = adjust_coords_gcp_crop_using_refs(config, lines, [])
                    lines = adjusted_lines
                    print(f"  - 基于 GCP 裁剪逻辑调整后 lines: {adjusted_lines}")
                elif valid_lines_range:
                    _, adjusted_lines_range = adjust_coords_gcp_crop_using_refs(config, [], lines_range)
                    lines_range = adjusted_lines_range
                    print(f"  - 基于 GCP 裁剪逻辑调整后 lines_range: {adjusted_lines_range}")
                
                # 第二阶段继续进行常规 pre_roi/roi 调整 (下面的代码)
            
            # 不管是否启用 GCP，都进行常规的 pre_roi/roi 调整（如果 GCP 启用，这是第二阶段的调整）
            
            # 调整 lines (顶层列表)
            if valid_lines:
                original_lines = list(lines) # 拷贝用于打印
                # 第一步：基于 pre_roi 调整
                lines_after_pre_roi = [max(0, y - pre_y1) for y in lines]
                # 第二步：基于调整后的 roi 的 y1 (adj_roi_y1) 调整
                final_lines = [max(0, y_pre - adj_roi_y1) for y_pre in lines_after_pre_roi]
                adjusted_config['lines'] = final_lines
                
                if gcp_enabled:
                    print(f"  - 经过 GCP 和 pre_roi/roi 双重调整后 lines: {final_lines}")
                else:
                    print(f"  - 原始 lines: {original_lines}")
                    print(f"  - 基于 pre_roi 和调整后 roi 调整后 lines: {final_lines}")
            
            # 调整 stiv.lines_range
            if valid_lines_range:
                original_lines_range = copy.deepcopy(lines_range) # 深拷贝用于打印
                final_lines_range = []
                for r in lines_range:
                    orig_x_start, orig_x_end = r
                    # 第一步：基于 pre_roi 调整
                    x_start_after_pre = max(0, orig_x_start - pre_x1)
                    x_end_after_pre = max(x_start_after_pre, orig_x_end - pre_x1)
                    # 第二步：基于调整后的 roi 的 x1 (adj_roi_x1) 调整
                    final_x_start = max(0, x_start_after_pre - adj_roi_x1)
                    final_x_end = max(final_x_start, x_end_after_pre - adj_roi_x1)
                    final_lines_range.append([final_x_start, final_x_end])
                
                # 确保更新回 adjusted_config['stiv']
                if 'stiv' not in adjusted_config: adjusted_config['stiv'] = {}
                adjusted_config['stiv']['lines_range'] = final_lines_range
                
                if gcp_enabled:
                    print(f"  - 经过 GCP 和 pre_roi/roi 双重调整后 stiv.lines_range: {final_lines_range}")
                else:
                    print(f"  - 原始 stiv.lines_range: {original_lines_range}")
                    print(f"  - 基于 pre_roi 和调整后 roi 调整后 stiv.lines_range: {final_lines_range}")

        return adjusted_config

    except Exception as e:
        print(f"ROI 调整期间出错: {e}", file=sys.stderr)
        # 如果出现意外错误，返回原始配置
        return config

def load_and_adjust_config_dict(original_config_path: Path) -> dict:
    """
    加载原始配置文件，调整 ROI 坐标，并返回调整后的配置字典。

    Args:
        original_config_path: 原始配置文件的路径。

    Returns:
        调整后的配置字典。

    Raises:
        FileNotFoundError: 如果输入文件未找到。
        json.JSONDecodeError: 如果 JSON 文件无效。
        Exception: 其他读取或处理错误。
    """
    if not original_config_path.is_file():
        raise FileNotFoundError(f"错误: 输入配置文件未找到: {original_config_path}")

    try:
        with open(original_config_path, 'r') as f:
            original_config = json.load(f)
    except json.JSONDecodeError as e:
        print(f"错误: 无法解码输入的 JSON 文件 {original_config_path}: {e}", file=sys.stderr)
        raise # 重新引发异常，让调用者处理
    except Exception as e:
        print(f"读取输入文件 {original_config_path} 时出错: {e}", file=sys.stderr)
        raise # 重新引发异常

    adjusted_config = adjust_roi_coordinates(original_config)
    return adjusted_config

# 移除了 main 函数和 argparse 相关代码 