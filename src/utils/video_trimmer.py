import cv2
import numpy as np
import os
import time
import csv
import yaml
from typing import Optional, Dict, Any

# ROI 配置 多边形顶点[x,y] - 默认使用与simple_calculation.py相同的配置
ROI_POINTS = np.array([[550,150],[1000,150],[1120,250],[1250,250],[1250,500],[1100,500],[900,350],[800,350]], np.int32)

def sort_polygon_points_clockwise(points):
    """将多边形顶点按顺时针方向排序"""
    # 计算多边形的中心点
    center = np.mean(points, axis=0)
    
    # 计算每个点相对于中心点的角度
    def get_angle(point):
        return np.arctan2(point[1] - center[1], point[0] - center[0])
    
    # 按角度排序
    sorted_points = sorted(points, key=get_angle, reverse=True)
    return np.array(sorted_points, dtype=np.int32)

# 确保ROI_POINTS按顺时针排序
ROI_POINTS = sort_polygon_points_clockwise(ROI_POINTS)

def load_csv_batch_config(config_path: str = "config/csv_batch_config.yaml") -> Dict[str, Any]:
    """加载CSV批处理配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典，如果加载失败则返回默认配置
    """
    default_config = {
        'paths': {
            'video_base_path': "/home/<USER>/data/xiwu/",
            'output_dir': "data/video/",
            'csv_file': "config/selected.csv"
        },
        'trimming': {
            'buffer_seconds': 60,
            'min_duration': 30,
            'skip_existing': True
        },
        'output': {
            'naming_pattern': "{original_name}_{start_time}_{end_time}.mp4",
            'create_log': True,
            'log_file': "data/output/csv_batch_log.txt"
        },
        'processing': {
            'max_concurrent': 1,
            'show_progress': True,
            'add_visual_elements': False
        },
        'debug': {
            'test_mode': False,
            'test_count': 3,
            'verbose': True
        }
    }
    
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                print(f"✓ 已加载配置文件: {config_path}")
                return config
        else:
            print(f"⚠ 配置文件不存在: {config_path}，使用默认配置")
            return default_config
    except Exception as e:
        print(f"⚠ 加载配置文件失败: {e}，使用默认配置")
        return default_config

def draw_grid(frame, grid_step=50):
    """在帧上绘制网格和坐标轴"""
    h, w = frame.shape[:2]
    # 绘制垂直线和标签
    for x in range(0, w, grid_step):
        cv2.line(frame, (x, 0), (x, h), (200, 200, 200), 1)
        cv2.putText(frame, str(x), (x + 5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    # 绘制水平线和标签
    for y in range(0, h, grid_step):
        cv2.line(frame, (0, y), (w, y), (200, 200, 200), 1)
        cv2.putText(frame, str(y), (5, y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    return frame

def time_to_seconds(time_str):
    """将 '分:秒' 格式的时间字符串转换为秒数"""
    parts = time_str.split(':')
    if len(parts) == 2:
        minutes, seconds = int(parts[0]), float(parts[1])
        return minutes * 60 + seconds
    else:
        try:
            return float(time_str)
        except ValueError:
            print(f"错误: 无法解析时间格式 '{time_str}'，请使用 '分:秒' 格式")
            return 0

def trim_video(input_video_path, start_time, end_time, roi_points=None, output_dir=None, add_visual_elements=True) -> Optional[str]:
    """
    裁剪视频并添加网格与ROI多边形
    
    参数:
        input_video_path: 输入视频文件路径
        start_time: 开始时间，格式为 '分:秒' 字符串
        end_time: 结束时间，格式为 '分:秒' 字符串
        roi_points: 可选的ROI多边形顶点，如果不提供则使用默认值
        output_dir: 输出目录，如果不提供则使用 'data/output/trimmed'
        add_visual_elements: 是否添加网格和ROI多边形 (True/False)
    
    返回:
        输出视频路径，失败时返回None
    """
    # 确保输出目录存在
    if output_dir is None:
        output_dir = 'data/output/trimmed'
    os.makedirs(output_dir, exist_ok=True)
    
    # 使用原文件名（不包含后缀部分）和裁剪的时间范围作为输出文件名
    base_name = os.path.splitext(os.path.basename(input_video_path))[0]
    start_time_str = start_time.replace(':', '').zfill(4)
    end_time_str = end_time.replace(':', '').zfill(4)
    output_path = os.path.join(output_dir, f'{base_name}_{start_time_str}_{end_time_str}.mp4')
    
    # 使用默认ROI如果没有提供
    if roi_points is None:
        roi_points = ROI_POINTS
    else:
        roi_points = sort_polygon_points_clockwise(np.array(roi_points, np.int32))
    
    # 初始化视频捕获
    cap = cv2.VideoCapture(input_video_path)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {input_video_path}")
        return None
    
    # 获取视频属性
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 将开始和结束时间转换为帧号
    start_seconds = time_to_seconds(start_time)
    end_seconds = time_to_seconds(end_time)
    start_frame = int(start_seconds * fps)
    end_frame = int(end_seconds * fps)
    
    if end_frame <= start_frame:
        print("错误: 结束时间必须大于开始时间")
        cap.release()
        return None
    
    if start_frame >= total_frames:
        print(f"错误: 开始时间 ({start_seconds}秒) 超出视频长度 ({total_frames/fps:.2f}秒)")
        cap.release()
        return None
    
    # 限制结束帧不超出视频总长度
    end_frame = min(end_frame, total_frames)
    
    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 跳到开始帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
    
    # 处理每一帧
    current_frame = start_frame
    print(f"开始裁剪视频: {os.path.basename(input_video_path)}")
    print(f"从 {start_time} ({start_frame} 帧) 到 {end_time} ({end_frame} 帧)")
    print(f"{'添加' if add_visual_elements else '不添加'}网格和ROI多边形")
    
    while current_frame < end_frame:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 处理帧: 根据add_visual_elements参数决定是否添加网格和ROI多边形
        if add_visual_elements:
            output_frame = frame.copy()
            output_frame = draw_grid(output_frame)
            cv2.polylines(output_frame, [roi_points], isClosed=True, color=(255, 0, 0), thickness=2)
        else:
            output_frame = frame
        
        # 写入输出视频
        out.write(output_frame)
        current_frame += 1
        
        # 打印进度
        if current_frame % 30 == 0:
            progress = (current_frame - start_frame) / (end_frame - start_frame) * 100
            # print(f"处理进度: {progress:.1f}% ({current_frame - start_frame}/{end_frame - start_frame} 帧)")
    
    # 释放资源
    cap.release()
    out.release()
    
    print(f"视频裁剪完成。输出文件: {os.path.abspath(output_path)}")
    return output_path

def parse_chinese_time_to_seconds(time_str: str) -> int:
    """将中文时间格式转换为秒数
    
    Args:
        time_str: 中文时间格式，如 "上午10时25分00秒" 或 "下午3时40分00秒"
        
    Returns:
        对应的秒数（从午夜0点开始计算）
    """
    import re
    
    # 处理上午/下午
    is_pm = '下午' in time_str
    is_am = '上午' in time_str
    
    # 提取时分秒
    pattern = r'(\d+)时(\d+)分(\d+)秒'
    match = re.search(pattern, time_str)
    
    if not match:
        raise ValueError(f"无法解析时间格式: {time_str}")
    
    hour, minute, second = map(int, match.groups())
    
    # 转换为24小时制
    if is_pm and hour != 12:
        hour += 12
    elif is_am and hour == 12:
        hour = 0
    # 如果既没有上午也没有下午标识，保持原值
    
    return hour * 3600 + minute * 60 + second

def get_video_duration(video_path: str) -> float:
    """获取视频总时长（秒）
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        视频时长（秒），失败时返回0
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return 0
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
    cap.release()
    
    return frame_count / fps if fps > 0 else 0

def seconds_to_time_str(seconds: int) -> str:
    """将秒数转换为 '分:秒' 格式的时间字符串
    
    Args:
        seconds: 秒数
        
    Returns:
        '分:秒' 格式的时间字符串
    """
    minutes = seconds // 60
    secs = seconds % 60
    return f"{minutes}:{secs:02d}"

def safe_trim_with_buffer(input_video_path: str, start_seconds: int, end_seconds: int, 
                         buffer_seconds: int, output_dir: str, 
                         skip_existing_check: bool = True) -> Optional[str]:
    """带缓冲区的安全视频裁剪
    
    Args:
        input_video_path: 输入视频路径
        start_seconds: 开始时间（秒）
        end_seconds: 结束时间（秒）
        buffer_seconds: 缓冲时间（秒）
        output_dir: 输出目录
        skip_existing_check: 是否在函数内部检查文件是否已存在
        
    Returns:
        输出文件路径，失败时返回None
    """
    
    # 获取视频总时长
    total_duration = get_video_duration(input_video_path)
    if total_duration == 0:
        raise ValueError(f"无法获取视频时长: {input_video_path}")
    
    print(f"视频总时长: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
    
    # 计算实际的开始和结束时间，添加缓冲区
    actual_start = max(0, start_seconds - buffer_seconds)
    actual_end = min(total_duration, end_seconds + buffer_seconds)
    
    # 确保最小时长
    min_duration = 30  # 最小30秒
    if actual_end - actual_start < min_duration:
        print(f"警告: 裁剪时长过短（{actual_end - actual_start:.1f}秒），调整缓冲区")
        center = (start_seconds + end_seconds) / 2
        half_min = min_duration / 2
        actual_start = max(0, center - half_min)
        actual_end = min(total_duration, center + half_min)
    
    print(f"原始时间段: {start_seconds}秒 - {end_seconds}秒")
    print(f"加缓冲后: {actual_start:.1f}秒 - {actual_end:.1f}秒 (缓冲±{buffer_seconds}秒)")
    
    # 转换为 '分:秒' 格式
    start_time = seconds_to_time_str(int(actual_start))
    end_time = seconds_to_time_str(int(actual_end))
    
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(input_video_path))[0]
    output_filename = f"{base_name}_{start_seconds}_{end_seconds}.mp4"
    
    # 检查输出文件是否已存在（如果启用了检查）
    output_path = os.path.join(output_dir, output_filename)
    if skip_existing_check and os.path.exists(output_path):
        print(f"输出文件已存在，跳过: {output_filename}")
        return output_path
    
    # 调用现有的trim_video函数（不添加视觉元素）
    return trim_video(input_video_path, start_time, end_time, 
                     output_dir=output_dir, add_visual_elements=False)

def validate_csv_data(csv_path: str) -> list:
    """验证CSV数据的有效性
    
    Args:
        csv_path: CSV文件路径
        
    Returns:
        问题列表，如果没有问题则返回空列表
    """
    issues = []
    
    if not os.path.exists(csv_path):
        issues.append(f"CSV文件不存在: {csv_path}")
        return issues
    
    with open(csv_path, 'r', encoding='GB2312') as f:
        reader = csv.DictReader(f)
        
        for row_num, row in enumerate(reader, start=2):  # 从第2行开始（第1行是标题）
            try:
                # 检查必要的列是否存在
                required_columns = ['日期', '开始时间', '结束时间', '视频名称']
                for col in required_columns:
                    if col not in row or not row[col].strip():
                        issues.append(f"第{row_num}行: 缺少必要的列 '{col}'")
                        continue
                
                # 解析时间 - 使用新的相对时间计算方法
                try:
                    start_seconds = calculate_relative_time(row['开始时间'], row['视频名称'])
                    end_seconds = calculate_relative_time(row['结束时间'], row['视频名称'])
                    
                    if start_seconds is None or end_seconds is None:
                        issues.append(f"第{row_num}行: 无法解析时间或视频文件名格式 - {row['开始时间']} - {row['结束时间']} - {row['视频名称']}")
                        continue
                    
                    # 检查时间逻辑
                    if end_seconds <= start_seconds:
                        issues.append(f"第{row_num}行: 结束时间({row['结束时间']})应该晚于开始时间({row['开始时间']})")
                    
                    # 检查时间段长度是否合理（不超过2小时，因为现在是相对时间）
                    duration = end_seconds - start_seconds
                    if duration > 2 * 3600:  # 2小时
                        issues.append(f"第{row_num}行: 时间段过长({duration/60:.1f}分钟)，可能存在错误")
                    elif duration < 10:  # 少于10秒
                        issues.append(f"第{row_num}行: 时间段过短({duration}秒)，可能存在错误")
                        
                except Exception as e:
                    issues.append(f"第{row_num}行: 时间解析错误 - {e}")
                
                # 检查视频文件名格式
                video_name = row['视频名称']
                if not video_name.endswith('.mp4'):
                    issues.append(f"第{row_num}行: 视频文件名应以.mp4结尾: {video_name}")
                
            except Exception as e:
                issues.append(f"第{row_num}行: 数据处理错误 - {e}")
    
    return issues

def process_csv_batch(csv_path: str, video_base_path: str, output_dir: str, 
                     buffer_seconds: int = 60, skip_existing: bool = True,
                     test_mode: bool = False, test_count: int = 3,
                     verbose: bool = True) -> dict:
    """批量处理CSV中定义的视频片段
    
    Args:
        csv_path: CSV文件路径
        video_base_path: 视频文件基础路径
        output_dir: 输出目录
        buffer_seconds: 缓冲时间（秒）
        skip_existing: 是否跳过已存在的文件
        test_mode: 测试模式，只处理前几个条目
        test_count: 测试模式下处理的条目数量
        verbose: 详细输出模式
        
    Returns:
        处理结果字典，包含成功、失败、跳过的文件信息
    """
    results = {
        'success': [],
        'failed': [],
        'skipped': []
    }
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查CSV文件是否存在
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
    
    with open(csv_path, 'r', encoding='GB2312') as f:
        reader = csv.DictReader(f)
        
        # 收集所有行数据
        all_rows = list(reader)
        total_rows = len(all_rows)
        
        # 如果是测试模式，只处理前几个条目
        if test_mode:
            all_rows = all_rows[:test_count]
            if verbose:
                print(f"⚠ 测试模式: 只处理前{len(all_rows)}个条目")
        
        current_row = 0
        for row in all_rows:
            current_row += 1
            if verbose:
                print(f"\n[{current_row}/{len(all_rows)}] 处理: {row['视频名称']}")
            
            try:
                # 使用新的相对时间计算方法
                start_seconds = calculate_relative_time(row['开始时间'], row['视频名称'])
                end_seconds = calculate_relative_time(row['结束时间'], row['视频名称'])
                
                if start_seconds is None or end_seconds is None:
                    error_msg = f'时间解析失败: {row["开始时间"]} - {row["结束时间"]}'
                    if verbose:
                        print(f"错误: {error_msg}")
                    results['failed'].append({
                        'file': row['视频名称'],
                        'error': error_msg
                    })
                    continue
                
                if verbose:
                    print(f"时间段: {row['开始时间']} ({start_seconds}秒) - {row['结束时间']} ({end_seconds}秒)")
                
                # 构建完整视频路径
                video_path = os.path.join(video_base_path, row['视频名称'])
                
                if not os.path.exists(video_path):
                    error_msg = f'视频文件不存在: {video_path}'
                    if verbose:
                        print(f"错误: {error_msg}")
                    results['failed'].append({
                        'file': row['视频名称'],
                        'error': error_msg
                    })
                    continue
                
                # 检查输出文件是否已存在
                if skip_existing:
                    base_name = os.path.splitext(os.path.basename(video_path))[0]
                    expected_output = os.path.join(output_dir, f"{base_name}_{start_seconds}_{end_seconds}.mp4")
                    if os.path.exists(expected_output):
                        if verbose:
                            print(f"⏭ 跳过: 文件已存在 {os.path.basename(expected_output)}")
                        results['skipped'].append({
                            'input': row['视频名称'],
                            'output': os.path.basename(expected_output),
                            'reason': '文件已存在'
                        })
                        continue
                
                # 执行裁剪（禁用内部文件存在检查，因为我们已经在上面检查过了）
                output_file = safe_trim_with_buffer(
                    video_path, start_seconds, end_seconds, 
                    buffer_seconds, output_dir, skip_existing_check=False
                )
                
                if output_file:
                    results['success'].append({
                        'input': row['视频名称'],
                        'output': os.path.basename(output_file),
                        'start': start_seconds,
                        'end': end_seconds,
                        'date': row['日期']
                    })
                    if verbose:
                        print(f"✓ 成功: {os.path.basename(output_file)}")
                else:
                    results['failed'].append({
                        'file': row['视频名称'],
                        'error': '裁剪失败'
                    })
                    if verbose:
                        print(f"✗ 失败: 裁剪过程出错")
                    
            except Exception as e:
                error_msg = str(e)
                if verbose:
                    print(f"✗ 错误: {error_msg}")
                results['failed'].append({
                    'file': row.get('视频名称', 'unknown'),
                    'error': error_msg
                })
    
    return results

def test_time_parsing():
    """测试时间解析功能"""
    print("测试中文时间解析功能:")
    print("=" * 60)
    
    # 测试绝对时间解析
    absolute_test_cases = [
        ("上午10时25分00秒", 37500),  # 10:25:00 AM = 10*3600 + 25*60 + 0
        ("下午3时40分00秒", 56400),   # 3:40:00 PM = 15*3600 + 40*60 + 0
        ("上午12时05分00秒", 300),    # 12:05:00 AM = 0*3600 + 5*60 + 0
        ("下午12时10分00秒", 43800),  # 12:10:00 PM = 12*3600 + 10*60 + 0
        ("上午1时50分00秒", 6600),    # 1:50:00 AM = 1*3600 + 50*60 + 0
        ("下午11时45分00秒", 84300)   # 11:45:00 PM = 23*3600 + 45*60 + 0
    ]
    
    print("1. 测试绝对时间解析:")
    all_passed = True
    for time_str, expected in absolute_test_cases:
        try:
            result = parse_chinese_time_to_seconds(time_str)
            status = '✓' if result == expected else '✗'
            if result != expected:
                all_passed = False
            print(f"  {status} {time_str} -> {result}秒 (期望: {expected}秒)")
        except Exception as e:
            print(f"  ✗ {time_str} -> 错误: {e}")
            all_passed = False
    
    print("\n2. 测试视频文件名时间戳解析:")
    filename_test_cases = [
        ("ch03_20250513145408.mp4", 53648),  # 14:54:08 = 14*3600 + 54*60 + 8
        ("ch01_20250414145900.mp4", 53940),  # 14:59:00 = 14*3600 + 59*60 + 0
        ("test_20250101120000.mp4", 43200),  # 12:00:00 = 12*3600
    ]
    
    for filename, expected in filename_test_cases:
        try:
            result = parse_video_filename_timestamp(filename)
            status = '✓' if result == expected else '✗'
            if result != expected:
                all_passed = False
            print(f"  {status} {filename} -> {result}秒 (期望: {expected}秒)")
        except Exception as e:
            print(f"  ✗ {filename} -> 错误: {e}")
            all_passed = False
    
    print("\n3. 测试相对时间计算:")
    # 测试相对时间计算
    # ch03_20250513145408.mp4 开始于 14:54:08 (53648秒)
    # 下午2时54分10秒 = 14:54:10 (53650秒)
    # 相对时间应该是 53650 - 53648 = 2秒
    relative_test_cases = [
        ("下午2时54分10秒", "ch03_20250513145408.mp4", 2),    # 53650 - 53648 = 2
        ("下午2时56分09秒", "ch03_20250513145408.mp4", 121),  # 53769 - 53648 = 121
    ]
    
    for time_str, filename, expected in relative_test_cases:
        try:
            result = calculate_relative_time(time_str, filename)
            status = '✓' if result == expected else '✗'
            if result != expected:
                all_passed = False
            print(f"  {status} {time_str} 相对于 {filename} -> {result}秒 (期望: {expected}秒)")
        except Exception as e:
            print(f"  ✗ {time_str} 相对于 {filename} -> 错误: {e}")
            all_passed = False
    
    print("=" * 60)
    print(f"测试结果: {'全部通过' if all_passed else '存在失败'}")
    return all_passed

def main_csv_batch():
    """CSV批处理的主函数"""
    print("CSV批量视频裁剪工具")
    print("=" * 40)
    
    # 加载配置文件
    config = load_csv_batch_config()
    
    # 从配置文件提取参数
    csv_path = config['paths']['csv_file']
    video_base_path = config['paths']['video_base_path']
    output_dir = config['paths']['output_dir']
    buffer_seconds = config['trimming']['buffer_seconds']
    skip_existing = config['trimming']['skip_existing']
    show_progress = config['processing']['show_progress']
    add_visual_elements = config['processing']['add_visual_elements']
    test_mode = config['debug']['test_mode']
    test_count = config['debug']['test_count']
    verbose = config['debug']['verbose']
    
    # 显示配置
    print(f"CSV文件: {csv_path}")
    print(f"视频路径: {video_base_path}")
    print(f"输出目录: {output_dir}")
    print(f"缓冲时间: {buffer_seconds}秒")
    print(f"跳过已存在文件: {skip_existing}")
    print(f"添加视觉元素: {add_visual_elements}")
    if test_mode:
        print(f"⚠ 测试模式: 只处理前{test_count}个条目")
    print("=" * 40)
    
    # 检查文件和路径是否存在
    if not os.path.exists(csv_path):
        print(f"错误: CSV文件不存在: {csv_path}")
        return
    
    if not os.path.exists(video_base_path):
        print(f"错误: 视频基础路径不存在: {video_base_path}")
        return
    
    try:
        # 验证CSV数据
        print("验证CSV数据...")
        issues = validate_csv_data(csv_path)
        if issues:
            print("发现数据问题:")
            for issue in issues:
                print(f"  ⚠ {issue}")
            
            user_input = input("\n是否继续处理? (y/n): ").strip().lower()
            if user_input != 'y':
                print("用户取消处理")
                return
            print()
        else:
            print("✓ CSV数据验证通过")
        
        # 开始处理
        print("开始批量处理...")
        start_time = time.time()
        
        results = process_csv_batch(
            csv_path, video_base_path, output_dir, buffer_seconds,
            skip_existing, test_mode, test_count, verbose
        )
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # 输出结果统计
        print("\n" + "=" * 40)
        print("处理完成!")
        print(f"总耗时: {elapsed:.1f}秒 ({elapsed/60:.1f}分钟)")
        print(f"成功: {len(results['success'])} 个")
        print(f"失败: {len(results['failed'])} 个")
        print(f"跳过: {len(results['skipped'])} 个")
        
        # 显示成功的文件
        if results['success']:
            print(f"\n成功处理的文件:")
            for item in results['success']:
                print(f"  ✓ {item['input']} -> {item['output']}")
        
        # 显示失败的文件
        if results['failed']:
            print(f"\n失败的文件:")
            for item in results['failed']:
                print(f"  ✗ {item['file']}: {item['error']}")
        
        print("=" * 40)
        
    except Exception as e:
        print(f"批处理过程中出现错误: {e}")

def main():
    """直接从控制台输入参数并执行视频裁剪"""
    print("视频裁剪工具")
    print("=============")
    
    # 获取视频文件路径
    default_video = '/home/<USER>/data/xiwu/ch03_20250513145408.mp4'
    input_prompt = f"请输入视频文件路径 (默认: {default_video}): "
    input_video = input(input_prompt).strip()
    if not input_video:
        input_video = default_video
    
    # 检查视频文件是否存在
    if not os.path.exists(input_video):
        print(f"错误: 视频文件 '{input_video}' 不存在")
        return
    
    # 获取开始和结束时间
    start_time = input("请输入开始时间 (格式: 分:秒，例如 1:30): ").strip()
    if not start_time:
        start_time = "0:00"
        print(f"使用默认开始时间: {start_time}")
    
    end_time = input("请输入结束时间 (格式: 分:秒，例如 2:45): ").strip()
    if not end_time:
        end_time = "1:00"
        print(f"使用默认结束时间: {end_time}")
    
    # 是否添加视觉元素
    add_visuals_input = input("是否添加网格和多边形? (y/n, 默认: y): ").strip().lower()
    add_visual_elements = add_visuals_input != 'n'
    
    # 是否使用自定义ROI
    use_custom_roi = False
    if add_visual_elements:  # 只有在添加视觉元素时才询问ROI
        use_custom_roi = input("是否使用自定义ROI? (y/n, 默认: n): ").strip().lower() == 'y'
    
    roi_points = None
    if use_custom_roi:
        print("请输入自定义ROI点坐标，格式为: [[x1,y1], [x2,y2], ..., [xn,yn]]")
        print("或直接按回车使用默认值")
        roi_input = input("ROI坐标: ").strip()
        if roi_input:
            try:
                roi_points = eval(roi_input)
                print(f"已设置自定义ROI: {roi_points}")
            except Exception as e:
                print(f"解析ROI时出错: {e}")
                print("将使用默认ROI")
    
    # 获取输出目录
    default_output = "data/output/trimmed"
    output_dir = input(f"请输入输出目录 (默认: {default_output}): ").strip()
    if not output_dir:
        output_dir = default_output
    
    # 执行视频裁剪
    print("\n开始执行视频裁剪...")
    output_video = trim_video(input_video, start_time, end_time, roi_points, output_dir, add_visual_elements)
    
    if output_video:
        print(f"\n裁剪完成！输出文件: {output_video}")
    
def choose_mode():
    """选择运行模式"""
    print("视频裁剪工具 - 选择运行模式")
    print("=" * 40)
    print("1. CSV批量处理")
    print("2. 测试时间解析功能")
    print("3. 交互式单个视频裁剪")
    print("=" * 40)
    
    while True:
        choice = input("请选择模式 (1-3): ").strip()
        if choice == '1':
            main_csv_batch()
            break
        elif choice == '2':
            test_time_parsing()
            break
        elif choice == '3':
            main()
            break
        else:
            print("无效选择，请输入 1、2 或 3")

def parse_video_filename_timestamp(filename: str) -> Optional[int]:
    """从视频文件名中解析时间戳并转换为秒数
    
    Args:
        filename: 视频文件名，格式如 'ch03_20250513145408.mp4'
                 其中 20250513145408 表示 2025年05月13日14时54分08秒
        
    Returns:
        时间戳对应的秒数（从午夜0点开始计算），解析失败时返回None
    """
    import re
    
    # 提取文件名中的时间戳部分
    # 匹配格式：YYYYMMDDHHMMSS
    pattern = r'(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})'
    match = re.search(pattern, filename)
    
    if not match:
        return None
    
    year, month, day, hour, minute, second = map(int, match.groups())
    
    # 验证时间有效性
    if not (1 <= month <= 12 and 1 <= day <= 31 and 0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59):
        return None
    
    # 转换为从午夜0点开始的秒数
    return hour * 3600 + minute * 60 + second

def calculate_relative_time(chinese_time_str: str, video_filename: str) -> Optional[int]:
    """计算相对于视频开始时间的相对秒数
    
    Args:
        chinese_time_str: 中文时间格式，如 "下午2时54分10秒"
        video_filename: 视频文件名，用于解析视频开始时间
        
    Returns:
        相对于视频开始时间的秒数，失败时返回None
    """
    # 解析中文时间为绝对秒数
    try:
        absolute_seconds = parse_chinese_time_to_seconds(chinese_time_str)
    except ValueError as e:
        print(f"解析中文时间失败: {e}")
        return None
    
    # 解析视频文件名中的开始时间
    video_start_seconds = parse_video_filename_timestamp(video_filename)
    if video_start_seconds is None:
        print(f"无法从视频文件名解析开始时间: {video_filename}")
        return None
    
    # 计算相对时间
    relative_seconds = absolute_seconds - video_start_seconds
    
    # 处理跨日情况（如果相对时间为负数，可能是跨日了）
    if relative_seconds < 0:
        # 假设跨日，加上24小时
        relative_seconds += 24 * 3600
    
    return relative_seconds

if __name__ == "__main__":
    # 在IDE中直接运行时的模式选择
    choose_mode() 