"""
几何计算工具模块

提供分析线绘制所需的几何计算功能，包括：
- 线段与矩形的裁剪
- 线段与多边形的裁剪
- 线段分割算法
"""

import math
import numpy as np
from typing import List, Tuple, Optional, Union
import logging

logger = logging.getLogger(__name__)


def clip_line_to_rectangle(start_point: Tuple[float, float], 
                          end_point: Tuple[float, float],
                          rect_bounds: List[List[int]]) -> Optional[Tuple[Tuple[float, float], Tuple[float, float]]]:
    """
    使用Cohen-Sutherland算法将线段裁剪到矩形区域内
    
    Args:
        start_point: 线段起点 (x, y)
        end_point: 线段终点 (x, y)
        rect_bounds: 矩形边界 [[y1, x1], [y2, x2]]
        
    Returns:
        裁剪后的线段 ((x1, y1), (x2, y2))，如果线段完全在矩形外则返回None
    """
    # 转换矩形格式：[[y1, x1], [y2, x2]] -> (x_min, y_min, x_max, y_max)
    y1, x1 = rect_bounds[0]
    y2, x2 = rect_bounds[1]
    x_min, x_max = min(x1, x2), max(x1, x2)
    y_min, y_max = min(y1, y2), max(y1, y2)
    
    # Cohen-Sutherland区域编码
    INSIDE = 0  # 0000
    LEFT = 1    # 0001
    RIGHT = 2   # 0010
    BOTTOM = 4  # 0100
    TOP = 8     # 1000
    
    def compute_outcode(x: float, y: float) -> int:
        """计算点的区域编码"""
        code = INSIDE
        if x < x_min:
            code |= LEFT
        elif x > x_max:
            code |= RIGHT
        if y < y_min:
            code |= BOTTOM
        elif y > y_max:
            code |= TOP
        return code
    
    x0, y0 = start_point
    x1, y1 = end_point
    
    outcode0 = compute_outcode(x0, y0)
    outcode1 = compute_outcode(x1, y1)
    
    while True:
        if not (outcode0 | outcode1):
            # 两点都在矩形内
            return ((x0, y0), (x1, y1))
        elif outcode0 & outcode1:
            # 两点都在矩形同一侧外
            return None
        else:
            # 至少有一点在矩形外，需要裁剪
            outcode_out = outcode0 if outcode0 else outcode1
            
            # 计算交点
            if outcode_out & TOP:
                x = x0 + (x1 - x0) * (y_max - y0) / (y1 - y0)
                y = y_max
            elif outcode_out & BOTTOM:
                x = x0 + (x1 - x0) * (y_min - y0) / (y1 - y0)
                y = y_min
            elif outcode_out & RIGHT:
                y = y0 + (y1 - y0) * (x_max - x0) / (x1 - x0)
                x = x_max
            elif outcode_out & LEFT:
                y = y0 + (y1 - y0) * (x_min - x0) / (x1 - x0)
                x = x_min
            
            # 更新点坐标和区域编码
            if outcode_out == outcode0:
                x0, y0 = x, y
                outcode0 = compute_outcode(x0, y0)
            else:
                x1, y1 = x, y
                outcode1 = compute_outcode(x1, y1)


def clip_line_to_polygon(start_point: Tuple[float, float], 
                        end_point: Tuple[float, float],
                        polygon_points: List[List[int]]) -> Optional[Tuple[Tuple[float, float], Tuple[float, float]]]:
    """
    使用Sutherland-Hodgman算法将线段裁剪到多边形区域内
    
    Args:
        start_point: 线段起点 (x, y)
        end_point: 线段终点 (x, y)
        polygon_points: 多边形顶点 [[x1, y1], [x2, y2], ...]
        
    Returns:
        裁剪后的线段 ((x1, y1), (x2, y2))，如果线段完全在多边形外则返回None
    """
    def point_in_polygon(point: Tuple[float, float], polygon: List[List[int]]) -> bool:
        """判断点是否在多边形内（射线法）"""
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def line_intersect_edge(p1: Tuple[float, float], p2: Tuple[float, float],
                           edge_start: Tuple[float, float], edge_end: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """计算线段与多边形边的交点"""
        x1, y1 = p1
        x2, y2 = p2
        x3, y3 = edge_start
        x4, y4 = edge_end
        
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return None  # 平行线
        
        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
        u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom
        
        if 0 <= t <= 1 and 0 <= u <= 1:
            x = x1 + t * (x2 - x1)
            y = y1 + t * (y2 - y1)
            return (x, y)
        
        return None
    
    # 简化实现：检查起点和终点是否在多边形内
    start_inside = point_in_polygon(start_point, polygon_points)
    end_inside = point_in_polygon(end_point, polygon_points)
    
    if start_inside and end_inside:
        # 两点都在多边形内
        return (start_point, end_point)
    elif not start_inside and not end_inside:
        # 检查线段是否与多边形有交点
        intersections = []
        n = len(polygon_points)
        
        for i in range(n):
            edge_start = tuple(polygon_points[i])
            edge_end = tuple(polygon_points[(i + 1) % n])
            intersection = line_intersect_edge(start_point, end_point, edge_start, edge_end)
            if intersection:
                intersections.append(intersection)
        
        if len(intersections) >= 2:
            # 取最远的两个交点
            return (intersections[0], intersections[-1])
        else:
            return None
    else:
        # 一点在内，一点在外，找交点
        intersections = []
        n = len(polygon_points)
        
        for i in range(n):
            edge_start = tuple(polygon_points[i])
            edge_end = tuple(polygon_points[(i + 1) % n])
            intersection = line_intersect_edge(start_point, end_point, edge_start, edge_end)
            if intersection:
                intersections.append(intersection)
        
        if intersections:
            if start_inside:
                return (start_point, intersections[0])
            else:
                return (intersections[0], end_point)
        else:
            return None


def clip_line_to_roi(start_point: Tuple[float, float], 
                    end_point: Tuple[float, float],
                    roi: List) -> Optional[Tuple[Tuple[float, float], Tuple[float, float]]]:
    """
    将线段裁剪到ROI区域内
    
    Args:
        start_point: 线段起点 (x, y)
        end_point: 线段终点 (x, y)
        roi: ROI区域，支持矩形和多边形格式
        
    Returns:
        裁剪后的线段，如果线段完全在ROI外则返回None
    """
    if not roi:
        return (start_point, end_point)
    
    if len(roi) == 2 and len(roi[0]) == 2:
        # 矩形格式: [[y1, x1], [y2, x2]]
        return clip_line_to_rectangle(start_point, end_point, roi)
    elif len(roi) >= 3:
        # 多边形格式: [[x1, y1], [x2, y2], [x3, y3], ...]
        return clip_line_to_polygon(start_point, end_point, roi)
    else:
        logger.warning(f"不支持的ROI格式: {roi}")
        return (start_point, end_point)


def calculate_line_length(start_point: Tuple[float, float], 
                         end_point: Tuple[float, float]) -> float:
    """计算线段长度"""
    x1, y1 = start_point
    x2, y2 = end_point
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


def extend_line_to_max_length(center_point: Tuple[float, float], 
                             angle: float, 
                             max_length: float) -> Tuple[Tuple[float, float], Tuple[float, float]]:
    """
    基于中心点和角度生成指定最大长度的线段
    
    Args:
        center_point: 中心点 (x, y)
        angle: 线段角度（弧度）
        max_length: 最大长度
        
    Returns:
        线段的起点和终点 ((x1, y1), (x2, y2))
    """
    center_x, center_y = center_point
    half_length = max_length / 2
    
    dx = math.cos(angle) * half_length
    dy = math.sin(angle) * half_length
    
    start_point = (center_x - dx, center_y - dy)
    end_point = (center_x + dx, center_y + dy)
    
    return (start_point, end_point)


def segment_line(start_point: Tuple[float, float],
                end_point: Tuple[float, float],
                optimal_length: float,
                interval_distance: float,
                line_angle: float) -> List[Tuple[Tuple[float, float], Tuple[float, float]]]:
    """
    将长线段分割成多个较短的线段

    智能分割逻辑：
    1. 从起点开始，沿着线段方向按optimal_length长度分割
    2. 段与段之间有interval_distance的间隔
    3. 确保至少一个分割线的起点与原始线起点匹配
    4. 确保至少一个分割线的终点与原始线终点匹配（如果可能）
    5. 分割线保持与原始线相同的角度/方向

    Args:
        start_point: 原始线段起点
        end_point: 原始线段终点
        optimal_length: 每段的最佳长度
        interval_distance: 段间间隔距离（沿线段方向）
        line_angle: 线段角度（弧度，仅用于日志，实际使用线段实际方向）

    Returns:
        分割后的线段列表
    """
    # 统一精度：保留1位小数
    line_length = round(calculate_line_length(start_point, end_point), 1)

    if line_length <= optimal_length:
        # 不需要分割
        return [(start_point, end_point)]

    # 计算实际线段方向的单位向量（而不是使用传入的line_angle）
    dx_total = end_point[0] - start_point[0]
    dy_total = end_point[1] - start_point[1]
    dx_unit = dx_total / line_length
    dy_unit = dy_total / line_length

    # 优化的分割段数计算逻辑
    segment_length_with_interval = optimal_length + interval_distance
    complete_segments = int(line_length / segment_length_with_interval)  # 完整段数
    remaining_length = line_length - complete_segments * segment_length_with_interval  # 剩余长度

    # 剩余长度处理规则
    if remaining_length >= optimal_length:
        # 剩余长度 >= 最佳长度：添加一个额外段
        segment_count = complete_segments + 1
        add_extra_segment = True
    elif remaining_length >= optimal_length / 2:
        # 剩余长度 >= 最佳长度的一半：添加一个额外段
        segment_count = complete_segments + 1
        add_extra_segment = True
    else:
        # 剩余长度 < 最佳长度的一半：丢弃剩余部分
        segment_count = complete_segments
        add_extra_segment = False

    segment_count = max(1, segment_count)  # 至少要有1段

    logger.debug(f"开始智能分割: 原长度={line_length:.1f}px, 最佳长度={optimal_length}px, 间隔={interval_distance}px")
    logger.debug(f"完整段数计算: {line_length:.1f} / ({optimal_length} + {interval_distance}) = {complete_segments}段")
    logger.debug(f"剩余长度: {remaining_length:.1f}px")
    logger.debug(f"剩余长度处理: {'添加额外段' if add_extra_segment else '丢弃剩余部分'}")
    logger.debug(f"最终分割段数: {segment_count}段")
    logger.debug(f"线段方向向量: dx={dx_unit:.4f}, dy={dy_unit:.4f}")

    result_segments = []
    current_position = 0.0  # 当前位置（沿线段方向的距离）

    for i in range(segment_count):
        # 计算当前段的起点
        segment_start_x = round(start_point[0] + current_position * dx_unit, 1)
        segment_start_y = round(start_point[1] + current_position * dy_unit, 1)
        segment_start = (segment_start_x, segment_start_y)

        # 计算当前段的终点位置
        segment_end_position = current_position + optimal_length

        # 检查是否是最后一段或超出原始线段的终点
        if i == segment_count - 1 or segment_end_position >= line_length:
            # 最后一段：终点设为原始线段的终点
            segment_end = (round(end_point[0], 1), round(end_point[1], 1))
            logger.debug(f"段{i}: 最后一段，终点设为原始线终点")
        else:
            # 计算段的终点
            segment_end_x = round(start_point[0] + segment_end_position * dx_unit, 1)
            segment_end_y = round(start_point[1] + segment_end_position * dy_unit, 1)
            segment_end = (segment_end_x, segment_end_y)

        # 验证段的长度
        actual_segment_length = round(calculate_line_length(segment_start, segment_end), 1)

        # 只有当段长度大于最小阈值时才添加
        if actual_segment_length >= 10.0:  # 最小长度阈值
            result_segments.append((segment_start, segment_end))
            logger.debug(f"段{i}: 起点=({segment_start[0]:.1f},{segment_start[1]:.1f}), "
                        f"终点=({segment_end[0]:.1f},{segment_end[1]:.1f}), 长度={actual_segment_length:.1f}px")
        else:
            logger.debug(f"段{i}: 长度过短({actual_segment_length:.1f}px)，跳过")

        # 移动到下一段的起点位置
        current_position = segment_end_position + interval_distance

        # 如果当前段的终点已经是原始线段的终点，则停止分割
        if segment_end == (round(end_point[0], 1), round(end_point[1], 1)):
            break

    # 验证分割结果
    if result_segments:
        first_segment = result_segments[0]
        last_segment = result_segments[-1]

        # 检查第一段的起点是否与原始线起点匹配
        start_match = (abs(first_segment[0][0] - start_point[0]) < 0.1 and
                      abs(first_segment[0][1] - start_point[1]) < 0.1)

        # 检查最后一段的终点是否与原始线终点匹配
        end_match = (abs(last_segment[1][0] - end_point[0]) < 0.1 and
                    abs(last_segment[1][1] - end_point[1]) < 0.1)

        logger.debug(f"分割验证: 起点匹配={start_match}, 终点匹配={end_match}, 总段数={len(result_segments)}")

        if not start_match:
            logger.warning("分割结果验证失败: 第一段起点与原始线起点不匹配")
        if not end_match:
            logger.warning("分割结果验证失败: 最后一段终点与原始线终点不匹配")

    logger.debug(f"线段分割完成: 原长度={line_length:.1f}px, 分割为{len(result_segments)}段")

    return result_segments
