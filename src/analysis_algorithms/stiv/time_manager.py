"""
时间管理模块

解决核心问题：
- 4.2 视频时间段选择问题：内置时间段选择功能，无需外部预处理
"""

import logging
from typing import Optional, Tuple
import cv2
import numpy as np
from pathlib import Path

logger = logging.getLogger(__name__)


class TimeRestrictedLoader:
    """时间限制的视频加载器
    
    包装原始的Loader，提供时间段选择功能
    """
    
    def __init__(self, video_path: str, start_time: Optional[float] = None, 
                 end_time: Optional[float] = None):
        """初始化时间限制加载器
        
        Args:
            video_path: 视频文件路径
            start_time: 开始时间（秒），None表示从头开始
            end_time: 结束时间（秒），None表示到结尾
        """
        self.video_path = video_path
        self.start_time = start_time
        self.end_time = end_time
        
        # 初始化视频捕获
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频属性
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.duration = self.total_frames / self.fps
        
        # 计算帧范围
        self.start_frame = 0 if start_time is None else int(start_time * self.fps)
        self.end_frame = self.total_frames if end_time is None else int(end_time * self.fps)
        
        # 验证时间范围
        self._validate_time_range()
        
        # 设置当前位置
        self.current_frame = self.start_frame
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.start_frame)
        
        logger.info(f"时间限制加载器初始化完成:")
        logger.info(f"  视频: {Path(video_path).name}")
        logger.info(f"  总时长: {self.duration:.2f}秒 ({self.total_frames}帧)")
        logger.info(f"  分析时段: {self.start_time or 0:.2f}s - {self.end_time or self.duration:.2f}s")
        logger.info(f"  分析帧范围: {self.start_frame} - {self.end_frame} (共{self.end_frame - self.start_frame}帧)")
    
    def _validate_time_range(self):
        """验证时间范围的有效性"""
        if self.start_frame < 0:
            self.start_frame = 0
            logger.warning("开始时间小于0，已调整为0")
        
        if self.end_frame > self.total_frames:
            self.end_frame = self.total_frames
            logger.warning(f"结束时间超出视频长度，已调整为{self.duration:.2f}秒")
        
        if self.start_frame >= self.end_frame:
            raise ValueError(f"无效的时间范围: 开始帧({self.start_frame}) >= 结束帧({self.end_frame})")
        
        # 检查最小时长
        min_frames = int(5 * self.fps)  # 最少5秒
        if self.end_frame - self.start_frame < min_frames:
            logger.warning(f"分析时段过短({(self.end_frame - self.start_frame) / self.fps:.2f}秒)，建议至少5秒")
    
    def has_images(self) -> bool:
        """检查是否还有图像可读取"""
        return self.current_frame < self.end_frame
    
    def read(self) -> Optional[np.ndarray]:
        """读取下一帧图像
        
        Returns:
            图像数组，如果没有更多帧则返回None
        """
        if not self.has_images():
            return None
        
        ret, frame = self.cap.read()
        if not ret:
            logger.warning(f"无法读取帧 {self.current_frame}")
            return None
        
        self.current_frame += 1
        return frame
    
    def get_progress(self) -> float:
        """获取处理进度（0-1）"""
        total_frames = self.end_frame - self.start_frame
        processed_frames = self.current_frame - self.start_frame
        return processed_frames / total_frames if total_frames > 0 else 1.0
    
    def get_current_time(self) -> float:
        """获取当前时间（秒）"""
        return self.current_frame / self.fps
    
    def release(self):
        """释放资源"""
        if self.cap:
            self.cap.release()
    
    def __del__(self):
        """析构函数"""
        self.release()


class TimeManager:
    """时间管理器
    
    负责处理视频时间段选择和时间相关的配置
    """
    
    def __init__(self, start_time: Optional[float] = None, end_time: Optional[float] = None):
        """初始化时间管理器
        
        Args:
            start_time: 开始时间（秒），None表示从头开始
            end_time: 结束时间（秒），None表示到结尾
        """
        self.start_time = start_time
        self.end_time = end_time
    
    def create_loader(self, video_path: str) -> TimeRestrictedLoader:
        """创建时间限制的加载器
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            TimeRestrictedLoader实例
        """
        return TimeRestrictedLoader(video_path, self.start_time, self.end_time)
    
    def apply_time_selection(self, original_loader) -> TimeRestrictedLoader:
        """对现有加载器应用时间选择
        
        Args:
            original_loader: 原始加载器（awive.loader.Loader）
            
        Returns:
            时间限制的加载器
        """
        # 如果没有时间限制，返回原始加载器的包装
        if self.start_time is None and self.end_time is None:
            # 创建一个不限制时间的TimeRestrictedLoader
            return TimeRestrictedLoader(original_loader.video_fp, None, None)
        
        # 创建时间限制的加载器
        return TimeRestrictedLoader(original_loader.video_fp, self.start_time, self.end_time)
    
    def get_time_info(self) -> dict:
        """获取时间配置信息"""
        return {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "has_time_restriction": self.start_time is not None or self.end_time is not None
        }
    
    @staticmethod
    def from_config(config: dict) -> 'TimeManager':
        """从配置创建时间管理器
        
        Args:
            config: 配置字典
            
        Returns:
            TimeManager实例
        """
        time_range = config.get("dataset", {}).get("time_range", {})
        start_time = time_range.get("start_time")
        end_time = time_range.get("end_time")
        
        return TimeManager(start_time, end_time)
    
    def validate_time_range(self, video_path: str) -> Tuple[bool, str]:
        """验证时间范围是否有效
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 创建临时加载器进行验证
            temp_loader = TimeRestrictedLoader(video_path, self.start_time, self.end_time)
            temp_loader.release()
            return True, ""
        except Exception as e:
            return False, str(e)


def create_time_manager_from_legacy_config(config: dict) -> TimeManager:
    """从旧版配置创建时间管理器（向后兼容）
    
    Args:
        config: 旧版配置字典
        
    Returns:
        TimeManager实例
    """
    # 旧版配置通常没有时间范围设置，返回无限制的时间管理器
    return TimeManager(None, None)
