"""
STIV主处理器

整合所有模块，提供统一的STIV处理接口
"""

import time
import logging
from typing import Dict, List, Optional, Callable, Tuple
from pathlib import Path
import cv2
import numpy as np

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from utils.config_manager import STIVConfig
from .coordinate_system import CoordinateSystem
from .time_manager import TimeManager
from .analysis_lines import AnalysisLines
from .stiv_core import STIGenerator, STIFilter, MotionCalculator

logger = logging.getLogger(__name__)


class STIVProcessor:
    """STIV v2主处理器
    
    解决了原版本的四个核心问题：
    1. 坐标系统兼容性问题
    2. 视频时间段选择问题  
    3. 像素比例自动计算问题
    4. 分析线方向适配问题
    """
    
    def __init__(self, config: STIVConfig, video_path: str = ""):
        """初始化STIV处理器

        Args:
            config: STIV配置
            video_path: 视频文件路径
        """
        self.config = config
        self.video_path = video_path

        # 初始化各个组件
        self._init_components()

        logger.info("STIV处理器初始化完成")
        logger.info(f"视频路径: {video_path}")
        logger.info(f"算法方法: {config.algorithm.method}")
        logger.info(f"分析线模式: {config.analysis_lines.mode}")
        logger.info(f"坐标系统: {config.coordinate_system.type}")
    
    def _init_components(self):
        """初始化各个组件"""
        # 坐标系统
        self.coordinate_system = CoordinateSystem(
            self.config.coordinate_system.type,
            self.config.coordinate_system.calibration
        )

        # 时间管理器（暂时使用None，因为新配置结构中没有时间范围）
        self.time_manager = TimeManager(None, None)

        # 分析线管理器
        lines_config = {
            "mode": self.config.analysis_lines.mode,
            "flow_direction": self.config.analysis_lines.flow_direction,
            "line_count": self.config.analysis_lines.line_count,
            "line_spacing": self.config.analysis_lines.line_spacing,
            "line_length": self.config.analysis_lines.line_length,
            "center_point": self.config.analysis_lines.center_point,
            "lines": self.config.analysis_lines.lines,
            "lines_range": self.config.analysis_lines.lines_range,
            # 新增：绘制模式配置
            "drawing_mode": getattr(self.config.analysis_lines, "drawing_mode", "default"),
            "optimal_length": getattr(self.config.analysis_lines, "optimal_length", 200),
            "interval_distance": getattr(self.config.analysis_lines, "interval_distance", 30)
        }
        self.analysis_lines = AnalysisLines(self.config.analysis_lines.mode, lines_config)

        # 核心算法组件
        self.sti_generator = STIGenerator(self.coordinate_system, self.analysis_lines)
        self.sti_filter = STIFilter(self.config.algorithm)

        # 传递分析线配置给运动计算器
        analysis_lines_config = {
            "flow_direction": self.config.analysis_lines.flow_direction,
            "mode": self.config.analysis_lines.mode
        }
        self.motion_calculator = MotionCalculator(self.coordinate_system, self.config.algorithm, analysis_lines_config)

    def process(self, preprocessing_func: Optional[Callable] = None,
               method: Optional[str] = None, save_debug: bool = False) -> Dict[str, Dict[str, float]]:
        """执行STIV分析

        Args:
            preprocessing_func: 图像预处理函数
            method: 运动计算方法，"fft" 或 "gmt"，None时使用配置中的方法
            save_debug: 是否保存调试图像

        Returns:
            分析结果字典
        """
        # 使用配置中的方法（如果未指定）
        if method is None:
            method = self.config.algorithm.method

        logger.info("开始STIV分析...")
        logger.info(f"使用运动计算方法: {method}")
        total_start_time = time.time()

        # 清空之前的分析数据
        self.motion_calculator.analysis_data = []
        
        # 1. 创建时间限制的加载器
        if not self.video_path:
            raise ValueError("未指定视频文件路径")

        loader = self.time_manager.create_loader(self.video_path)
        fps = loader.fps
        
        try:
            # 2. 生成STI
            logger.info("步骤1: 生成Space-Time Images")
            # 获取ROI信息用于分析线裁剪
            roi = getattr(self.config.preprocessing, 'roi', None)
            stis, first_original_frame = self.sti_generator.generate_stis(loader, preprocessing_func, roi)

            if not stis:
                raise ValueError("没有生成任何STI图像")

            # 3. 处理每个STI
            results = {}
            velocities = []
            angle_diffs = []  # 存储角度差异数据
            debug_data = []  # 存储调试数据，稍后统一处理

            for i, sti in enumerate(stis):
                logger.info(f"步骤2: 处理STI {i+1}/{len(stis)}")

                # 滤波
                filtered_sti = self.sti_filter.filter_sti(sti, i)

                # 计算运动
                if method == "fft":
                    velocity, mask, angle_diff = self.motion_calculator.calculate_velocity_fft(filtered_sti, fps, i)
                elif method == "gmt":
                    velocity, mask, angle_diff = self.motion_calculator.calculate_velocity_gmt(filtered_sti, fps)
                else:
                    raise ValueError(f"不支持的运动计算方法: {method}")

                velocities.append(velocity)
                angle_diffs.append(angle_diff)
                results[str(i)] = {"velocity": velocity, "angle_diff": angle_diff}

                logger.info(f"STI {i}: 速度 = {velocity:.3f} m/s")

                # 存储调试数据，稍后处理
                if save_debug:
                    debug_data.append({
                        'sti_id': i,
                        'original_sti': sti,
                        'filtered_sti': filtered_sti,
                        'mask': mask
                    })

            # 4. 保存调试图像（在所有流速计算完成后）
            if save_debug and debug_data:
                for data in debug_data:
                    self._save_debug_images(data['sti_id'], data['original_sti'],
                                          data['filtered_sti'], data['mask'],
                                          first_original_frame, velocities, angle_diffs)

                # 在所有调试图像保存完成后，生成流速数据箱式图
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                debug_dir = Path("data/image/stiv_debug")
                self._save_velocity_boxplot(debug_dir, velocities, timestamp)

            # 5. 计算统计信息
            mean_velocity = np.mean(velocities) if velocities else 0.0

            # 计算定制化平均流速
            custom_avg_velocity = self._calculate_custom_average_velocity(velocities)
            
            # 计算去除异常值后的平均流速
            velocity_without_outliers = self._calculate_average_velocity_without_outliers(velocities)

            results["summary"] = {
                "mean_velocity": mean_velocity,
                "velocity_std": np.std(velocities) if len(velocities) > 1 else 0.0,
                "line_count": len(velocities),
                "method": method,
                "custom_average_velocity": custom_avg_velocity,
                "average_without_outliers": velocity_without_outliers
            }
            
            total_time = time.time() - total_start_time
            
            # 输出去除异常值后的平均流速信息（在两行主要日志之间）
            if velocity_without_outliers["normal_count"] > 0:
                logger.info(f"  去除异常值后的平均流速: {velocity_without_outliers['average_without_outliers']:.3f} m/s "
                           f"(基于{velocity_without_outliers['normal_count']}个正常值，排除了{velocity_without_outliers['outlier_count']}个异常值)")
            else:
                logger.info(f"  去除异常值后的平均流速: 无法计算 (没有正常数据)")

            # 输出集中式参数汇总
            if hasattr(self.motion_calculator, 'analysis_data') and self.motion_calculator.analysis_data:
                self.motion_calculator._output_analysis_summary(self.motion_calculator.analysis_data)

            logger.info(f"STIV分析完成: 平均速度 = {mean_velocity:.3f} m/s, 耗时 {total_time:.2f}秒")
            
            return results
            
        finally:
            loader.release()

    def _calculate_average_velocity_without_outliers(self, velocities: List[float]) -> Dict:
        """计算去除异常值后的平均流速
        
        使用箱式图的异常值检测逻辑（1.5×IQR规则）：
        - 计算Q1（25%分位数）、Q3（75%分位数）和IQR
        - 异常值边界：Q1 - 1.5×IQR 到 Q3 + 1.5×IQR
        - 排除异常值后计算剩余正常值的平均值
        
        Args:
            velocities: 原始流速数据列表
            
        Returns:
            包含去除异常值后平均流速结果的字典
        """
        if not velocities:
            return {
                "average_without_outliers": 0.0,
                "normal_count": 0,
                "outlier_count": 0,
                "total_count": 0,
                "q1": 0.0,
                "q3": 0.0,
                "iqr": 0.0,
                "lower_bound": 0.0,
                "upper_bound": 0.0
            }
        
        try:
            import numpy as np
            
            # 过滤掉零值数据（与箱式图逻辑一致）
            valid_velocities = [v for v in velocities if v != 0.0 and v != -0.0]
            
            if not valid_velocities:
                return {
                    "average_without_outliers": 0.0,
                    "normal_count": 0,
                    "outlier_count": 0,
                    "total_count": len(velocities),
                    "q1": 0.0,
                    "q3": 0.0,
                    "iqr": 0.0,
                    "lower_bound": 0.0,
                    "upper_bound": 0.0
                }
            
            # 计算统计指标
            valid_velocities_array = np.array(valid_velocities)
            q1 = np.percentile(valid_velocities_array, 25)
            q3 = np.percentile(valid_velocities_array, 75)
            iqr = q3 - q1
            
            # 计算异常值边界
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            # 识别正常值和异常值
            normal_values = [v for v in valid_velocities if lower_bound <= v <= upper_bound]
            outliers = [v for v in valid_velocities if v < lower_bound or v > upper_bound]
            
            # 计算去除异常值后的平均值
            average_without_outliers = np.mean(normal_values) if normal_values else 0.0
            
            result = {
                "average_without_outliers": average_without_outliers,
                "normal_count": len(normal_values),
                "outlier_count": len(outliers),
                "total_count": len(velocities),
                "q1": q1,
                "q3": q3,
                "iqr": iqr,
                "lower_bound": lower_bound,
                "upper_bound": upper_bound
            }
            
            logger.debug(f"去除异常值平均流速计算:")
            logger.debug(f"  原始数据数量: {len(velocities)}")
            logger.debug(f"  有效数据数量: {len(valid_velocities)} (排除零值)")
            logger.debug(f"  正常值数量: {len(normal_values)}")
            logger.debug(f"  异常值数量: {len(outliers)}")
            logger.debug(f"  异常值边界: [{lower_bound:.3f}, {upper_bound:.3f}] m/s")
            logger.debug(f"  去除异常值后平均流速: {average_without_outliers:.3f} m/s")
            
            if outliers:
                logger.debug(f"  异常值列表: {[f'{v:.3f}' for v in outliers]} m/s")
            
            return result
            
        except Exception as e:
            logger.error(f"计算去除异常值平均流速时发生错误: {e}")
            return {
                "average_without_outliers": 0.0,
                "normal_count": 0,
                "outlier_count": 0,
                "total_count": len(velocities),
                "q1": 0.0,
                "q3": 0.0,
                "iqr": 0.0,
                "lower_bound": 0.0,
                "upper_bound": 0.0
            }

    def _filter_outliers_from_group(self, values: List[float], group_name: str) -> Tuple[List[float], List[float]]:
        """从数据组中过滤异常值

        Args:
            values: 待过滤的数值列表
            group_name: 数据组名称（用于日志）

        Returns:
            (正常值列表, 异常值列表)
        """
        if len(values) < 3:  # 数据量太少，不进行异常值检测
            return values, []

        try:
            import numpy as np

            # 计算统计指标
            values_array = np.array(values)
            q1 = np.percentile(values_array, 25)
            q3 = np.percentile(values_array, 75)
            iqr = q3 - q1

            # 计算异常值边界
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr

            # 识别正常值和异常值
            normal_values = [v for v in values if lower_bound <= v <= upper_bound]
            outliers = [v for v in values if v < lower_bound or v > upper_bound]

            if outliers:
                logger.debug(f"  {group_name}组异常值过滤:")
                logger.debug(f"    原始{group_name}数量: {len(values)}")
                logger.debug(f"    过滤后{group_name}数量: {len(normal_values)}")
                logger.debug(f"    异常值边界: [{lower_bound:.3f}, {upper_bound:.3f}] m/s")
                logger.debug(f"    被排除的异常值: {[f'{v:.3f}' for v in outliers]} m/s")

            return normal_values, outliers

        except Exception as e:
            logger.warning(f"过滤{group_name}组异常值时发生错误: {e}，返回原始数据")
            return values, []

    def _calculate_custom_average_velocity(self, velocities: List[float]) -> Dict:
        """计算定制化平均流速

        数据预处理：过滤掉所有零值（包括正零和负零）
        数据分类统计：统计正值和负值的数量
        计算逻辑：
        - 如果正值数量 > 负值数量，计算所有正值的平均值
        - 如果负值数量 > 正值数量，计算所有负值的平均值
        - 如果正值数量 = 负值数量，返回正值平均值和负值平均值

        Args:
            velocities: 原始流速数据列表

        Returns:
            包含定制化平均流速结果的字典
        """
        if not velocities:
            return {
                "result": 0.0,
                "calculation_type": "no_data",
                "original_count": 0,
                "filtered_count": 0,
                "positive_count": 0,
                "negative_count": 0,
                "zero_count": 0
            }

        # 1. 数据预处理：过滤掉所有零值
        non_zero_velocities = [v for v in velocities if v != 0.0 and v != -0.0]

        # 2. 数据分类统计
        positive_values = [v for v in non_zero_velocities if v > 0]
        negative_values = [v for v in non_zero_velocities if v < 0]
        zero_count = len(velocities) - len(non_zero_velocities)

        positive_count = len(positive_values)
        negative_count = len(negative_values)

        logger.debug(f"定制化平均流速计算 - 数据统计:")
        logger.debug(f"  原始数据数量: {len(velocities)}")
        logger.debug(f"  零值数量: {zero_count}")
        logger.debug(f"  过滤后数据数量: {len(non_zero_velocities)}")
        logger.debug(f"  正值数量: {positive_count}")
        logger.debug(f"  负值数量: {negative_count}")

        # 输出详细的逐条分析线信息
        logger.debug(f"详细分析线流速信息:")
        for i, velocity in enumerate(velocities):
            if hasattr(self, 'analysis_lines') and self.analysis_lines.lines and i < len(self.analysis_lines.lines):
                line = self.analysis_lines.lines[i]
                logger.debug(f"  分析线ID: {line.line_id}, 中心点: ({line.center_point[0]}, {line.center_point[1]}), "
                           f"长度: {line.length} px, 流速: {velocity:.3f} m/s")
            else:
                logger.debug(f"  分析线ID: {i}, 中心点: (未知), 长度: 未知 px, 流速: {velocity:.3f} m/s")

        # 3. 计算逻辑
        if positive_count == 0 and negative_count == 0:
            # 所有数据都是零值
            result = {
                "result": 0.0,
                "calculation_type": "all_zero",
                "original_count": len(velocities),
                "filtered_count": len(non_zero_velocities),
                "positive_count": positive_count,
                "negative_count": negative_count,
                "zero_count": zero_count
            }
        
        elif positive_count > negative_count:
            # 情况1：正值数量 > 负值数量，计算所有正值的平均值（先过滤异常值）
            logger.debug(f"定制化平均流速计算 - 正值占优，开始异常值过滤:")
            filtered_positive_values, positive_outliers = self._filter_outliers_from_group(positive_values, "正值")

            if filtered_positive_values:
                avg_positive = np.mean(filtered_positive_values)
            else:
                # 如果所有正值都被识别为异常值，使用原始正值的平均值
                avg_positive = np.mean(positive_values)
                logger.warning(f"  警告: 所有正值都被识别为异常值，使用原始正值平均值")

            result = {
                "result": avg_positive,
                "calculation_type": "positive_dominant",
                "original_count": len(velocities),
                "filtered_count": len(non_zero_velocities),
                "positive_count": positive_count,
                "negative_count": negative_count,
                "zero_count": zero_count,
                "positive_average": avg_positive,
                "filtered_positive_count": len(filtered_positive_values),
                "positive_outliers_count": len(positive_outliers),
                "positive_outliers": positive_outliers
            }
            logger.info(f"  计算结果: 正值占优，平均值 = {avg_positive:.3f} m/s (基于{len(filtered_positive_values)}个正常值，排除了{len(positive_outliers)}个异常值)")
            
        elif negative_count > positive_count:
            # 情况2：负值数量 > 正值数量，计算所有负值的平均值（先过滤异常值）
            logger.debug(f"定制化平均流速计算 - 负值占优，开始异常值过滤:")
            filtered_negative_values, negative_outliers = self._filter_outliers_from_group(negative_values, "负值")

            if filtered_negative_values:
                avg_negative = np.mean(filtered_negative_values)
            else:
                # 如果所有负值都被识别为异常值，使用原始负值的平均值
                avg_negative = np.mean(negative_values)
                logger.warning(f"  警告: 所有负值都被识别为异常值，使用原始负值平均值")

            result = {
                "result": avg_negative,
                "calculation_type": "negative_dominant",
                "original_count": len(velocities),
                "filtered_count": len(non_zero_velocities),
                "positive_count": positive_count,
                "negative_count": negative_count,
                "zero_count": zero_count,
                "negative_average": avg_negative,
                "filtered_negative_count": len(filtered_negative_values),
                "negative_outliers_count": len(negative_outliers),
                "negative_outliers": negative_outliers
            }
            logger.info(f"  计算结果: 负值占优，平均值 = {avg_negative:.3f} m/s (基于{len(filtered_negative_values)}个正常值，排除了{len(negative_outliers)}个异常值)")
        else:
            # 情况3：正值数量 = 负值数量，返回两个结果（都先过滤异常值）
            logger.debug(f"定制化平均流速计算 - 正负值数量相等，开始异常值过滤:")

            # 过滤正值异常值
            filtered_positive_values, positive_outliers = self._filter_outliers_from_group(positive_values, "正值") if positive_values else ([], [])
            avg_positive = np.mean(filtered_positive_values) if filtered_positive_values else (np.mean(positive_values) if positive_values else 0.0)

            # 过滤负值异常值
            filtered_negative_values, negative_outliers = self._filter_outliers_from_group(negative_values, "负值") if negative_values else ([], [])
            avg_negative = np.mean(filtered_negative_values) if filtered_negative_values else (np.mean(negative_values) if negative_values else 0.0)

            result = {
                "result": {
                    "positive_average": avg_positive,
                    "negative_average": avg_negative
                },
                "calculation_type": "equal_count",
                "original_count": len(velocities),
                "filtered_count": len(non_zero_velocities),
                "positive_count": positive_count,
                "negative_count": negative_count,
                "zero_count": zero_count,
                "positive_average": avg_positive,
                "negative_average": avg_negative,
                "filtered_positive_count": len(filtered_positive_values),
                "positive_outliers_count": len(positive_outliers),
                "positive_outliers": positive_outliers,
                "filtered_negative_count": len(filtered_negative_values),
                "negative_outliers_count": len(negative_outliers),
                "negative_outliers": negative_outliers
            }
            logger.info(f"  计算结果: 正负值数量相等")
            logger.info(f"    正值平均值 = {avg_positive:.3f} m/s (基于{len(filtered_positive_values)}个正常值，排除了{len(positive_outliers)}个异常值)")
            logger.info(f"    负值平均值 = {avg_negative:.3f} m/s (基于{len(filtered_negative_values)}个正常值，排除了{len(negative_outliers)}个异常值)")

        return result

    def _save_debug_images(self, sti_id: int, original_sti: np.ndarray,
                          filtered_sti: np.ndarray, mask: np.ndarray,
                          first_original_frame: np.ndarray, velocities: List[float], angle_diffs: List[float]):
        """保存调试图像"""
        from datetime import datetime
        import os

        debug_dir = Path("data/image/stiv_debug")
        debug_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存原始STI
        # original_filename = f"sti_{sti_id:02d}_original_{timestamp}.png"
        # original_path = debug_dir / original_filename
        # cv2.imwrite(str(original_path), original_sti)
        # file_size = os.path.getsize(original_path) / 1024  # KB
        # logger.debug(f"STI调试图像 - 原始STI:")
        # logger.debug(f"  类型: Space-Time Image (原始)")
        # logger.debug(f"  目的: 显示第{sti_id}条分析线的原始时空图像")
        # logger.debug(f"  尺寸: {original_sti.shape}")
        # logger.debug(f"  文件大小: {file_size:.1f} KB")
        # logger.debug(f"  保存路径: {original_path}")
        # logger.debug(f"  数据范围: [{original_sti.min():.1f}, {original_sti.max():.1f}]")

        # 保存滤波后的STI
        # filtered_filename = f"sti_{sti_id:02d}_filtered_{timestamp}.png"
        # filtered_path = debug_dir / filtered_filename
        # cv2.imwrite(str(filtered_path), filtered_sti)
        # file_size = os.path.getsize(filtered_path) / 1024  # KB
        # logger.debug(f"STI调试图像 - 滤波STI:")
        # logger.debug(f"  类型: Space-Time Image (滤波后)")
        # logger.debug(f"  目的: 显示第{sti_id}条分析线经过频域滤波后的STI")
        # logger.debug(f"  尺寸: {filtered_sti.shape}")
        # logger.debug(f"  文件大小: {file_size:.1f} KB")
        # logger.debug(f"  保存路径: {filtered_path}")
        # logger.debug(f"  滤波参数: filter_window={self.config.algorithm.filter_window}")

        # 保存带掩码的结果
        # result_image = cv2.add(filtered_sti, mask.astype(np.uint8))
        # result_filename = f"sti_{sti_id:02d}_result_{timestamp}.png"
        # result_path = debug_dir / result_filename
        # cv2.imwrite(str(result_path), result_image)
        # file_size = os.path.getsize(result_path) / 1024  # KB
        # logger.debug(f"STI调试图像 - 运动检测结果:")
        # logger.debug(f"  类型: Space-Time Image (运动检测)")
        # logger.debug(f"  目的: 显示第{sti_id}条分析线的运动检测结果和角度掩码")
        # logger.debug(f"  尺寸: {result_image.shape}")
        # logger.debug(f"  文件大小: {file_size:.1f} KB")
        # logger.debug(f"  保存路径: {result_path}")
        # logger.debug(f"  算法方法: {self.config.algorithm.method}")

        # 只在第一个STI时保存分析线和ROI可视化图片
        if sti_id == 0 and first_original_frame is not None:
            self._save_coordinate_grid(debug_dir, first_original_frame, timestamp)
            self._save_analysis_lines_visualization(debug_dir, first_original_frame, timestamp, velocities)
            self._save_roi_visualization(debug_dir, first_original_frame, timestamp)
            
            # 导出分析线数据到CSV（与调试图片放在同一目录）
            # 使用self.video_path作为视频路径，如果可用的话
            video_path_for_csv = getattr(self, 'video_path', None)
            self._export_analysis_lines_data_to_csv(video_path_for_csv, velocities, angle_diffs, debug_dir)

    def _save_analysis_lines_visualization(self, debug_dir: Path, original_frame: np.ndarray, timestamp: str, velocities: List[float] = None):
        """保存分析线可视化图片"""
        # 检查输入图像是否有效
        if original_frame is None or original_frame.size == 0:
            logger.error("分析线可视化：原始帧为空，跳过保存")
            return

        # 创建可视化图像
        vis_image = original_frame.copy()

        # 转换为彩色图像（如果是灰度图）
        if len(vis_image.shape) == 2:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)
        elif vis_image.shape[2] == 1:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)

        # 确保分析线已生成
        if not self.analysis_lines.lines:
            roi = getattr(self.config.preprocessing, 'roi', None)
            self.analysis_lines.generate_lines(original_frame.shape[:2], roi)

        # 首先绘制配置的主中心点（红色标记）- 仅显示点，不显示坐标
        if self.config.analysis_lines.center_point:
            main_center_x, main_center_y = self.config.analysis_lines.center_point
            # 绘制大红色圆圈标记主中心点
            cv2.circle(vis_image, (main_center_x, main_center_y), 10, (0, 0, 255), 3)  # 红色空心圆
            cv2.circle(vis_image, (main_center_x, main_center_y), 6, (0, 0, 255), -1)  # 红色实心圆
            # 不再显示坐标数值，仅保留中心点标记

        # 绘制配置的流向角度箭头（在绘制分析线之前）
        self._draw_flow_direction_arrow(vis_image)

        # 绘制分析线
        for i, line in enumerate(self.analysis_lines.lines):
            # 使用绿色绘制分析线
            color = (0, 255, 0)  # 绿色 (BGR格式)

            # 绘制分析线（加粗）
            cv2.line(vis_image, line.start_point, line.end_point, color, 4)

            # 绘制起点（小圆圈）
            cv2.circle(vis_image, line.start_point, 4, color, -1)

            # 绘制终点（小圆圈）
            cv2.circle(vis_image, line.end_point, 4, color, -1)

            # 绘制分析线中心点（绿色圆圈，确保在线上）
            cv2.circle(vis_image, line.center_point, 4, color, -1)
            # 添加白色边框以便更清楚地看到
            cv2.circle(vis_image, line.center_point, 4, (255, 255, 255), 2)

            # 改进分析线ID标注位置：优先放置在起点附近，避免与线重叠
            self._draw_line_id_label(vis_image, line, color)
            
            # 添加流速信息（如果提供了velocities数据）
            if velocities and i < len(velocities):
                velocity = velocities[i]
                # 绘制流速数值
                # velocity_text = f"V: {velocity:.3f} m/s"
                # cv2.putText(vis_image, velocity_text,
                #            (label_x, label_y + 20),
                #            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)  # 黄色

                # 绘制流速方向箭头
                self._draw_velocity_arrow(vis_image, line.center_point, velocity,
                                        self.config.analysis_lines.flow_direction)

        # 保存图像
        filename = f"analysis_lines_visualization_{timestamp}.png"
        filepath = debug_dir / filename
        cv2.imwrite(str(filepath), vis_image)

        # 详细调试日志
        import os
        file_size = os.path.getsize(filepath) / 1024  # KB
        logger.debug(f"分析线可视化图像:")
        logger.debug(f"  类型: Analysis Lines Visualization")
        logger.debug(f"  目的: 显示STIV分析线在原始图像上的位置和方向")
        logger.debug(f"  尺寸: {vis_image.shape}")
        logger.debug(f"  文件大小: {file_size:.1f} KB")
        logger.debug(f"  保存路径: {filepath}")
        logger.debug(f"  配置参数:")
        logger.debug(f"    - 流向角度: {self.config.analysis_lines.flow_direction}°")
        logger.debug(f"    - 分析线数量: {self.config.analysis_lines.line_count}")
        logger.debug(f"    - 线间距: {self.config.analysis_lines.line_spacing}像素")
        logger.debug(f"    - 线长度: {self.config.analysis_lines.line_length}像素")
        logger.debug(f"    - 中心点: {self.config.analysis_lines.center_point}")
        logger.info(f"分析线可视化图片已保存: {filename}")

    def _draw_line_id_label(self, image: np.ndarray, line, color: tuple):
        """在分析线端点附近绘制ID标注

        Args:
            image: 图像
            line: 分析线对象
            color: 标注颜色
        """
        import math

        # 获取图像尺寸
        image_height, image_width = image.shape[:2]

        # 计算线段方向向量（从起点到终点）
        dx = line.end_point[0] - line.start_point[0]
        dy = line.end_point[1] - line.start_point[1]
        line_length = math.sqrt(dx*dx + dy*dy)

        if line_length > 0:
            # 单位方向向量
            dx_unit = dx / line_length
            dy_unit = dy / line_length

            # 垂直方向向量（用于偏移标注位置，避免与线重叠）
            perp_dx = -dy_unit
            perp_dy = dx_unit
        else:
            dx_unit = dy_unit = perp_dx = perp_dy = 0

        # 标注偏移距离
        offset_distance = 18
        perp_offset = 12

        # 优先尝试在起点附近放置标注
        start_label_x = int(line.start_point[0] + dx_unit * offset_distance + perp_dx * perp_offset)
        start_label_y = int(line.start_point[1] + dy_unit * offset_distance + perp_dy * perp_offset)

        # 检查起点标注是否在图像边界内
        text_width = len(str(line.line_id)) * 12  # 估算文字宽度
        text_height = 20

        start_in_bounds = (start_label_x >= 0 and start_label_x + text_width <= image_width and
                          start_label_y >= text_height and start_label_y <= image_height)

        if start_in_bounds:
            # 在起点附近放置标注
            label_x, label_y = start_label_x, start_label_y
        else:
            # 在终点附近放置标注
            end_label_x = int(line.end_point[0] - dx_unit * offset_distance + perp_dx * perp_offset)
            end_label_y = int(line.end_point[1] - dy_unit * offset_distance + perp_dy * perp_offset)

            # 确保终点标注在边界内
            end_label_x = max(0, min(end_label_x, image_width - text_width))
            end_label_y = max(text_height, min(end_label_y, image_height))

            label_x, label_y = end_label_x, end_label_y

        # 绘制分析线ID标注
        cv2.putText(image, str(line.line_id),
                   (label_x, label_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    def _save_velocity_boxplot(self, debug_dir: Path, velocities: List[float], timestamp: str):
        """生成并保存流速数据箱式图

        Args:
            debug_dir: 调试图片保存目录
            velocities: 所有分析线的流速数据
            timestamp: 时间戳
        """
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            # 过滤掉零值数据
            valid_velocities = [v for v in velocities if v != 0.0 and v != -0.0]

            if not valid_velocities:
                logger.warning("没有有效的流速数据，跳过箱式图生成")
                return

            # 计算统计指标
            valid_velocities_array = np.array(valid_velocities)
            q1 = np.percentile(valid_velocities_array, 25)
            median = np.percentile(valid_velocities_array, 50)
            q3 = np.percentile(valid_velocities_array, 75)
            iqr = q3 - q1

            # 计算异常值边界
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr

            # 识别异常值
            outliers = [v for v in valid_velocities if v < lower_bound or v > upper_bound]

            # 创建箱式图
            plt.figure(figsize=(10, 6))

            # 生成箱式图
            plt.boxplot(valid_velocities, patch_artist=True,
                       boxprops=dict(facecolor='lightblue', alpha=0.7),
                       medianprops=dict(color='red', linewidth=2),
                       flierprops=dict(marker='o', markerfacecolor='red',
                                     markersize=6, alpha=0.7))

            # 设置图表属性
            plt.title('STIV Velocity Data Distribution Box Plot', fontsize=14, fontweight='bold')
            plt.xlabel('Analysis Lines', fontsize=12)
            plt.ylabel('Velocity (m/s)', fontsize=12)
            plt.grid(True, alpha=0.3)

            # 添加统计信息文本
            stats_text = f'Total: {len(velocities)}\nValid: {len(valid_velocities)}\n'
            stats_text += f'Q1: {q1:.3f}\nMedian: {median:.3f}\nQ3: {q3:.3f}\n'
            stats_text += f'IQR: {iqr:.3f}\nOutliers: {len(outliers)}'

            plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            # 保存图片
            filename = f"velocity_boxplot_{timestamp}.png"
            filepath = debug_dir / filename
            plt.savefig(str(filepath), dpi=150, bbox_inches='tight')
            plt.close()

            # 输出统计摘要日志
            import os
            file_size = os.path.getsize(filepath) / 1024  # KB

            logger.debug(f"流速数据箱式图:")
            logger.debug(f"  类型: Velocity Box Plot")
            logger.debug(f"  目的: 显示所有分析线流速数据的统计分布")
            logger.debug(f"  数据总数: {len(velocities)}")
            logger.debug(f"  有效数据数: {len(valid_velocities)} (排除零值)")
            logger.debug(f"  统计指标:")
            logger.debug(f"    - Q1 (25%分位数): {q1:.3f} m/s")
            logger.debug(f"    - 中位数 (Q2): {median:.3f} m/s")
            logger.debug(f"    - Q3 (75%分位数): {q3:.3f} m/s")
            logger.debug(f"    - 四分位距 (IQR): {iqr:.3f} m/s")
            logger.debug(f"    - 异常值边界: [{lower_bound:.3f}, {upper_bound:.3f}] m/s")
            logger.debug(f"    - 异常值数量: {len(outliers)}")
            if outliers:
                logger.debug(f"    - 异常值: {[f'{v:.3f}' for v in outliers]} m/s")
            logger.debug(f"  文件大小: {file_size:.1f} KB")
            logger.debug(f"  保存路径: {filepath}")
            logger.info(f"流速数据箱式图已保存: {filename}")

        except ImportError:
            logger.warning("matplotlib未安装，跳过箱式图生成")
        except Exception as e:
            logger.error(f"生成箱式图时发生错误: {e}")

    def _draw_flow_direction_arrow(self, image: np.ndarray):
        """绘制配置的流向角度箭头标识"""
        import math

        # 获取配置的流向角度
        flow_direction = self.config.analysis_lines.flow_direction

        # 选择箭头绘制位置（图像右上角）
        image_height, image_width = image.shape[:2]
        arrow_center_x = image_width - 150
        arrow_center_y = 100

        # 将角度转换为弧度
        flow_angle_rad = math.radians(flow_direction)

        # 计算箭头方向向量
        arrow_length = 80
        arrow_end_x = arrow_center_x + math.cos(flow_angle_rad) * arrow_length
        arrow_end_y = arrow_center_y + math.sin(flow_angle_rad) * arrow_length

        # 绘制流向箭头（橙色）
        arrow_color = (0, 165, 255)  # 橙色 (BGR格式)
        cv2.arrowedLine(image,
                       (int(arrow_center_x), int(arrow_center_y)),
                       (int(arrow_end_x), int(arrow_end_y)),
                       arrow_color, 4, tipLength=0.3)

        # 绘制箭头起点圆圈
        cv2.circle(image, (int(arrow_center_x), int(arrow_center_y)), 8, arrow_color, -1)
        cv2.circle(image, (int(arrow_center_x), int(arrow_center_y)), 8, (255, 255, 255), 2)

        # 添加流向角度标签
        label_x = arrow_center_x - 60
        label_y = arrow_center_y - 20

        # 绘制标签背景
        # cv2.rectangle(image, (label_x - 10, label_y - 25), (label_x + 120, label_y + 10), (0, 0, 0), -1)
        # cv2.rectangle(image, (label_x - 10, label_y - 25), (label_x + 120, label_y + 10), (255, 255, 255), 2)

        # 绘制标签文字
        cv2.putText(image, f"{flow_direction:.1f} degrees",
                   (label_x, label_y + 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, arrow_color, 2)

        logger.debug(f"流向角度箭头已绘制: {flow_direction:.1f}°")

    def _draw_velocity_arrow(self, image: np.ndarray, center_point: tuple, velocity: float, flow_direction: float):
        """在分析线位置绘制流速箭头

        Args:
            image: 图像
            center_point: 分析线中心点 (x, y)
            velocity: 流速值 (m/s)
            flow_direction: 配置的流向角度（度）
        """
        import math

        # 如果流速为0，不绘制箭头
        if abs(velocity) < 0.001:
            return

        # 计算箭头方向（基于流向和流速符号）
        flow_angle_rad = math.radians(flow_direction)
        if velocity < 0:
            # 负流速：箭头方向与配置流向相反
            arrow_angle_rad = flow_angle_rad + math.pi
        else:
            # 正流速：箭头方向与配置流向相同
            arrow_angle_rad = flow_angle_rad

        # 计算箭头长度（基于流速大小）
        base_length = 30
        arrow_length = base_length + min(abs(velocity) * 20, 50)  # 最大长度限制

        # 计算箭头终点
        arrow_end_x = center_point[0] + math.cos(arrow_angle_rad) * arrow_length
        arrow_end_y = center_point[1] + math.sin(arrow_angle_rad) * arrow_length

        # 选择箭头颜色（基于流速符号）
        if velocity > 0:
            arrow_color = (0, 255, 255)  # 黄色：正流速
        else:
            arrow_color = (255, 0, 255)  # 紫色：负流速

        # 绘制箭头
        cv2.arrowedLine(image,
                       center_point,
                       (int(arrow_end_x), int(arrow_end_y)),
                       arrow_color, 3, tipLength=0.3)

    def _save_roi_visualization(self, debug_dir: Path, original_frame: np.ndarray, timestamp: str):
        """保存ROI区域可视化图片"""
        # 检查输入图像是否有效
        if original_frame is None or original_frame.size == 0:
            logger.error("ROI可视化：原始帧为空，跳过保存")
            return

        # 创建可视化图像
        vis_image = original_frame.copy()

        # 转换为彩色图像（如果是灰度图）
        if len(vis_image.shape) == 2:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)
        elif vis_image.shape[2] == 1:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)

        roi_configured = False

        # 绘制pre_roi（如果存在）
        if self.config.preprocessing.pre_roi:
            pre_roi = self.config.preprocessing.pre_roi
            if len(pre_roi) == 2 and len(pre_roi[0]) == 2:
                # 格式: [[y1, x1], [y2, x2]]
                y1, x1 = pre_roi[0]
                y2, x2 = pre_roi[1]
                # 绘制蓝色边框
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 0), 3)  # 蓝色
                cv2.putText(vis_image, "Pre-ROI", (x1 + 10, y1 + 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
                roi_configured = True

        # 绘制roi（如果存在）
        if self.config.preprocessing.roi:
            roi = self.config.preprocessing.roi
            try:
                if len(roi) == 2 and len(roi[0]) == 2:
                    # 矩形格式: [[y1, x1], [y2, x2]]
                    y1, x1 = roi[0]
                    y2, x2 = roi[1]
                    # 绘制红色边框
                    cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 0, 255), 3)  # 红色
                    cv2.putText(vis_image, "ROI (Rectangle)", (x1 + 10, y1 + 60),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    roi_configured = True
                elif len(roi) >= 3:
                    # 多边形格式: [[x1, y1], [x2, y2], [x3, y3], ...]
                    # 坐标格式：[x, y] -> OpenCV格式
                    pts = np.array([[x, y] for x, y in roi], np.int32)

                    # 绘制多边形边界
                    cv2.polylines(vis_image, [pts], True, (0, 0, 255), 3)  # 红色边界

                    # 绘制半透明填充
                    overlay = vis_image.copy()
                    cv2.fillPoly(overlay, [pts], (0, 0, 255))  # 红色填充
                    cv2.addWeighted(vis_image, 0.8, overlay, 0.2, 0, vis_image)

                    # 绘制顶点和坐标标签
                    for i, (x, y) in enumerate(roi):
                        cv2.circle(vis_image, (x, y), 8, (0, 255, 255), -1)  # 黄色顶点
                        cv2.putText(vis_image, f"P{i+1}({x},{y})", (x + 15, y - 15),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                    # 添加标签
                    center_x = int(np.mean([point[0] for point in roi]))  # x坐标
                    center_y = int(np.mean([point[1] for point in roi]))  # y坐标
                    cv2.putText(vis_image, f"ROI (Polygon, {len(roi)} points)",
                               (center_x - 100, center_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    roi_configured = True
                else:
                    logger.warning(f"ROI格式不支持: {roi}")
            except Exception as e:
                logger.error(f"ROI可视化错误: {e}")
                logger.warning(f"ROI配置: {roi}")

        # 如果没有配置ROI，显示示例ROI（用于演示）
        if not roi_configured:
            # 使用yuwangling案例的示例ROI: [[75, 600], [75, 80], [750, 70], [1240, 140]]
            demo_pre_roi = [[75, 600], [750, 1240]]  # 示例Pre-ROI
            demo_roi = [[75, 80], [750, 140]]        # 示例ROI

            # 绘制示例Pre-ROI
            y1, x1 = demo_pre_roi[0]
            y2, x2 = demo_pre_roi[1]
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 0), 3)  # 蓝色
            cv2.putText(vis_image, "Demo Pre-ROI", (x1 + 10, y1 + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)

            # 绘制示例ROI
            y1, x1 = demo_roi[0]
            y2, x2 = demo_roi[1]
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 0, 255), 3)  # 红色
            cv2.putText(vis_image, "Demo ROI", (x1 + 10, y1 + 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

            # 添加说明文字
            cv2.putText(vis_image, "No ROI configured - showing demo ROI",
                       (50, vis_image.shape[0] - 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)  # 黄色

        # 保存图像
        filename = f"roi_visualization_{timestamp}.png"
        filepath = debug_dir / filename
        cv2.imwrite(str(filepath), vis_image)

        # 详细调试日志
        import os
        file_size = os.path.getsize(filepath) / 1024  # KB
        logger.debug(f"ROI可视化图像:")
        logger.debug(f"  类型: ROI Visualization")
        logger.debug(f"  目的: 显示预处理ROI区域在原始图像上的位置")
        logger.debug(f"  尺寸: {vis_image.shape}")
        logger.debug(f"  文件大小: {file_size:.1f} KB")
        logger.debug(f"  保存路径: {filepath}")
        logger.debug(f"  配置参数:")
        logger.debug(f"    - Pre-ROI: {self.config.preprocessing.pre_roi}")
        logger.debug(f"    - ROI: {self.config.preprocessing.roi}")
        logger.debug(f"    - 是否显示示例: {not roi_configured}")
        logger.info(f"ROI可视化图片已保存: {filename}")

    def _save_coordinate_grid(self, debug_dir: Path, original_frame: np.ndarray, timestamp: str):
        """保存坐标网格调试图片"""
        # 检查输入图像是否有效
        if original_frame is None or original_frame.size == 0:
            logger.error("坐标网格：原始帧为空，跳过保存")
            return

        # 创建网格图像
        grid_image = original_frame.copy()

        # 转换为彩色图像（如果是灰度图）
        if len(grid_image.shape) == 2:
            grid_image = cv2.cvtColor(grid_image, cv2.COLOR_GRAY2BGR)
        elif grid_image.shape[2] == 1:
            grid_image = cv2.cvtColor(grid_image, cv2.COLOR_GRAY2BGR)

        height, width = grid_image.shape[:2]

        # 网格参数
        grid_spacing_x = max(50, width // 20)   # 水平网格间距
        grid_spacing_y = max(50, height // 15)  # 垂直网格间距

        # 绘制垂直网格线
        for x in range(0, width, grid_spacing_x):
            cv2.line(grid_image, (x, 0), (x, height), (0, 255, 0), 1)  # 绿色网格线

            # 在顶部和底部添加x坐标标签
            if x % (grid_spacing_x * 2) == 0:  # 每隔一条线显示坐标
                cv2.putText(grid_image, f"{x}", (x + 5, 25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.putText(grid_image, f"{x}", (x + 5, height - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制水平网格线
        for y in range(0, height, grid_spacing_y):
            cv2.line(grid_image, (0, y), (width, y), (0, 255, 0), 1)  # 绿色网格线

            # 在左侧和右侧添加y坐标标签
            if y % (grid_spacing_y * 2) == 0:  # 每隔一条线显示坐标
                cv2.putText(grid_image, f"{y}", (5, y + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.putText(grid_image, f"{y}", (width - 50, y + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制主要坐标轴（更粗的线）
        cv2.line(grid_image, (0, 0), (width, 0), (255, 255, 0), 2)      # 顶边 - 青色
        cv2.line(grid_image, (0, 0), (0, height), (255, 255, 0), 2)     # 左边 - 青色
        cv2.line(grid_image, (width-1, 0), (width-1, height), (255, 255, 0), 2)  # 右边 - 青色
        cv2.line(grid_image, (0, height-1), (width, height-1), (255, 255, 0), 2) # 底边 - 青色

        # 添加配置文件中的坐标点范围（借鉴ROI可视化的简洁实现）
        self._draw_coordinate_range(grid_image)

        # 添加图像尺寸信息
        cv2.putText(grid_image, f"Image Size: {width} x {height}",
                   (width // 2 - 100, 50),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)

        # 添加网格间距信息
        cv2.putText(grid_image, f"Grid: {grid_spacing_x}x{grid_spacing_y} pixels",
                   (width // 2 - 100, height - 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        # 添加坐标系说明
        cv2.putText(grid_image, "Origin (0,0) at top-left",
                   (10, height - 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(grid_image, "Format: (x, y)",
                   (10, height - 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

        # 保存图像
        filename = f"coordinate_grid_{timestamp}.png"
        filepath = debug_dir / filename
        cv2.imwrite(str(filepath), grid_image)

        # 详细调试日志
        import os
        file_size = os.path.getsize(filepath) / 1024  # KB
        logger.debug(f"坐标网格调试图像:")
        logger.debug(f"  类型: Coordinate Grid")
        logger.debug(f"  目的: 提供像素坐标参考，用于验证分析线和ROI位置")
        logger.debug(f"  尺寸: {grid_image.shape}")
        logger.debug(f"  文件大小: {file_size:.1f} KB")
        logger.debug(f"  保存路径: {filepath}")
        logger.debug(f"  网格间距: {grid_spacing_x}x{grid_spacing_y} 像素")
        logger.debug(f"  图像尺寸: {width}x{height} 像素")
        logger.info(f"坐标网格调试图片已保存: {filename}")

    def _draw_coordinate_range(self, image: np.ndarray):
        """绘制配置文件中的坐标点范围（简化版，借鉴ROI可视化实现）

        Args:
            image: 要绘制的图像
        """
        try:
            # 获取坐标系统配置
            if not hasattr(self.config, 'coordinate_system'):
                logger.debug("坐标网格：未配置坐标系统，跳过坐标点绘制")
                return

            coord_system = self.config.coordinate_system
            if not coord_system or not hasattr(coord_system, 'calibration'):
                logger.debug("坐标网格：坐标系统配置无效，跳过坐标点绘制")
                return

            calibration_data = coord_system.calibration
            if not calibration_data:
                logger.debug("坐标网格：坐标系统标定数据为空，跳过坐标点绘制")
                return

            # 获取coordinates列表
            coordinates = calibration_data.get("coordinates", [])
            if not coordinates:
                logger.debug("坐标网格：未找到坐标点配置，跳过坐标点绘制")
                return

            logger.debug(f"坐标网格：找到 {len(coordinates)} 个坐标点")

            # 提取有效的像素坐标点
            valid_points = []
            image_height, image_width = image.shape[:2]

            for i, coord in enumerate(coordinates):
                if "pixel" in coord and len(coord["pixel"]) == 2:
                    x, y = coord["pixel"]

                    # 检查坐标是否在图像范围内
                    if 0 <= x <= image_width and 0 <= y <= image_height:
                        valid_points.append((x, y))
                        logger.debug(f"  坐标点{i+1}: 像素坐标({x}, {y})")
                    else:
                        logger.warning(f"坐标点{i+1}超出图像范围: ({x}, {y}) vs 图像尺寸({image_width}, {image_height})")

            # 如果有多个有效坐标点，绘制多边形区域（借鉴ROI可视化的多边形处理方式）
            if len(valid_points) >= 3:
                # 将坐标点转换为numpy数组格式，用于多边形绘制
                pts = np.array(valid_points, np.int32)

                # 绘制多边形边界（橙色边界，与ROI的红色区分）
                cv2.polylines(image, [pts], True, (0, 165, 255), 3)  # 橙色边界

                # 绘制半透明填充
                overlay = image.copy()
                cv2.fillPoly(overlay, [pts], (0, 165, 255))  # 橙色填充
                cv2.addWeighted(image, 0.8, overlay, 0.2, 0, image)

                # 绘制顶点和坐标标签
                for i, (x, y) in enumerate(valid_points):
                    # 绘制顶点（橙色圆圈）
                    cv2.circle(image, (x, y), 8, (0, 255, 255), -1)  # 黄色顶点（更醒目）
                    cv2.putText(image, f"C{i+1}({x},{y})", (x + 15, y - 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)  # 黄色标签

                # 添加多边形信息标签
                center_x = int(np.mean([point[0] for point in valid_points]))
                center_y = int(np.mean([point[1] for point in valid_points]))
                cv2.putText(image, f"Coord Polygon ({len(valid_points)} points)",
                           (center_x - 100, center_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 165, 255), 2)

                logger.debug(f"坐标网格：绘制了坐标点多边形，{len(valid_points)}个顶点")

            elif len(valid_points) == 2:
                # 如果只有2个点，绘制线段
                pt1, pt2 = valid_points
                cv2.line(image, pt1, pt2, (0, 165, 255), 3)

                # 绘制端点
                for i, (x, y) in enumerate(valid_points):
                    cv2.circle(image, (x, y), 8, (0, 255, 255), -1)  # 黄色端点
                    cv2.putText(image, f"C{i+1}({x},{y})", (x + 15, y - 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                logger.debug(f"坐标网格：绘制了坐标点线段，2个端点")

            elif len(valid_points) == 1:
                # 如果只有1个点，绘制单点
                x, y = valid_points[0]
                cv2.circle(image, (x, y), 8, (0, 255, 255), -1)  # 黄色点
                cv2.putText(image, f"C1({x},{y})", (x + 15, y - 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                logger.debug(f"坐标网格：绘制了单个坐标点")

            # 添加坐标系统类型标签（右下角）
            coord_type = getattr(coord_system, 'type', 'unknown')
            type_text = f"Coord System: {coord_type}"
            cv2.putText(image, type_text, (image_width - 250, image_height - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 165, 255), 2)  # 橙色

            logger.debug(f"坐标网格：成功绘制了 {len(valid_points)} 个有效坐标点及其范围")

        except Exception as e:
            logger.error(f"坐标网格：绘制坐标点时发生错误: {e}")
            logger.debug(f"错误详情: {str(e)}")

    def _export_analysis_lines_data_to_csv(self, video_path: str, velocities: List[float],
                                           angle_diffs: List[float], debug_dir: Path) -> None:
        """将分析线详细数据导出到CSV文件

        Args:
            video_path: 视频文件路径，用于提取视频名称
            velocities: 分析线对应的流速数据列表
            angle_diffs: 分析线对应的角度差异数据列表
            debug_dir: 调试输出目录
        """
        try:
            import csv
            import math
            from pathlib import Path
            
            # 检查是否有分析线数据
            if not hasattr(self, 'analysis_lines') or not self.analysis_lines.lines:
                logger.warning("没有分析线数据，跳过CSV导出")
                return
                
            # 检查数据一致性
            if len(velocities) != len(self.analysis_lines.lines):
                logger.warning(f"流速数据数量({len(velocities)})与分析线数量({len(self.analysis_lines.lines)})不匹配，跳过CSV导出")
                return
            
            # 提取视频文件名
            video_name = Path(video_path).name if video_path else "unknown_video"
            
            # 准备CSV文件路径 - 使用固定名称，支持数据累积
            csv_filename = "analysis_lines_data.csv"
            csv_filepath = debug_dir / csv_filename
            
            # 获取配置的流向角度
            configured_flow_direction = self.config.analysis_lines.flow_direction
            
            # 准备CSV数据
            csv_data = []
            
            for i, (line, velocity) in enumerate(zip(self.analysis_lines.lines, velocities)):
                # 使用真实的角度差异数据（从FFT/GMT计算中获取）
                angle_diff = angle_diffs[i] if i < len(angle_diffs) else 0.0

                # 准备一行数据
                row_data = {
                    '视频名称': video_name,
                    '分析线ID': line.line_id,
                    '中心点坐标': f"({line.center_point[0]},{line.center_point[1]})",
                    '长度': line.length,
                    '流速': round(velocity, 4),  # 保留4位小数
                    '角度差异': round(angle_diff, 1)  # 保留1位小数
                }

                csv_data.append(row_data)
            
            # 检查文件是否存在，决定是否写入表头
            file_exists = csv_filepath.exists()

            # 写入CSV文件（追加模式）
            with open(csv_filepath, 'a', newline='', encoding='utf-8') as csvfile:
                if csv_data:
                    # 使用字典的键作为列名
                    fieldnames = list(csv_data[0].keys())
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    # 如果文件不存在或为空，写入表头
                    if not file_exists or csv_filepath.stat().st_size == 0:
                        writer.writeheader()

                    # 写入数据行
                    for row in csv_data:
                        writer.writerow(row)
            
            # 计算文件大小
            file_size = csv_filepath.stat().st_size / 1024  # KB
            
            # 输出日志信息
            mode_text = "追加" if file_exists else "创建"
            logger.info(f"分析线数据CSV已{mode_text}: {csv_filename} (本次{len(csv_data)}条记录)")
            logger.debug(f"CSV文件详情:")
            logger.debug(f"  文件路径: {csv_filepath}")
            logger.debug(f"  文件大小: {file_size:.1f} KB")
            logger.debug(f"  本次记录数量: {len(csv_data)}")
            logger.debug(f"  列定义: {', '.join(fieldnames if csv_data else [])}")
            
            # 输出前几条数据作为示例
            if csv_data:
                logger.debug(f"  数据示例 (前3条):")
                for i, row in enumerate(csv_data[:3]):
                    logger.debug(f"    记录{i+1}: {row}")
            
        except Exception as e:
            logger.error(f"导出分析线数据到CSV时发生错误: {e}")
            logger.debug(f"错误详情: {str(e)}")

    def get_analysis_info(self) -> Dict:
        """获取分析信息"""
        return {
            "coordinate_system": self.coordinate_system.get_calibration_info(),
            "time_range": self.time_manager.get_time_info(),
            "analysis_lines": self.analysis_lines.get_lines_info(),
            "config_summary": self.config.get_summary()
        }
    
    @classmethod
    def from_config_manager(cls, config_manager, video_path: str) -> 'STIVProcessor':
        """从配置管理器创建STIV处理器

        Args:
            config_manager: 配置管理器实例
            video_path: 视频文件路径

        Returns:
            STIVProcessor实例
        """
        # 配置日志级别
        try:
            from ...utils.logging_utils import configure_logging_from_config
            configure_logging_from_config(config_manager)
        except Exception as e:
            logger.warning(f"配置日志级别失败: {e}")

        stiv_config = config_manager.get_algorithm_config("stiv")
        if stiv_config is None:
            raise ValueError("配置文件中未找到STIV算法配置")

        return cls(stiv_config, video_path)


def create_preprocessing_function(preprocessing_config) -> Optional[Callable]:
    """创建预处理函数
    
    Args:
        preprocessing_config: 预处理配置
        
    Returns:
        预处理函数或None
    """
    if not preprocessing_config:
        return None
    
    def preprocess_image(image: np.ndarray) -> np.ndarray:
        """图像预处理函数"""
        processed = image.copy()
        
        # 旋转图像
        if preprocessing_config.rotate_image:
            processed = cv2.rotate(processed, cv2.ROTATE_90_CLOCKWISE)
        
        # 应用pre_roi
        if preprocessing_config.pre_roi:
            y1, x1 = preprocessing_config.pre_roi[0]
            y2, x2 = preprocessing_config.pre_roi[1]
            processed = processed[y1:y2, x1:x2]
        
        # 应用roi
        if preprocessing_config.roi:
            roi = preprocessing_config.roi
            if len(roi) == 2 and len(roi[0]) == 2:
                # 矩形ROI格式: [[y1, x1], [y2, x2]]
                y1, x1 = roi[0]
                y2, x2 = roi[1]
                processed = processed[y1:y2, x1:x2]
            elif len(roi) >= 3:
                # 多边形ROI格式: [[x1, y1], [x2, y2], [x3, y3], ...]
                # 创建多边形掩码
                mask = np.zeros(processed.shape[:2], dtype=np.uint8)
                # 坐标格式：[x, y] -> OpenCV需要的格式
                points = np.array([[x, y] for x, y in roi], dtype=np.int32)
                cv2.fillPoly(mask, [points], 255)

                # 应用掩码
                if len(processed.shape) == 3:
                    # 彩色图像
                    processed = cv2.bitwise_and(processed, processed, mask=mask)
                else:
                    # 灰度图像
                    processed = cv2.bitwise_and(processed, processed, mask=mask)

            else:
                logger.warning(f"ROI格式不支持: {roi}")
        
        # 应用分辨率缩放
        if preprocessing_config.resolution != 1.0:
            height, width = processed.shape[:2]
            new_height = int(height * preprocessing_config.resolution)
            new_width = int(width * preprocessing_config.resolution)
            processed = cv2.resize(processed, (new_width, new_height))
        
        # 图像校正（如果需要）
        if (preprocessing_config.image_correction and
            preprocessing_config.image_correction.get("apply", False)):
            # 这里可以添加镜头失真校正代码
            pass

        # 转换为灰度图像（STIV算法要求）
        if len(processed.shape) == 3:
            processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)

        return processed
    
    return preprocess_image


# 便捷函数
def analyze_video_with_stiv(video_path: str, config_path: str,
                           method: Optional[str] = None, save_debug: bool = False) -> Dict:
    """使用STIV分析视频的便捷函数

    Args:
        video_path: 视频文件路径
        config_path: 配置文件路径
        method: 运动计算方法，None时使用配置中的方法
        save_debug: 是否保存调试图像

    Returns:
        分析结果
    """
    from ...utils.config_manager import ConfigManager

    # 创建配置管理器
    config_manager = ConfigManager(config_path)

    # 创建处理器
    processor = STIVProcessor.from_config_manager(config_manager, video_path)

    # 创建预处理函数
    preprocess_func = create_preprocessing_function(processor.config.preprocessing)

    # 执行分析
    return processor.process(preprocess_func, method, save_debug)



