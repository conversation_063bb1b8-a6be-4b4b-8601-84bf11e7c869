"""
STIV核心算法模块

重构的Space-Time Image生成和处理算法
"""

import math
import time
import logging
from typing import List, Tuple, Optional
import cv2
import numpy as np
from numpy.typing import NDArray
import os

from .coordinate_system import CoordinateSystem
from .time_manager import TimeRestrictedLoader
from .analysis_lines import AnalysisLines, AnalysisLine

logger = logging.getLogger(__name__)


class STIGenerator:
    """Space-Time Image生成器"""
    
    def __init__(self, coordinate_system: CoordinateSystem, analysis_lines: AnalysisLines):
        """初始化STI生成器
        
        Args:
            coordinate_system: 坐标系统
            analysis_lines: 分析线管理器
        """
        self.coordinate_system = coordinate_system
        self.analysis_lines = analysis_lines
        self.stis: List[NDArray] = []
    
    def generate_stis(self, loader: TimeRestrictedLoader,
                     preprocessing_func: Optional[callable] = None,
                     roi: Optional[List] = None) -> tuple[List[NDArray], NDArray]:
        """生成Space-Time Images

        Args:
            loader: 时间限制的视频加载器
            preprocessing_func: 图像预处理函数
            roi: ROI区域，用于分析线裁剪

        Returns:
            tuple: (STI图像列表, 第一帧原始图像)
        """
        logger.info("开始生成Space-Time Images...")
        logger.debug(f"视频信息: FPS={loader.fps:.2f}, 总帧数={loader.total_frames}, 分析帧范围={loader.start_frame}-{loader.end_frame}")
        start_time = time.time()

        frame_count = 0
        progress_interval = 100  # 每100帧显示一次进度
        first_frame_processed = False
        stis = None  # 延迟初始化
        first_original_frame = None  # 保存第一帧原始图像

        # 处理视频帧
        while loader.has_images():
            frame = loader.read()
            if frame is None:
                logger.warning(f"第{frame_count + 1}帧读取失败，跳过")
                continue

            # 记录第一帧信息
            if not first_frame_processed:
                logger.debug(f"第一帧信息: 形状={frame.shape}, 数据类型={frame.dtype}")
                first_original_frame = frame.copy()  # 保存第一帧原始图像
                first_frame_processed = True

            # 应用预处理
            original_shape = frame.shape
            if preprocessing_func:
                frame = preprocessing_func(frame)

            # 确保分析线已生成
            if not self.analysis_lines.lines:
                logger.debug(f"生成分析线，图像尺寸: {frame.shape[:2]}")
                self.analysis_lines.generate_lines(frame.shape[:2], roi)
                logger.info(f"生成了{len(self.analysis_lines.lines)}条分析线")

                # 初始化STI列表（现在分析线已生成）
                line_count = len(self.analysis_lines.lines)
                stis = [[] for _ in range(line_count)]
                logger.debug(f"初始化STI列表，分析线数量: {line_count}")

                # 记录分析线详细信息
                for i, line in enumerate(self.analysis_lines.lines):
                    logger.debug(f"分析线{i}: 起点{line.start_point} -> 终点{line.end_point}, 长度{line.length}像素")

            # 提取每条分析线的像素
            for i, line in enumerate(self.analysis_lines.lines):
                line_pixels = self.analysis_lines.get_line_pixels(line, frame)
                stis[i].append(line_pixels)

                # 记录第一帧的像素提取信息
                if frame_count == 0:
                    logger.debug(f"分析线{i}像素提取: 长度={len(line_pixels)}, 数据范围=[{line_pixels.min():.1f}, {line_pixels.max():.1f}]")

            frame_count += 1

            # 显示进度
            if frame_count % progress_interval == 0:
                progress = loader.get_progress()
                current_time = loader.get_current_time()
                logger.info(f"STI生成进度: {progress*100:.1f}% (第{frame_count}帧, {current_time:.1f}s)")

                # 记录内存使用情况
                total_pixels = sum(len(sti_list) * len(sti_list[0]) for sti_list in stis if sti_list)
                memory_mb = total_pixels * 4 / (1024 * 1024)  # 假设float32
                logger.debug(f"当前STI内存使用: {memory_mb:.1f}MB")

        # 转换为NumPy数组
        self.stis = []
        for i, sti_list in enumerate(stis):
            if sti_list:
                sti_array = np.array(sti_list)
                self.stis.append(sti_array)

                # 详细的STI统计信息
                logger.debug(f"STI {i}: 形状={sti_array.shape}, 数据类型={sti_array.dtype}")
                logger.debug(f"STI {i}: 数据范围=[{sti_array.min():.1f}, {sti_array.max():.1f}], 均值={sti_array.mean():.1f}")
                logger.debug(f"STI {i}: 内存大小={sti_array.nbytes / (1024*1024):.1f}MB")
            else:
                logger.warning(f"STI {i}: 没有数据")

        elapsed_time = time.time() - start_time
        fps_processed = frame_count / elapsed_time if elapsed_time > 0 else 0
        logger.info(f"STI生成完成: 处理了{frame_count}帧, 生成了{len(self.stis)}个STI, 耗时{elapsed_time:.2f}秒")
        logger.info(f"处理性能: {fps_processed:.1f} FPS")

        # 保存STI数据到.npy文件
        try:
            # 确保输出目录存在
            output_dir = "data/output"
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名（使用时间戳确保唯一性）
            sti_filename = f"stiv_sti_data.npy"
            sti_filepath = os.path.join(output_dir, sti_filename)
            
            # 保存STI数据 - 使用字典格式保存每个STI，避免形状不一致问题
            sti_data = {}
            for i, sti_array in enumerate(self.stis):
                sti_data[f'sti_{i}'] = sti_array
                logger.debug(f"准备保存STI {i}: 形状={sti_array.shape}")
            
            # 保存为npz格式，可以保存多个数组
            np.savez(sti_filepath.replace('.npy', '.npz'), **sti_data)
            logger.info(f"STI数据已保存到: {sti_filepath.replace('.npy', '.npz')}")
            
        except Exception as e:
            logger.error(f"保存STI数据时出错: {e}")
            # 记录详细的错误信息
            logger.debug(f"self.stis类型: {type(self.stis)}")
            if self.stis:
                for i, sti in enumerate(self.stis):
                    logger.debug(f"STI {i}: 类型={type(sti)}, 形状={sti.shape if hasattr(sti, 'shape') else '无shape属性'}")

        return self.stis, first_original_frame


class STIFilter:
    """STI滤波器
    
    基于论文方法的改进滤波算法
    """
    
    def __init__(self, config):
        """初始化STI滤波器
        
        Args:
            config: STIV算法配置
        """
        self.config = config
        
        # 创建滤波窗口
        w_size = config.filter_window
        w_mn = (1 - np.cos(2 * math.pi * np.arange(w_size) / w_size)) / 2
        w_mn = np.tile(w_mn, (w_size, 1))
        self._filter_win = w_mn * w_mn.T
        
        # 滤波参数
        self._vh_filter = 1  # 垂直和水平滤波器宽度
        self._polar_filter_width = config.polar_filter_width
    
    def filter_sti(self, sti: NDArray, sti_id: int = 0) -> NDArray:
        """滤波STI图像

        Args:
            sti: 输入STI图像
            sti_id: STI编号（用于调试）

        Returns:
            滤波后的STI图像
        """
        # logger.debug(f"开始滤波STI {sti_id}, 原始形状: {sti.shape}")
        # logger.debug(f"STI {sti_id} 输入数据: 范围=[{sti.min():.1f}, {sti.max():.1f}], 均值={sti.mean():.1f}")

        filter_start_time = time.time()

        # 1. 裁剪和调整大小
        x = min(sti.shape)
        sti_cropped = sti[:, :x] if x == sti.shape[0] else sti[:x, :]
        # logger.debug(f"STI {sti_id} 裁剪: {sti.shape} -> {sti_cropped.shape}")

        # 调整到600x600（论文中的标准尺寸）
        sti_resized = cv2.resize(sti_cropped, (600, 600), interpolation=cv2.INTER_LINEAR)
        # logger.debug(f"STI {sti_id} 调整大小: {sti_cropped.shape} -> {sti_resized.shape}")

        # 2. 窗口函数滤波
        # logger.debug(f"STI {sti_id} 滤波前: sti_resized.shape={sti_resized.shape}, filter_win.shape={self._filter_win.shape}")
        sti_windowed = self._conv2d(sti_resized, self._filter_win)
        # logger.debug(f"STI {sti_id} 窗口滤波: 数据范围=[{sti_windowed.min():.1f}, {sti_windowed.max():.1f}]")

        # 3. 傅里叶变换和频域滤波
        sti_ft = np.abs(np.fft.fftshift(np.fft.fft2(sti_windowed)))
        # logger.debug(f"STI {sti_id} FFT: 频域能量范围=[{sti_ft.min():.1f}, {sti_ft.max():.1f}]")

        # 滤除垂直和水平模式
        c_x = int(sti_ft.shape[0] / 2)
        c_y = int(sti_ft.shape[1] / 2)
        original_energy = sti_ft.sum()
        sti_ft[c_x - self._vh_filter : c_x + self._vh_filter, :] = 0
        sti_ft[:, c_y - self._vh_filter : c_y + self._vh_filter] = 0
        filtered_energy = sti_ft.sum()
        energy_ratio = filtered_energy / original_energy if original_energy > 0 else 0
        # logger.debug(f"STI {sti_id} 垂直/水平滤波: 保留能量比例={energy_ratio:.3f}")

        # 4. 极坐标变换
        sti_ft_polar = self._to_polar_system(sti_ft)
        # logger.debug(f"STI {sti_id} 极坐标变换: {sti_ft.shape} -> {sti_ft_polar.shape}")

        # 5. 极坐标域滤波
        polar_mask = self._generate_polar_mask(sti_ft_polar)
        mask_coverage = polar_mask.sum() / polar_mask.size
        # logger.debug(f"STI {sti_id} 极坐标掩码: 覆盖率={mask_coverage:.3f}")

        sti_ft_polar_filtered = sti_ft_polar * polar_mask
        polar_energy_ratio = sti_ft_polar_filtered.sum() / sti_ft_polar.sum() if sti_ft_polar.sum() > 0 else 0
        # logger.debug(f"STI {sti_id} 极坐标滤波: 保留能量比例={polar_energy_ratio:.3f}")

        # 6. 逆变换
        sti_ft_filtered = self._to_polar_system(sti_ft_polar_filtered, "invert")
        sti_filtered = np.abs(np.fft.ifft2(np.fft.ifftshift(sti_ft_filtered)))
        # logger.debug(f"STI {sti_id} 逆FFT: 数据范围=[{sti_filtered.min():.1f}, {sti_filtered.max():.1f}]")

        # 7. 调整回原始尺寸
        original_size = sti_cropped.shape
        sti_final = cv2.resize(sti_filtered, (original_size[1], original_size[0]),
                              interpolation=cv2.INTER_AREA)
        # logger.debug(f"STI {sti_id} 恢复尺寸: {sti_filtered.shape} -> {sti_final.shape}")

        # 8. 归一化到0-255
        min_val, max_val = sti_final.min(), sti_final.max()
        sti_normalized = np.interp(sti_final, (min_val, max_val), (0, 255))
        # logger.debug(f"STI {sti_id} 归一化: [{min_val:.1f}, {max_val:.1f}] -> [0, 255]")

        filter_time = time.time() - filter_start_time
        # logger.debug(f"STI {sti_id} 滤波完成: 输出形状={sti_normalized.shape}, 耗时={filter_time:.3f}秒")

        return sti_normalized.astype(np.uint8)
    
    @staticmethod
    def _conv2d(a: NDArray, f: NDArray) -> NDArray:
        """2D卷积"""
        s = f.shape + tuple(np.subtract(a.shape, f.shape) + 1)
        strd = np.lib.stride_tricks.as_strided
        sub_m = strd(a, shape=s, strides=a.strides * 2)
        return np.einsum("ij,ijkl->kl", f, sub_m)
    
    @staticmethod
    def _to_polar_system(img: np.ndarray, option: str = "convert") -> NDArray:
        """极坐标变换"""
        if option == "invert":
            flag = cv2.WARP_INVERSE_MAP
        else:
            flag = cv2.WARP_FILL_OUTLIERS
        
        row, col = img.shape
        cent = (int(col / 2), int(row / 2))
        max_radius = int(np.sqrt(row**2 + col**2) / 2)
        return cv2.linearPolar(img, cent, max_radius, flag)
    
    def _generate_polar_mask(self, polar_img: NDArray) -> NDArray:
        """生成极坐标掩码"""
        # 计算积分谱分布
        isd = np.sum(polar_img.T, axis=0)
        main_freqs = self._get_main_freqs(isd)
        mask = np.zeros(polar_img.shape)
        
        mask = self._apply_angle(mask, main_freqs[0], isd)
        return self._apply_angle(mask, main_freqs[1], isd)
    
    @staticmethod
    def _get_main_freqs(isd: NDArray) -> List[int]:
        """获取主要频率"""
        main_freqs = []
        main_freqs.append(np.argmax(isd))
        if main_freqs[0] < len(isd) / 2:
            main_freqs.append(main_freqs[0] + len(isd) / 2)
        else:
            main_freqs.append(main_freqs[0] - len(isd) / 2)
        return main_freqs
    
    def _apply_angle(self, mask: NDArray, freq: float, isd: NDArray) -> NDArray:
        """应用角度掩码"""
        x = int(freq - self._polar_filter_width)
        y = int(freq + self._polar_filter_width)
        if x < 0:
            x = 0
            mask[x + len(isd) :, :] = 1
        elif y > len(isd):
            y = len(isd)
            mask[: y - len(isd), :] = 1
        mask[x:y, :] = 1
        return mask


class MotionCalculator:
    """运动计算器
    
    计算STI中的运动模式和速度
    """
    
    def __init__(self, coordinate_system: CoordinateSystem, config, analysis_lines_config=None):
        """初始化运动计算器

        Args:
            coordinate_system: 坐标系统
            config: STIV算法配置
            analysis_lines_config: 分析线配置（包含flow_direction）
        """
        self.coordinate_system = coordinate_system
        self.config = config
        self.analysis_lines_config = analysis_lines_config
        self.analysis_data = []  # 用于收集分析数据
    
    def calculate_velocity_fft(self, sti: NDArray, fps: float, sti_id: int = 0) -> Tuple[float, NDArray, float]:
        """使用FFT方法计算速度

        Args:
            sti: 滤波后的STI图像
            fps: 视频帧率
            sti_id: STI编号（用于数据收集）

        Returns:
            (速度值, 可视化掩码, 角度差异)
        """
        logger.debug(f"FFT速度计算: STI形状={sti.shape}, FPS={fps}")
        calc_start_time = time.time()

        # 初始化当前分析线的数据收集字典
        current_data = {'sti_id': sti_id}

        # 边缘检测
        sti_canny = cv2.Canny(sti, 10, 10)
        edge_count = np.count_nonzero(sti_canny)
        edge_percentage = edge_count/sti_canny.size*100
        logger.debug(f"边缘检测: 检测到{edge_count}个边缘像素 ({edge_percentage:.1f}%)")

        # 收集边缘检测数据
        current_data.update({
            'edge_pixels': edge_count,
            'edge_ratio': edge_percentage,
            'data_min': sti.min(),
            'data_max': sti.max()
        })

        # 边缘检测质量评估
        if edge_percentage < 0.5:
            logger.warning(f"边缘像素比例过低 ({edge_percentage:.1f}%)，可能影响后续分析质量")
        elif edge_percentage > 20:
            logger.warning(f"边缘像素比例过高 ({edge_percentage:.1f}%)，可能存在噪声")

        # 方形化处理
        sti_padded = self._squarify(sti_canny)
        logger.debug(f"方形化: {sti_canny.shape} -> {sti_padded.shape}")

        # FFT和极坐标变换
        sti_ft = np.abs(np.fft.fftshift(np.fft.fft2(sti_padded)))
        fft_energy_total = sti_ft.sum()
        fft_energy_max = sti_ft.max()
        fft_energy_mean = sti_ft.mean()
        logger.debug(f"FFT: 频域能量范围=[{sti_ft.min():.1f}, {fft_energy_max:.1f}], 总能量={fft_energy_total:.1f}, 平均能量={fft_energy_mean:.1f}")

        sti_ft_polar = STIFilter._to_polar_system(sti_ft)
        logger.debug(f"极坐标变换: {sti_ft.shape} -> {sti_ft_polar.shape}")

        # 极坐标变换质量检查
        polar_energy_total = sti_ft_polar.sum()
        energy_preservation_ratio = polar_energy_total / fft_energy_total if fft_energy_total > 0 else 0
        logger.debug(f"极坐标变换能量保持率: {energy_preservation_ratio:.3f}")

        # 收集频域分析数据
        current_data.update({
            'fft_energy_range': (sti_ft.min(), fft_energy_max),
            'polar_energy_ratio': energy_preservation_ratio
        })

        # 积分谱分布分析
        isd = np.sum(sti_ft_polar.T, axis=0)
        main_freqs = STIFilter._get_main_freqs(isd)
        isd_max = isd.max()
        isd_mean = isd.mean()
        isd_peak_ratio = isd_max / isd_mean if isd_mean > 0 else 0
        logger.debug(f"主要频率: {main_freqs}, ISD峰值={isd[main_freqs[0]]:.1f}")
        logger.debug(f"ISD分析: 最大值={isd_max:.1f}, 平均值={isd_mean:.1f}, 峰值比={isd_peak_ratio:.2f}")

        # ISD质量评估
        if isd_peak_ratio < 1.5:
            logger.warning(f"ISD峰值不明显 (峰值比={isd_peak_ratio:.2f})，可能导致频率检测不准确")

        # 频率合理性限制和智能选择
        freq = main_freqs[0]

        # 收集频率检测数据
        current_data.update({
            'main_frequency': freq,
            'isd_peak': isd[freq] if freq < len(isd) else 0,
            'peak_ratio': isd_peak_ratio
        })
        polar_shape_0 = sti_ft_polar.shape[0]
        polar_shape_1 = sti_ft_polar.shape[1]

        # 直接使用检测到的频率，恢复原始逻辑
        angle0 = 2 * math.pi * freq / polar_shape_0
        angle1 = 2 * math.pi * freq / polar_shape_1
        angle = (angle0 + angle1) / 2
        angle_deg = math.degrees(angle)
        logger.debug(f"角度计算: freq={freq}, polar_shape=({polar_shape_0},{polar_shape_1})")
        logger.debug(f"角度计算: angle0={math.degrees(angle0):.2f}°, angle1={math.degrees(angle1):.2f}°, angle={angle_deg:.2f}°")

        # 速度计算
        ppm = self.coordinate_system.calculate_ppm()
        tan_angle = math.tan(angle)
        raw_velocity = tan_angle * fps / ppm
        logger.debug(f"速度计算详细: tan({angle_deg:.2f}°)={tan_angle:.6f}, fps={fps}, ppm={ppm:.6f}")
        logger.debug(f"速度计算公式: {tan_angle:.6f} * {fps} / {ppm:.6f} = {raw_velocity:.6f}")

        # 收集角度计算数据
        current_data.update({
            'angle_deg': angle_deg,
            'tan_value': tan_angle
        })

        # 检查tan值是否异常
        if abs(tan_angle) > 100:
            logger.warning(f"tan(angle)值异常大 ({tan_angle:.6f})，可能导致速度计算异常")

        # 根据配置的流向角度调整速度符号
        velocity, angle_diff = self._adjust_velocity_sign(raw_velocity, angle_deg)
        velocity = round(velocity, 4)
        logger.debug(f"速度计算: ppm={ppm:.6f}, raw_velocity={raw_velocity:.6f}, adjusted_velocity={velocity:.6f} m/s")

        # 物理约束检查和修正
        velocity = self._apply_physical_constraints(velocity)

        # 速度合理性检查
        if abs(velocity) > 10:  # 超过10 m/s认为异常
            logger.warning(f"计算出的速度异常大 ({velocity:.6f} m/s)，可能存在计算错误")

        # 收集最终速度数据
        current_data.update({
            'raw_velocity': raw_velocity,
            'final_velocity': velocity,
            'angle_diff': angle_diff
        })

        # 将当前分析线数据添加到总数据列表中
        self.analysis_data.append(current_data)

        # 生成可视化掩码
        mask = np.zeros(sti.shape)
        mask = self._draw_angle(mask, angle,
                               (int(sti.shape[1] / 2), int(sti.shape[0] / 2)),
                               thick=10, amplitude=80)

        calc_time = time.time() - calc_start_time
        logger.debug(f"FFT速度计算完成: 耗时={calc_time:.3f}秒")

        return velocity, mask, angle_diff
    
    def calculate_velocity_gmt(self, sti: NDArray, fps: float) -> Tuple[float, NDArray, float]:
        """使用GMT方法计算速度

        Args:
            sti: 滤波后的STI图像
            fps: 视频帧率

        Returns:
            (速度值, 可视化掩码, 角度差异)
        """
        window_width = int(self.config.window_shape[0] / 2)
        window_height = int(self.config.window_shape[1] / 2)
        
        height, width = sti.shape
        
        angle_accumulated = 0
        coherence_total = 0
        mask = np.zeros(sti.shape)
        
        # 滑动窗口处理
        s = window_width
        while s + window_width < height:
            e = window_height
            while e + window_height < width:
                # 提取窗口
                window = sti[s-window_width:s+window_width, 
                           e-window_height:e+window_height]
                
                # 计算局部角度和相干性
                angle, coherence = self._process_sti_window(window)
                
                # 累积加权角度
                angle_accumulated += angle * coherence
                coherence_total += coherence
                
                # 绘制角度向量
                mask = self._draw_angle(mask, angle, (e, s))
                
                e += int(self.config.overlap) if self.config.overlap > 0 else window_height
            s += int(self.config.overlap) if self.config.overlap > 0 else window_width
        
        # 计算平均角度
        if coherence_total > 0:
            mean_angle = angle_accumulated / coherence_total
            raw_velocity = self._angle_to_velocity(mean_angle, fps)
            # 根据配置的流向角度调整速度符号
            mean_angle_deg = math.degrees(mean_angle)
            velocity, angle_diff = self._adjust_velocity_sign(raw_velocity, mean_angle_deg)
        else:
            velocity = 0.0
            angle_diff = 0.0

        return velocity, mask, angle_diff

    def _output_analysis_summary(self, analysis_data):
        """输出所有分析线的核心参数汇总"""
        try:
            logger.info("=" * 60)
            logger.info("STIV算法核心参数汇总分析")
            logger.info("=" * 60)

            # 边缘检测汇总
            logger.info("【边缘检测汇总】")
            for i, data in enumerate(analysis_data):
                if 'edge_pixels' in data and 'edge_ratio' in data:
                    logger.info(f"  分析线{i:2d}: 边缘像素={data['edge_pixels']:5d}, 比例={data['edge_ratio']:5.1f}%, 数据范围=[{data.get('data_min', 0):.1f}, {data.get('data_max', 0):.1f}]")

            # 频域分析汇总
            logger.info("【频域分析汇总】")
            for i, data in enumerate(analysis_data):
                if 'fft_energy_range' in data and 'polar_energy_ratio' in data:
                    energy_min, energy_max = data['fft_energy_range']
                    logger.info(f"  分析线{i:2d}: FFT能量=[{energy_min:.1f}, {energy_max:.1f}], 极坐标能量保持率={data['polar_energy_ratio']:.3f}")

            # 频率检测汇总
            logger.info("【频率检测汇总】")
            for i, data in enumerate(analysis_data):
                if 'main_frequency' in data and 'isd_peak' in data and 'peak_ratio' in data:
                    logger.info(f"  分析线{i:2d}: 主频率={data['main_frequency']:3d}, ISD峰值={data['isd_peak']:.1e}, 峰值比={data['peak_ratio']:.2f}")

            # 角度计算汇总
            logger.info("【角度计算汇总】")
            for i, data in enumerate(analysis_data):
                if 'angle_deg' in data and 'tan_value' in data:
                    logger.info(f"  分析线{i:2d}: 角度={data['angle_deg']:6.2f}°, tan值={data['tan_value']:8.6f}")

            # 速度计算汇总
            logger.info("【速度计算汇总】")
            for i, data in enumerate(analysis_data):
                if 'raw_velocity' in data and 'final_velocity' in data and 'angle_diff' in data:
                    logger.info(f"  分析线{i:2d}: 原始速度={data['raw_velocity']:8.6f}, 最终速度={data['final_velocity']:8.6f}, 角度差异={data['angle_diff']:6.1f}°")

            logger.info("=" * 60)

        except Exception as e:
            logger.debug(f"输出分析汇总时出错: {e}")

    def _apply_physical_constraints(self, velocity):
        """应用物理约束检查和修正"""
        try:
            # 从配置中获取约束参数（如果有的话）
            max_velocity = getattr(self, 'max_velocity', 8.0)  # 默认8 m/s上限
            min_velocity = getattr(self, 'min_velocity', None)  # 默认无下限

            original_velocity = velocity

            # 检查上限约束
            if abs(velocity) > max_velocity:
                logger.warning(f"速度超出物理上限: {velocity:.3f} m/s > {max_velocity} m/s")
                # 将异常大的速度限制在合理范围内
                velocity = max_velocity if velocity > 0 else -max_velocity
                logger.info(f"速度约束修正: {original_velocity:.3f} -> {velocity:.3f} m/s")

            # 检查下限约束（如果设置了的话）
            if min_velocity is not None and abs(velocity) < min_velocity:
                logger.debug(f"速度低于设定下限: {abs(velocity):.3f} m/s < {min_velocity} m/s")
                # 对于过小的速度，可以选择设为0或保持原值
                # 这里选择保持原值，避免过度限制低速检测

            return velocity

        except Exception as e:
            logger.debug(f"应用物理约束时出错: {e}")
            return velocity

    def _angle_to_velocity(self, angle: float, fps: float, ppm: float) -> float:
        """将角度转换为速度

        Args:
            angle: STI中的角度（弧度）
            fps: 视频帧率

        Returns:
            速度值（m/s）
        """
        return math.tan(angle) * fps / ppm

    def _adjust_velocity_sign(self, raw_velocity: float, motion_angle_deg: float) -> float:
        """根据配置的流向角度调整速度符号

        Args:
            raw_velocity: 原始计算的速度值
            motion_angle_deg: STI中检测到的运动角度（度）

        Returns:
            调整符号后的速度值
        """
        if not self.analysis_lines_config:
            # 如果没有分析线配置，保持原始符号
            return raw_velocity

        # 获取配置的流向角度
        configured_flow_direction = self.analysis_lines_config.get("flow_direction", 0.0)

        # 计算角度差异
        angle_diff = abs(motion_angle_deg - configured_flow_direction)

        # 处理角度环绕（0-360度）
        if angle_diff > 180:
            angle_diff = 360 - angle_diff

        # 判断运动方向与配置流向的一致性
        # 如果角度差异小于90度，认为是同向（正值）
        # 如果角度差异大于90度，认为是反向（负值）
        if angle_diff <= 90:
            # 同向运动，速度为正值
            adjusted_velocity = abs(raw_velocity)
            direction_status = "同向"
        else:
            # 反向运动，速度为负值
            adjusted_velocity = -abs(raw_velocity)
            direction_status = "反向"

        logger.debug(f"流向比较: 配置流向={configured_flow_direction:.1f}°, "
                    f"检测运动角度={motion_angle_deg:.1f}°, "
                    f"角度差异={angle_diff:.1f}°, "
                    f"判定={direction_status}, "
                    f"原始速度={raw_velocity:.3f} -> 调整后速度={adjusted_velocity:.3f}")

        # 返回调整后的速度和角度差异数据
        return adjusted_velocity, angle_diff
    
    def _process_sti_window(self, window: np.ndarray) -> Tuple[float, float]:
        """处理STI窗口"""
        # Sobel梯度计算
        sobelx = cv2.Sobel(window, cv2.CV_64F, 0, 1, ksize=self.config.ksize)
        sobelt = cv2.Sobel(window, cv2.CV_64F, 1, 0, ksize=self.config.ksize)
        
        if sobelx.sum() == 0 and sobelt.sum() == 0:
            return 0, 0
        
        # 结构张量计算
        j_xx = (sobelx * sobelx).sum()
        j_tt = (sobelt * sobelt).sum()
        j_xt = (sobelx * sobelt).sum()
        
        # 角度和相干性计算
        angle = math.atan2(2 * j_xt, j_tt - j_xx) / 2
        coherence = math.sqrt((j_tt - j_xx) ** 2 + 4 * j_xt**2) / (j_xx + j_tt)
        
        return angle, coherence
    
    @staticmethod
    def _squarify(matrix: NDArray) -> NDArray:
        """方形化矩阵"""
        a, b = matrix.shape
        padding = ((0, 0), (0, a - b)) if a > b else ((0, b - a), (0, 0))
        return np.pad(matrix, padding)
    
    @staticmethod
    def _draw_angle(image: NDArray, angle: float, position: Tuple[int, int],
                   thick: int = 1, amplitude: int = 10) -> NDArray:
        """在图像上绘制角度线"""
        x, y = position
        end_x = int(x + amplitude * math.sin(angle))
        end_y = int(y - amplitude * math.cos(angle))
        cv2.line(image, (x, y), (end_x, end_y), 255, thick)
        return image
