"""
STIV - Space-Time Image Velocimetry算法

这是STIV算法的实现，具有以下特性：
1. 坐标系统兼容性 - 支持像素+距离和经纬度两种坐标系统
2. 视频时间段选择 - 内置时间段选择功能
3. 像素比例自动计算 - 基于标定信息自动计算ppm
4. 分析线方向适配 - 支持任意角度的水流方向

主要模块：
- coordinate_system: 坐标系统管理
- time_manager: 时间段管理
- analysis_lines: 分析线管理
- stiv_core: 核心STIV算法
- stiv_processor: 主处理器

配置管理已统一到 src/utils/config_manager.py
"""

from .stiv_processor import STIVProcessor
from .coordinate_system import CoordinateSystem
from .time_manager import TimeManager
from .analysis_lines import AnalysisLines

__version__ = "1.0.0"
__author__ = "Augment Agent"

__all__ = [
    "STIVProcessor",
    "CoordinateSystem",
    "TimeManager",
    "AnalysisLines"
]
