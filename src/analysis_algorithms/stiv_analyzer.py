"""
STIV分析器统一接口

提供STIV v2算法的统一接口，类似于openpiv_piv_analyzer.py
"""

import json
import yaml
import logging
import time
from typing import Dict, Any, Optional
from pathlib import Path

from .stiv import STIVProcessor
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class STIVAnalyzer:
    """STIV分析器
    
    提供统一的STIV分析接口，支持新旧配置格式
    """
    
    def __init__(self, config_path: str):
        """初始化STIV分析器

        Args:
            config_path: 配置文件路径
        """
        self.config_manager = ConfigManager(config_path)
        self.config_path = config_path

        # 验证STIV配置
        is_valid, errors = self.config_manager.validate_config("stiv")
        if not is_valid:
            raise ValueError(f"STIV配置验证失败: {'; '.join(errors)}")

        logger.info(f"STIV分析器初始化完成 (配置文件: {config_path})")
    
    def analyze(self, video_path: str, method: Optional[str] = None,
               save_debug: Optional[bool] = None, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """执行STIV分析

        Args:
            video_path: 视频文件路径
            method: 运动计算方法，"fft" 或 "gmt"，None时使用配置中的方法
            save_debug: 是否保存调试图像，None时从配置读取
            output_dir: 输出目录

        Returns:
            分析结果字典
        """
        if not Path(video_path).exists():
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        # 从配置读取调试参数（如果未明确指定）
        if save_debug is None:
            save_debug = self.config_manager.is_debug_enabled()

        logger.info(f"开始STIV分析: {Path(video_path).name}")
        logger.info(f"使用方法: {method or '配置默认'}")
        logger.info(f"调试模式: save_debug={save_debug}")

        start_time = time.time()

        try:
            # 创建处理器
            processor = STIVProcessor.from_config_manager(self.config_manager, video_path)

            # 创建预处理函数
            from .stiv.stiv_processor import create_preprocessing_function
            preprocess_func = create_preprocessing_function(processor.config.preprocessing)

            # 执行分析
            results = processor.process(preprocess_func, method, save_debug)
            
            # 添加元数据
            elapsed_time = time.time() - start_time
            results["metadata"] = {
                "video_path": video_path,
                "method": method or processor.config.algorithm.method,
                "analysis_time": elapsed_time,
                "config_source": self.config_path,
                "stiv_version": "1.0"
            }

            # 添加分析信息
            results["analysis_info"] = {
                "coordinate_system": processor.coordinate_system.get_calibration_info(),
                "time_range": processor.time_manager.get_time_info(),
                "analysis_lines": processor.analysis_lines.get_lines_info()
            }

            logger.info(f"STIV分析完成，耗时 {elapsed_time:.2f}秒")

            # 保存结果
            if output_dir:
                self._save_results(results, output_dir, video_path)

            return results
            
        except Exception as e:
            logger.error(f"STIV分析失败: {e}")
            raise
    
    def _save_results(self, results: Dict[str, Any], output_dir: str, video_path: str):
        """保存分析结果

        Args:
            results: 分析结果
            output_dir: 输出目录
            video_path: 视频文件路径
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成文件名
        video_name = Path(video_path).stem
        timestamp = time.strftime("%Y%m%d_%H%M%S")

        # 保存结果JSON
        result_file = output_dir / f"stiv_result_{video_name}_{timestamp}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"分析结果已保存到: {result_file}")

        # 保存配置
        config_file = output_dir / f"stiv_config_{video_name}_{timestamp}.yaml"
        self.config_manager.save_config(str(config_file))
    
    def get_config_summary(self) -> str:
        """获取配置摘要"""
        stiv_config = self.config_manager.get_algorithm_config("stiv")
        return f"""STIV配置摘要:
  坐标系统: {stiv_config.coordinate_system.type}
  分析线模式: {stiv_config.analysis_lines.mode}
  分析线数量: {stiv_config.analysis_lines.line_count}
  流向角度: {stiv_config.analysis_lines.flow_direction}°
  算法方法: {stiv_config.algorithm.method}
  调试模式: {self.config_manager.is_debug_enabled()}
"""

    def validate_config(self) -> tuple[bool, list[str]]:
        """验证配置"""
        return self.config_manager.validate_config("stiv")
    



def analyze_video_stiv(video_path: str, config_path: str, method: Optional[str] = None,
                      save_debug: Optional[bool] = None, output_dir: Optional[str] = None) -> Dict[str, Any]:
    """分析视频的便捷函数

    Args:
        video_path: 视频文件路径
        config_path: 配置文件路径
        method: 运动计算方法，None时使用配置中的方法
        save_debug: 是否保存调试图像，None时从配置读取
        output_dir: 输出目录

    Returns:
        分析结果
    """
    analyzer = STIVAnalyzer(config_path)
    return analyzer.analyze(video_path, method, save_debug, output_dir)





# 主函数用于命令行调用
def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="STIV v2 视频分析工具")
    parser.add_argument("video_path", help="视频文件路径")
    parser.add_argument("config_path", help="配置文件路径")
    parser.add_argument("--method", choices=["fft", "gmt"], default="fft", help="运动计算方法")
    parser.add_argument("--save-debug", action="store_true", help="保存调试图像")
    parser.add_argument("--output-dir", help="输出目录")
    parser.add_argument("--convert-config", help="转换旧版配置文件路径")
    parser.add_argument("--create-template", action="store_true", help="创建配置模板")
    parser.add_argument("--coord-type", choices=["pixel_distance", "geographic"], 
                       default="pixel_distance", help="坐标系统类型（用于模板创建）")
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname).1s | %(name).20s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    try:
        if args.convert_config:
            # 转换配置
            convert_legacy_config_file(args.convert_config, args.config_path)
        elif args.create_template:
            # 创建模板
            create_config_template(args.video_path, args.config_path, args.coord_type)
        else:
            # 执行分析
            results = analyze_video_stiv(
                args.video_path, 
                args.config_path, 
                args.method, 
                args.save_debug, 
                args.output_dir
            )
            
            # 打印结果摘要
            if "summary" in results:
                summary = results["summary"]
                print(f"\n分析结果摘要:")
                print(f"  平均速度: {summary['mean_velocity']:.3f} m/s")
                print(f"  速度标准差: {summary['velocity_std']:.3f} m/s")
                print(f"  分析线数量: {summary['line_count']}")
                print(f"  计算方法: {summary['method']}")
    
    except Exception as e:
        logger.error(f"执行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
