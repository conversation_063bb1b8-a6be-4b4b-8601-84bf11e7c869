#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenCV Optical Track Velocimetry (OTV) Analyzer
此模块用于通过OpenCV的光流法分析视频片段并计算流速。

重构版本：采用两阶段过滤架构
- 实时过滤阶段：基础特征点过滤
- 后处理过滤阶段：基于pandas/numpy的批量高效过滤
"""

import cv2
import numpy as np
import os
import sys
import time
import yaml
import math
import csv
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib
import pandas as pd
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
matplotlib.use('Agg')  # 使用非交互式后端

# 假设logging_utils在父目录的utils下
try:
    from ..utils.logging_utils import setup_logger # 相对导入
except ImportError:
    # 如果作为脚本直接运行，尝试修改sys.path
    sys.path.append(str(Path(__file__).resolve().parents[2])) # 添加项目根目录到sys.path
    from src.utils.logging_utils import setup_logger

# 设置日志 - 根据文件位置调整logger名称
logger = setup_logger("otv_analyzer")

@dataclass
class TrackingPoint:
    """特征点数据结构"""
    point_id: int
    time: float
    x: float
    y: float
    displacement: float
    angle: float
    start_x: float
    start_y: float

@dataclass
class FilteringResults:
    """过滤结果数据结构"""
    marked_data: pd.DataFrame  # 包含标记的完整数据
    filter_stats: Dict[str, any]
    flow_stats: Optional[Dict[str, float]] = None
    
class OTVFilterPipeline:
    """OTV过滤管道类，封装所有过滤逻辑"""
    
    def __init__(self, config: Dict, pixel_to_meter: float, fps: float):
        self.config = config
        self.pixel_to_meter = pixel_to_meter
        self.fps = fps
        self.direction_concentration_range = config.get('direction_concentration_range', 90)
        self.trajectory_static_threshold = config.get('trajectory_static_threshold', 10.0)
        # 处理分位法过滤阈值：支持单个值（向后兼容）或[A, B]列表
        quartile_config = config.get('quartile_filter_min_points', 20)
        if isinstance(quartile_config, list) and len(quartile_config) == 2:
            self.quartile_filter_min_points_A = quartile_config[0]  # 最小阈值
            self.quartile_filter_min_points_B = quartile_config[1]  # 四分位阈值
        else:
            # 向后兼容：单个值时，A=值/2, B=值
            self.quartile_filter_min_points_A = max(1, int(quartile_config // 2))
            self.quartile_filter_min_points_B = quartile_config
        self.window_avg_time_seconds = config.get('window_avg_time_seconds', 1.0)
        self.motion_decay_threshold = config.get('motion_decay_threshold', 0.1)
        self.speed_adjustment_factor = config.get('speed_adjustment_factor', 1.0)
        
        # 过滤层级启用控制参数
        filter_enable_config = config.get('filter_enable_layers', [True, True, True, True, True])
        if isinstance(filter_enable_config, list) and len(filter_enable_config) == 5:
            self.enable_initial_filter = filter_enable_config[0]      # 初始特征筛选
            self.enable_direction_filter = filter_enable_config[1]    # 方向过滤
            self.enable_trajectory_filter = filter_enable_config[2]   # 轨迹静态点过滤
            self.enable_quartile_filter = filter_enable_config[3]     # 分位法过滤
            self.enable_window_filter = filter_enable_config[4]       # 时间窗口动态过滤
        else:
            # 默认全部启用
            logger.warning("filter_enable_layers 配置格式错误，使用默认值（全部启用）")
            self.enable_initial_filter = True
            self.enable_direction_filter = True
            self.enable_trajectory_filter = True
            self.enable_quartile_filter = True
            self.enable_window_filter = True
        
    def filter_by_direction(self, df: pd.DataFrame) -> FilteringResults:
        """
        第二层：方向过滤（标记模式）
        基于完整数据集进行方向集中度分析，标记方向不一致的特征点
        
        Args:
            df: 包含所有特征点数据的DataFrame，必须包含'angle'列
            
        Returns:
            FilteringResults: 包含方向标记的完整数据和统计信息
        """
        logger.info("开始执行方向过滤（标记模式）...")
        
        df_marked = df.copy()
        
        # 检查是否启用方向过滤
        if not self.enable_direction_filter:
            logger.info("方向过滤已被禁用，跳过方向过滤")
            df_marked['direction_valid'] = True
            # 计算禁用状态下的流速统计
            flow_stats = self._calculate_flow_stats_for_filter(df_marked, "方向过滤（禁用）")
            return FilteringResults(df_marked, {
                'filter_disabled': True,
                'dominant_direction': None, 
                'valid_count': len(df),
                'total_points': len(df),
                'invalid_count': 0,
                'valid_ratio': 1.0
            }, flow_stats)
        
        if df.empty or 'angle' not in df.columns:
            logger.warning("数据为空或缺少角度信息，跳过方向过滤")
            df_marked['direction_valid'] = True
            return FilteringResults(df_marked, {'dominant_direction': None, 'valid_count': 0})
        
        # 收集所有角度数据
        angles = df['angle'].values
        
        # 方向集中度分析
        dominant_direction, valid_angles_mask = self._analyze_direction_concentration(angles)
        
        # 添加方向过滤标记（不删除数据）
        df_marked['direction_valid'] = valid_angles_mask
        
        # 统计信息
        valid_count = valid_angles_mask.sum()
        stats = {
            'dominant_direction': dominant_direction,
            'total_points': len(df),
            'valid_count': int(valid_count),
            'invalid_count': len(df) - int(valid_count),
            'valid_ratio': float(valid_count) / len(df) if len(df) > 0 else 0
        }
        
        logger.info(f"方向过滤标记完成 - 主导方向: {dominant_direction:.1f}°, "
                   f"有效点: {stats['valid_count']}/{stats['total_points']} ({stats['valid_ratio']:.1%})")
        
        # 计算基于标记的流速统计
        valid_data = df_marked[df_marked['direction_valid']]
        flow_stats = self._calculate_flow_stats_for_filter(valid_data, "方向过滤")
        
        return FilteringResults(df_marked, stats, flow_stats)
    
    def filter_static_trajectories(self, df: pd.DataFrame) -> FilteringResults:
        """
        第三层：轨迹静态点过滤（标记模式）
        基于总位移阈值标记静态点
        
        Args:
            df: 特征点数据DataFrame，必须包含坐标和起始坐标信息
            
        Returns:
            FilteringResults: 包含轨迹动态标记的完整数据和统计信息
        """
        logger.info("开始执行轨迹静态点过滤（标记模式）...")
        
        df_marked = df.copy()
        
        # 检查是否启用轨迹静态点过滤
        if not self.enable_trajectory_filter:
            logger.info("轨迹静态点过滤已被禁用，跳过过滤")
            df_marked['trajectory_dynamic'] = True
            unique_points = df['point_id'].nunique() if not df.empty else 0
            # 计算禁用状态下的流速统计
            flow_stats = self._calculate_flow_stats_for_filter(df_marked, "轨迹静态点过滤（禁用）")
            return FilteringResults(df_marked, {
                'filter_disabled': True,
                'total_unique_points': unique_points,
                'dynamic_count': unique_points,
                'static_count': 0,
                'threshold_used': self.trajectory_static_threshold,
                'dynamic_ratio': 1.0
            }, flow_stats)
        
        if df.empty:
            df_marked['trajectory_dynamic'] = True
            return FilteringResults(df_marked, {'static_count': 0, 'dynamic_count': 0})
        
        # 按特征点ID分组，计算每个点的总位移
        def calculate_total_displacement(group):
            if len(group) == 0:
                return 0
            last_row = group.iloc[-1]
            total_disp = np.sqrt((last_row['x'] - last_row['start_x'])**2 + 
                               (last_row['y'] - last_row['start_y'])**2)
            return total_disp
        
        # 使用pandas groupby计算总位移
        total_displacements = df.groupby('point_id').apply(calculate_total_displacement)
        
        # 确定动态点ID
        dynamic_point_ids = set(total_displacements[total_displacements >= self.trajectory_static_threshold].index)
        
        # 添加轨迹动态标记（不删除数据）
        df_marked['trajectory_dynamic'] = df_marked['point_id'].isin(dynamic_point_ids)
        
        stats = {
            'total_unique_points': len(total_displacements),
            'dynamic_count': len(dynamic_point_ids),
            'static_count': len(total_displacements) - len(dynamic_point_ids),
            'threshold_used': self.trajectory_static_threshold,
            'dynamic_ratio': len(dynamic_point_ids) / len(total_displacements) if len(total_displacements) > 0 else 0
        }
        
        logger.info(f"轨迹静态点过滤标记完成 - 动态点: {stats['dynamic_count']}, "
                   f"静态点: {stats['static_count']}, 阈值: {stats['threshold_used']}像素")
        
        # 计算基于标记的流速统计
        dynamic_data = df_marked[df_marked['trajectory_dynamic']]
        flow_stats = self._calculate_flow_stats_for_filter(dynamic_data, "轨迹静态点过滤")
        
        return FilteringResults(df_marked, stats, flow_stats)
    
    def filter_by_quartile(self, df: pd.DataFrame) -> FilteringResults:
        """
        第四层：分位法异常值过滤（标记模式）
        根据数据量自适应选择过滤策略：
        - 数据量 >= B：四分位法过滤
        - A <= 数据量 < B：八分位法过滤（去掉最小和最大的12.5%）
        - 数据量 < A：不过滤
        
        Args:
            df: 特征点数据DataFrame，必须包含'displacement'列
            
        Returns:
            FilteringResults: 包含分位法标记的完整数据和统计信息
        """
        logger.info("开始执行自适应分位法异常值过滤（标记模式）...")
        
        df_marked = df.copy()
        
        # 检查是否启用分位法过滤
        if not self.enable_quartile_filter:
            logger.info("分位法过滤已被禁用，跳过过滤")
            df_marked['quartile_valid'] = True
            # 计算禁用状态下的流速统计
            flow_stats = self._calculate_flow_stats_for_filter(df_marked, "分位法过滤（禁用）")
            return FilteringResults(df_marked, {
                'filter_disabled': True,
                'quartile_filter_applied': False,
                'original_count': len(df),
                'valid_count': len(df),
                'invalid_count': 0,
                'retention_ratio': 1.0
            }, flow_stats)
        
        if df.empty or 'displacement' not in df.columns:
            df_marked['quartile_valid'] = True
            return FilteringResults(df_marked, {'quartile_filter_applied': False})
        
        # 检查数据点数量，决定过滤策略
        unique_points_count = df['point_id'].nunique()
        
        if unique_points_count < self.quartile_filter_min_points_A:
            # 数据量 < A：不过滤
            logger.info(f"数据点数量 {unique_points_count} < {self.quartile_filter_min_points_A}，跳过分位法过滤")
            df_marked['quartile_valid'] = True
            return FilteringResults(df_marked, {
                'quartile_filter_applied': False,
                'reason': 'insufficient_data_points',
                'unique_points_count': unique_points_count,
                'threshold_A': self.quartile_filter_min_points_A
            })
        
        displacements = df['displacement'].values
        
        if unique_points_count >= self.quartile_filter_min_points_B:
            # 数据量 >= B：使用四分位法
            logger.info(f"数据点数量 {unique_points_count} >= {self.quartile_filter_min_points_B}，使用四分位法过滤")
            
            # 计算四分位数
            q1 = np.percentile(displacements, 25)
            q3 = np.percentile(displacements, 75)
            iqr = q3 - q1
            
            # 设定过滤边界
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            filter_method = "四分位法"
            percentile_info = f"Q1: {q1:.2f}, Q3: {q3:.2f}, IQR: {iqr:.2f}"
            
        else:
            # A <= 数据量 < B：使用八分位法
            logger.info(f"数据点数量 {unique_points_count} 在 [{self.quartile_filter_min_points_A}, {self.quartile_filter_min_points_B}) 范围内，使用八分位法过滤")
            
            # 计算八分位数（去掉最小和最大的12.5%）
            p12_5 = np.percentile(displacements, 12.5)
            p87_5 = np.percentile(displacements, 87.5)
            
            # 设定过滤边界（直接使用12.5%和87.5%百分位数作为边界）
            lower_bound = p12_5
            upper_bound = p87_5
            
            filter_method = "八分位法"
            percentile_info = f"P12.5: {p12_5:.2f}, P87.5: {p87_5:.2f}"
        
        # 添加分位法标记（不删除数据）
        mask = (df['displacement'] >= lower_bound) & (df['displacement'] <= upper_bound)
        df_marked['quartile_valid'] = mask
        
        valid_count = mask.sum()
        stats = {
            'quartile_filter_applied': True,
            'filter_method': filter_method,
            'original_count': len(df),
            'valid_count': int(valid_count),
            'invalid_count': len(df) - int(valid_count),
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'retention_ratio': float(valid_count) / len(df) if len(df) > 0 else 0,
            'unique_points_count': unique_points_count,
            'threshold_A': self.quartile_filter_min_points_A,
            'threshold_B': self.quartile_filter_min_points_B,
            'percentile_info': percentile_info
        }
        
        logger.info(f"{filter_method}过滤标记完成 - 有效: {stats['valid_count']}/{stats['original_count']} "
                   f"({stats['retention_ratio']:.1%}), 边界: [{lower_bound:.2f}, {upper_bound:.2f}]")
        logger.info(f"分位数信息: {percentile_info}")
        
        # 计算基于标记的流速统计
        valid_data = df_marked[df_marked['quartile_valid']]
        flow_stats = self._calculate_flow_stats_for_filter(valid_data, f"{filter_method}过滤")
        
        return FilteringResults(df_marked, stats, flow_stats)
    
    def filter_by_time_window(self, df: pd.DataFrame, fps: float) -> FilteringResults:
        """
        第五层：时间窗口动态过滤（标记模式）
        识别特征点的活跃时间范围，标记运动衰减后的数据
        
        Args:
            df: 特征点数据DataFrame
            fps: 视频帧率
            
        Returns:
            FilteringResults: 包含时间窗口标记的完整数据和统计信息
        """
        logger.info("开始执行时间窗口动态过滤（标记模式）...")
        
        # 检查是否启用时间窗口动态过滤
        if not self.enable_window_filter:
            logger.info("时间窗口动态过滤已被禁用，跳过过滤")
            df_marked = df.copy()
            df_marked['window_active'] = True
            df_marked['window_avg_displacement'] = df_marked.get('displacement', 0)
            # 计算禁用状态下的流速统计
            flow_stats = self._calculate_flow_stats_for_filter(df_marked, "时间窗口动态过滤（禁用）")
            return FilteringResults(df_marked, {
                'filter_disabled': True,
                'window_filter_applied': False,
                'total_data_points': len(df),
                'active_data_points': len(df),
                'inactive_data_points': 0,
                'active_ratio': 1.0
            }, flow_stats)
        
        if df.empty:
            df_marked = df.copy()
            df_marked['window_active'] = True
            df_marked['window_avg_displacement'] = 0
            return FilteringResults(df_marked, {'window_filter_applied': False})
        
        # 计算窗口大小（帧数）
        window_size_frames = max(1, int(self.window_avg_time_seconds * fps))
        
        # 按特征点分组处理
        enhanced_data_list = []
        window_stats = {}
        
        for point_id in df['point_id'].unique():
            point_data = df[df['point_id'] == point_id].sort_values('time').copy()
            
            if len(point_data) < window_size_frames:
                # 数据点太少，全部标记为活跃
                point_data['window_active'] = True
                point_data['window_avg_displacement'] = point_data['displacement']
                enhanced_data_list.append(point_data)
                continue
            
            # 计算滑动窗口平均位移
            point_data['window_avg_displacement'] = point_data['displacement'].rolling(
                window=window_size_frames, min_periods=window_size_frames, center=False
            ).mean().round(3)
            
            # 找到第一个有效的窗口平均位移作为基准
            valid_window_avg = point_data['window_avg_displacement'].dropna()
            if len(valid_window_avg) == 0:
                point_data['window_active'] = True
                point_data['window_avg_displacement'] = point_data['window_avg_displacement'].fillna(
                    point_data['displacement'])
                enhanced_data_list.append(point_data)
                continue
            
            # 使用第一个有效窗口平均作为基准
            first_valid_avg = valid_window_avg.iloc[0]
            threshold_value = first_valid_avg * self.motion_decay_threshold
            
            # 初始化活跃状态
            point_data['window_active'] = True
            
            # 对有效窗口数据进行阈值判断
            valid_indices = point_data['window_avg_displacement'].notna()
            if valid_indices.any():
                valid_data = point_data[valid_indices]
                below_threshold = valid_data['window_avg_displacement'] < threshold_value
                
                if below_threshold.any():
                    # 找到第一次低于阈值的时间点
                    first_below_idx = valid_data[below_threshold].index[0]
                    cutoff_time = point_data.loc[first_below_idx, 'time']
                    
                    # 标记cutoff_time之后的数据为非活跃
                    point_data.loc[point_data['time'] >= cutoff_time, 'window_active'] = False
                    
                    window_stats[point_id] = {
                        'cutoff_time': cutoff_time,
                        'total_duration': point_data['time'].max() - point_data['time'].min(),
                        'active_duration': cutoff_time - point_data['time'].min()
                    }
                else:
                    window_stats[point_id] = {
                        'cutoff_time': None,
                        'total_duration': point_data['time'].max() - point_data['time'].min(),
                        'active_duration': point_data['time'].max() - point_data['time'].min()
                    }
            
            # 填充缺失的窗口平均位移
            point_data['window_avg_displacement'] = point_data['window_avg_displacement'].fillna(
                point_data['displacement'])
            
            enhanced_data_list.append(point_data)
        
        # 合并所有数据
        if enhanced_data_list:
            df_marked = pd.concat(enhanced_data_list, ignore_index=True)
        else:
            df_marked = df.copy()
            df_marked['window_active'] = True
            df_marked['window_avg_displacement'] = df_marked['displacement']
        
        # 统计活跃数据点
        active_count = df_marked['window_active'].sum()
        
        stats = {
            'window_filter_applied': True,
            'window_size_seconds': self.window_avg_time_seconds,
            'window_size_frames': window_size_frames,
            'decay_threshold': self.motion_decay_threshold,
            'total_data_points': len(df),
            'active_data_points': int(active_count),
            'inactive_data_points': len(df_marked) - int(active_count),
            'active_ratio': float(active_count) / len(df_marked) if len(df_marked) > 0 else 0,
            'unique_points_with_cutoff': len([s for s in window_stats.values() if s['cutoff_time'] is not None]),
            'avg_active_duration': np.mean([s['active_duration'] for s in window_stats.values()]) if window_stats else 0
        }
        
        logger.info(f"时间窗口过滤标记完成 - 活跃数据: {stats['active_data_points']}/{stats['total_data_points']} "
                   f"({stats['active_ratio']:.1%}), 平均活跃时长: {stats['avg_active_duration']:.2f}秒")
        
        # 计算基于标记的流速统计
        active_data = df_marked[df_marked['window_active']]
        flow_stats = self._calculate_flow_stats_for_filter(active_data, "时间窗口动态过滤")
        
        return FilteringResults(df_marked, stats, flow_stats)
    
    def _analyze_direction_concentration(self, angles: np.ndarray) -> Tuple[float, np.ndarray]:
        """
        分析方向集中度，找到最集中的方向区域
        
        Args:
            angles: 角度数组
            
        Returns:
            tuple: (主导方向, 有效角度掩码)
        """
        if len(angles) == 0:
            return 0.0, np.array([])
        
        # 创建角度直方图
        hist_bins = np.arange(0, 361, 10)
        hist, _ = np.histogram(angles, bins=hist_bins)
        
        # 使用滑动窗口找到最集中的区域
        window_size = self.direction_concentration_range // 10
        max_concentration = 0
        dominant_center_bin = 0
        
        for i in range(len(hist) - window_size + 1):
            window_sum = np.sum(hist[i:i+window_size])
            if window_sum > max_concentration:
                max_concentration = window_sum
                dominant_center_bin = i + window_size // 2
        
        # 计算主导方向
        dominant_direction = dominant_center_bin * 10 + 5
        
        # 计算有效角度范围
        half_range = self.direction_concentration_range // 2
        min_valid_angle = dominant_direction - half_range
        max_valid_angle = dominant_direction + half_range
        
        # 处理角度循环
        if min_valid_angle < 0:
            min_valid_angle += 360
        if max_valid_angle > 360:
            max_valid_angle -= 360
        
        # 创建有效角度掩码
        if min_valid_angle <= max_valid_angle:
            valid_mask = (angles >= min_valid_angle) & (angles <= max_valid_angle)
        else:
            valid_mask = (angles >= min_valid_angle) | (angles <= max_valid_angle)
        
        return dominant_direction, valid_mask
    
    def _calculate_flow_stats_for_filter(self, df: pd.DataFrame, filter_name: str) -> Dict[str, float]:
        """
        为过滤后的数据计算流速统计
        
        Args:
            df: 过滤后的数据DataFrame
            filter_name: 过滤器名称，用于日志
            
        Returns:
            Dict: 流速统计信息
        """
        if df.empty:
            logger.warning(f"{filter_name}: 数据为空，无法计算流速")
            return {
                'avg_speed': 0.0,
                'min_speed': 0.0,
                'max_speed': 0.0,
                'valid_frames': 0,
                'total_data_points': 0
            }
        
        # 按时间分组，计算每帧的平均位移
        frame_avg_displacement = df.groupby('time')['displacement'].mean()
        
        if frame_avg_displacement.empty:
            logger.warning(f"{filter_name}: 无有效帧数据")
            return {
                'avg_speed': 0.0,
                'min_speed': 0.0,
                'max_speed': 0.0,
                'valid_frames': 0,
                'total_data_points': len(df)
            }
        
        # 计算每帧的速度：平均位移 * 像素转换系数 * 帧率 * 调整系数
        frame_speeds = frame_avg_displacement * self.pixel_to_meter * self.fps * self.speed_adjustment_factor
        
        # 计算统计指标
        stats = {
            'avg_speed': float(frame_speeds.mean()),
            'min_speed': float(frame_speeds.min()),
            'max_speed': float(frame_speeds.max()),
            'valid_frames': len(frame_speeds),
            'total_data_points': len(df),
            'std_speed': float(frame_speeds.std()),
            'median_speed': float(frame_speeds.median())
        }
        
        logger.info(f"{filter_name}后流速统计 - 平均: {stats['avg_speed']:.4f} m/s, "
                   f"范围: [{stats['min_speed']:.4f}, {stats['max_speed']:.4f}] m/s, "
                   f"有效帧数: {stats['valid_frames']}")
        
        return stats


def prepare_video_processing(cap, roi_points, feature_params, lk_params, config_params):
    """
    基础图像预处理模块
    
    Returns:
        dict: 包含预处理后的所有必要数据
    """
    # 初始化默认参数
    if feature_params is None:
        feature_params = dict(maxCorners=500, qualityLevel=0.01, minDistance=5, blockSize=7)
    if lk_params is None:
        lk_params = dict(winSize=(15, 15), maxLevel=2, criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03))
    
    # 获取特征检测方案配置
    feature_detection_methods = config_params.get('feature_detection_methods', ['goodFeaturesToTrack', 'orb']) if config_params else ['goodFeaturesToTrack', 'orb']
    enable_fallback = config_params.get('enable_fallback', True) if config_params else True
    
    # 获取视频基本信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    # 读取第一帧获取尺寸信息
    original_pos = cap.get(cv2.CAP_PROP_POS_FRAMES)
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
    ret, first_frame = cap.read()
    if not ret:
        logger.error("无法读取视频第一帧")
        return None
    
    # 创建ROI掩膜
    frame_gray = cv2.cvtColor(first_frame, cv2.COLOR_BGR2GRAY)
    roi_mask = np.zeros_like(frame_gray)
    if roi_points is not None and len(roi_points) > 0:
        roi_points_array = np.array(roi_points, dtype=np.int32)
        roi_points_array = sort_polygon_points_clockwise(roi_points_array)
        cv2.fillPoly(roi_mask, [roi_points_array], 255)
    else:
        roi_mask = np.ones_like(frame_gray, dtype=np.uint8) * 255
    
    # 检测静态区域
    static_mask = detect_static_regions(cap, roi_mask)
    
    # 创建CLAHE对象
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    
    # 恢复原始位置
    cap.set(cv2.CAP_PROP_POS_FRAMES, original_pos)
    
    return {
        'feature_params': feature_params,
        'lk_params': lk_params,
        'feature_detection_methods': feature_detection_methods,
        'enable_fallback': enable_fallback,
        'fps': fps,
        'roi_points': roi_points,
        'roi_mask': roi_mask,
        'static_mask': static_mask,
        'clahe': clahe,
        'config_params': config_params
    }


def try_feature_method_on_frame(frame, setup_data, method, orb_nfeatures=2000):
    """
    在单帧上尝试特征检测方法
    
    Returns:
        dict: {'success': bool, 'points': array, 'filtered_count': int, 'quality_score': float}
    """
    # 转换为灰度图并增强
    frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    frame_gray_enhanced = setup_data['clahe'].apply(frame_gray)
    
    # 使用指定方法检测特征点
    candidate_points, _ = detect_features_with_method(
        frame_gray_enhanced, setup_data['roi_mask'], method, 
        setup_data['feature_params'], orb_nfeatures
    )
    
    if candidate_points is None or len(candidate_points) == 0:
        return {'success': False, 'points': None, 'filtered_count': 0, 'quality_score': 0.0}
    
    # 应用初始过滤
    candidate_p0_data = []
    for i, pt_coords in enumerate(candidate_points):
        coords = pt_coords.reshape(1, 2).astype(np.float32)
        candidate_p0_data.append({
            'id': i,
            'coords': coords,
            'start_coords': coords.copy(),
            'prev_coords': coords.copy()
        })
    
    # 过滤处理
    config_params = setup_data.get('config_params', {})
    filter_enable_config = config_params.get('filter_enable_layers', [True, True, True, True, True])
    enable_initial_filter = filter_enable_config[0] if isinstance(filter_enable_config, list) and len(filter_enable_config) >= 1 else True
    
    if enable_initial_filter and setup_data['static_mask'] is not None:
        filtered_p0_data = filter_grid_and_text_features(candidate_p0_data, static_mask=setup_data['static_mask'])
    else:
        filtered_p0_data = candidate_p0_data
    
    if not filtered_p0_data:
        return {'success': False, 'points': candidate_points, 'filtered_count': 0, 'quality_score': 0.0}
    
    # 评估帧质量
    filtered_points = np.array([item['coords'] for item in filtered_p0_data]).reshape(-1, 1, 2)
    quality_score = evaluate_frame_quality(frame_gray_enhanced, setup_data['roi_mask'], filtered_points)
    
    return {
        'success': True,
        'points': candidate_points,
        'filtered_count': len(filtered_p0_data),
        'quality_score': quality_score
    }


def create_failed_detection_video(cap, setup_data, output_path_halfway):
    """
    当两种特征识别方法都失败时，创建无过滤的识别帧视频
    
    Args:
        cap: 视频捕获对象
        setup_data: 预处理数据
        output_path_halfway: 输出路径基础
    """
    logger.info("开始生成特征识别失败时的无过滤识别帧视频...")
    
    max_search_frames = setup_data['config_params'].get('max_search_frames', 10) if setup_data['config_params'] else 10
    orb_nfeatures = setup_data['config_params'].get('orb_nfeatures', 2000) if setup_data['config_params'] else 2000
    fps = setup_data['fps']
    
    # 创建输出视频路径
    failed_video_path = os.path.join(
        os.path.dirname(output_path_halfway),
        f"{os.path.basename(output_path_halfway).split('_halfway')[0]}_failed_detection.mp4"
    )
    
    # 获取视频尺寸
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
    ret, first_frame = cap.read()
    if not ret:
        logger.error("无法读取视频第一帧用于创建失败检测视频")
        return
    
    height, width = first_frame.shape[:2]
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(failed_video_path, fourcc, fps, (width, height))
    
    try:
        # 对每种特征检测方法和每一帧进行无过滤的特征检测
        for method in setup_data['feature_detection_methods']:
            logger.info(f"处理 {method} 方法的无过滤识别帧...")
            
            for frame_index in range(max_search_frames):
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
                ret, current_frame = cap.read()
                if not ret:
                    logger.warning(f"无法读取第 {frame_index} 帧")
                    continue
                
                # 转换为灰度图并增强
                frame_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
                frame_gray_enhanced = setup_data['clahe'].apply(frame_gray)
                
                # 使用指定方法检测特征点（无过滤）
                candidate_points, _ = detect_features_with_method(
                    frame_gray_enhanced, setup_data['roi_mask'], method, 
                    setup_data['feature_params'], orb_nfeatures
                )
                
                # 创建标记帧
                marked_frame = current_frame.copy()
                
                # 添加方法和帧信息
                cv2.putText(marked_frame, f"Method: {method}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(marked_frame, f"Frame: {frame_index}", (10, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                # 绘制ROI区域
                if setup_data['roi_points'] is not None:
                    roi_array = np.array(setup_data['roi_points'], dtype=np.int32)
                    cv2.polylines(marked_frame, [roi_array], isClosed=True, color=(255, 0, 0), thickness=2)
                
                # 标记检测到的特征点
                if candidate_points is not None and len(candidate_points) > 0:
                    for i, pt in enumerate(candidate_points):
                        x, y = pt.ravel().astype(int)
                        # 绘制特征点
                        cv2.circle(marked_frame, (x, y), 5, (0, 0, 255), -1)
                        # 添加点的编号（仅显示前50个，避免过于拥挤）
                        if i < 50:
                            cv2.putText(marked_frame, str(i), (x + 8, y - 8), 
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)
                    
                    cv2.putText(marked_frame, f"Points: {len(candidate_points)}", (10, 110), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                else:
                    cv2.putText(marked_frame, "Points: 0", (10, 110), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                
                # 写入视频帧
                out.write(marked_frame)
        
        logger.info(f"特征识别失败视频已保存: {failed_video_path}")
        
    except Exception as e:
        logger.error(f"创建特征识别失败视频时出错: {e}")
    finally:
        out.release()


def find_suitable_start_frame(cap, setup_data, output_path_halfway=None):
    """
    寻找合适的起始帧 - 按照理想逻辑：逐个特征方案，逐帧检查
    
    Args:
        cap: 视频捕获对象
        setup_data: 预处理数据
        output_path_halfway: 输出路径基础，用于生成失败检测视频
    
    Returns:
        dict: 起始帧相关数据或None
    """
    max_search_frames = setup_data['config_params'].get('max_search_frames', 10) if setup_data['config_params'] else 10
    frame_quality_threshold = setup_data['config_params'].get('frame_quality_threshold', 0.3) if setup_data['config_params'] else 0.3
    orb_nfeatures = setup_data['config_params'].get('orb_nfeatures', 2000) if setup_data['config_params'] else 2000
    
    logger.info(f"开始搜索合适的起始帧，最多搜索 {max_search_frames} 帧...")
    
    # 逐个特征识别方案
    for method in setup_data['feature_detection_methods']:
        logger.info(f"尝试使用 {method} 方法寻找起始帧...")
        
        # 逐帧检查
        for frame_index in range(max_search_frames):
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
            ret, current_frame = cap.read()
            if not ret:
                logger.warning(f"无法读取第 {frame_index} 帧")
                continue
            
            # 在当前帧尝试当前方法
            result = try_feature_method_on_frame(current_frame, setup_data, method, orb_nfeatures)
            
            if result['success']:
                logger.info(f"第 {frame_index} 帧: 使用 {method} 方法检测到 {len(result['points'])} 个特征点 → 过滤后 {result['filtered_count']} 个, 质量评分: {result['quality_score']:.3f}")
                
                # 判断是否合格
                if result['quality_score'] > frame_quality_threshold:
                    logger.info(f"✓ 选择第 {frame_index} 帧作为起始帧 (使用 {method} 方法, 过滤后 {result['filtered_count']} 个特征点, 质量评分: {result['quality_score']:.3f})")
                    
                    return {
                        'frame_index': frame_index,
                        'frame': current_frame,
                        'points': result['points'],
                        'used_method': method,
                        'quality_score': result['quality_score'],
                        'filtered_count': result['filtered_count']
                    }
            else:
                logger.debug(f"第 {frame_index} 帧: {method} 方法未检测到有效特征点")
        
        # 如果不启用fallback，只尝试第一种方法
        if not setup_data['enable_fallback']:
            break
    
    logger.error(f"在前 {max_search_frames} 帧中未找到合适的起始帧")
    
    # 检查是否启用失败检测视频保存
    save_failed_video = setup_data['config_params'].get('save_failed_detection_video', False) if setup_data.get('config_params') else False
    if save_failed_video and output_path_halfway is not None:
        logger.info("启用失败检测视频保存，开始生成无过滤识别帧视频...")
        create_failed_detection_video(cap, setup_data, output_path_halfway)
    
    return None


def collect_optical_flow_data(cap, start_frame_data, setup_data, out=None):
    """
    收集光流跟踪数据
    """
    if start_frame_data is None:
        return pd.DataFrame()
    
    logger.info("开始收集光流跟踪数据...")
    
    # 获取初始过滤开关配置
    config_params = setup_data.get('config_params', {})
    filter_enable_config = config_params.get('filter_enable_layers', [True, True, True, True, True])
    enable_initial_filter = filter_enable_config[0] if isinstance(filter_enable_config, list) and len(filter_enable_config) >= 1 else True
    
    # 初始化特征点数据
    p0_data = []
    for i, pt_coords in enumerate(start_frame_data['points']):
        coords = pt_coords.reshape(1, 2).astype(np.float32)
        p0_data.append({
            'id': i,
            'coords': coords,
            'start_coords': coords.copy(),
            'prev_coords': coords.copy()
        })
    
    # 应用初始过滤
    if enable_initial_filter and setup_data['static_mask'] is not None:
        p0_data = filter_grid_and_text_features(p0_data, static_mask=setup_data['static_mask'])
    
    if not p0_data:
        logger.error("初始过滤后无特征点")
        return pd.DataFrame()
    
    # 数据收集列表
    tracking_data = []
    frame_count = start_frame_data['frame_index']
    
    # 设置起始帧信息
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame_data['frame_index'])
    ret, prev_frame = cap.read()
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    prev_gray_enhanced = setup_data['clahe'].apply(prev_gray)
    
    # 移动到下一帧开始处理
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame_data['frame_index'] + 1)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        frame_gray_enhanced = setup_data['clahe'].apply(frame_gray)
        current_time_seconds = frame_count / setup_data['fps'] if setup_data['fps'] > 0 else 0
        
        if p0_data:
            # 光流跟踪
            current_coords_for_lk = np.array([item['coords'] for item in p0_data]).reshape(-1, 1, 2)
            p1, st, err = cv2.calcOpticalFlowPyrLK(prev_gray_enhanced, frame_gray_enhanced, current_coords_for_lk, None, **setup_data['lk_params'])
            
            # 处理跟踪结果
            next_p0_data = []
            
            if p1 is not None and st is not None:
                for k in range(len(st)):
                    if st[k][0] == 1:  # 成功跟踪
                        original_id = p0_data[k]['id']
                        new_coord_point_arr = p1[k].reshape(1, 2)
                        old_coord_point_arr = current_coords_for_lk[k].reshape(1, 2)
                        
                        # 检查是否在ROI内
                        x_coord, y_coord = new_coord_point_arr.ravel()
                        in_roi = True
                        if setup_data['roi_points'] is not None:
                            in_roi = cv2.pointPolygonTest(
                                np.array(setup_data['roi_points'], dtype=np.int32), 
                                (x_coord, y_coord), False) >= 0
                        
                        if in_roi:
                            # 更新特征点数据
                            original_start_coords = p0_data[k].get('start_coords', new_coord_point_arr.copy())
                            next_p0_data.append({
                                'id': original_id,
                                'coords': new_coord_point_arr,
                                'start_coords': original_start_coords,
                                'prev_coords': new_coord_point_arr.ravel()
                            })
                            
                            # 计算位移和角度
                            new_pt = new_coord_point_arr.ravel()
                            old_pt = old_coord_point_arr.ravel()
                            
                            # 计算相对于上一帧的位移
                            if frame_count == start_frame_data['frame_index']:
                                displacement_from_prev_frame = 0.0
                            else:
                                prev_coords = p0_data[k].get('prev_coords', old_pt)
                                if hasattr(prev_coords, 'ravel'):
                                    prev_coords = prev_coords.ravel()
                                displacement_from_prev_frame = np.sqrt(
                                    (new_pt[0] - prev_coords[0]) ** 2 + 
                                    (new_pt[1] - prev_coords[1]) ** 2)
                            
                            # 计算移动角度
                            movement_angle = calculate_angle(new_pt, old_pt)
                            
                            # 记录数据
                            start_coords = original_start_coords.ravel()
                            tracking_data.append({
                                'point_id': original_id,
                                'time': current_time_seconds,
                                'x': new_pt[0],
                                'y': new_pt[1],
                                'displacement': round(displacement_from_prev_frame, 3),
                                'angle': movement_angle,
                                'start_x': start_coords[0],
                                'start_y': start_coords[1]
                            })
            
            p0_data = next_p0_data
        
        # 如果特征点太少，重新检测
        if len(p0_data) < 5:
            logger.info(f"帧 {frame_count}: 特征点过少，重新检测...")
            orb_nfeatures = setup_data['config_params'].get('orb_nfeatures', 2000) if setup_data['config_params'] else 2000
            newly_detected_points = None
            if start_frame_data['used_method'] and start_frame_data['used_method'] != "none":
                newly_detected_points, _ = detect_features_with_method(
                    frame_gray_enhanced, setup_data['roi_mask'], start_frame_data['used_method'], 
                    setup_data['feature_params'], orb_nfeatures
                )
            
            p0_data = []
            if newly_detected_points is not None:
                for i, pt_coords in enumerate(newly_detected_points):
                    coords = pt_coords.reshape(1, 2).astype(np.float32)
                    p0_data.append({
                        'id': i + 10000 + frame_count,  # 避免ID冲突
                        'coords': coords,
                        'start_coords': coords.copy(),
                        'prev_coords': coords.copy()
                    })
                # 应用初始过滤
                if enable_initial_filter and setup_data['static_mask'] is not None:
                    p0_data = filter_grid_and_text_features(p0_data, static_mask=setup_data['static_mask'])
        
        # 生成视频输出（如果提供了VideoWriter对象）
        if out is not None:
            output_frame = frame.copy()
            
            # 添加网格（如果配置要求）
            if setup_data['config_params'] and setup_data['config_params'].get('add_grid_to_output_video', True):
                output_frame = draw_grid(output_frame, draw_direction=False)
            
            # 绘制ROI
            if setup_data['roi_points'] is not None:
                roi_array = np.array(setup_data['roi_points'], dtype=np.int32)
                cv2.polylines(output_frame, [roi_array], isClosed=True, color=(255, 0, 0), thickness=2)
            
            # 绘制当前跟踪的特征点
            if p0_data:
                for item in p0_data:
                    x_f, y_f = item['coords'].ravel()
                    cv2.circle(output_frame, (int(x_f), int(y_f)), 3, (0, 255, 0), -1)
            
            # 写入视频帧
            out.write(output_frame)
        
        prev_gray = frame_gray.copy()
        prev_gray_enhanced = frame_gray_enhanced.copy()
        frame_count += 1
        
        if frame_count % 100 == 0:
            logger.info(f"已处理 {frame_count} 帧，当前特征点数: {len(p0_data)}")
    
    # 转换为DataFrame
    df = pd.DataFrame(tracking_data)
    logger.info(f"数据收集完成 - 总数据点: {len(df)}, 唯一特征点: {df['point_id'].nunique() if not df.empty else 0}")
    
    return df


def collect_tracking_data(cap, roi_points=None, feature_params=None, lk_params=None, 
                         check_feature_points=False, static_mask=None, max_search_frames=10, 
                         frame_quality_threshold=0.3, orb_fallback_enabled=True, orb_nfeatures=2000,
                         orb_search_frames=5, orb_quality_threshold_offset=-0.1, out=None, config_params=None, 
                         output_path_halfway=None) -> tuple[pd.DataFrame, str]:
    """
    数据收集阶段：光流跟踪和数据收集 - 重构版本
    采用清晰的模块化架构：基础预处理 → 起始帧选择 → 数据收集
    
    Args:
        cap: 视频捕获对象
        roi_points: ROI区域点
        feature_params: 特征点检测参数
        lk_params: Lucas-Kanade光流参数
        check_feature_points: 是否检查特征点（保留兼容性，暂不使用）
        static_mask: 静态区域掩膜（保留兼容性，函数内会重新生成）
        max_search_frames: 最大搜索帧数
        frame_quality_threshold: 帧质量阈值
        orb_fallback_enabled: 是否启用ORB备选（保留兼容性，已通过config_params处理）
        orb_nfeatures: ORB特征点数量
        orb_search_frames: ORB搜索帧数（保留兼容性）
        orb_quality_threshold_offset: ORB质量阈值偏移（保留兼容性）
        out: VideoWriter对象，用于生成视频输出（可选）
        config_params: 配置参数，用于视频生成（可选）
        output_path_halfway: 输出路径基础，用于生成失败检测视频（可选）
        
    Returns:
        tuple[pd.DataFrame, str]: (包含所有特征点跟踪数据的DataFrame, 实际使用的特征检测方法)
    """
    logger.info("开始数据收集阶段（重构版本）...")
    
    # 1. 基础预处理
    setup_data = prepare_video_processing(cap, roi_points, feature_params, lk_params, config_params)
    if setup_data is None:
        logger.error("基础预处理失败")
        return pd.DataFrame(), "none"
    
    # 2. 寻找合适的起始帧
    start_frame_data = find_suitable_start_frame(cap, setup_data, output_path_halfway)
    if start_frame_data is None:
        logger.error("未找到合适的起始帧")
        return pd.DataFrame(), "none"
    
    # 3. 收集光流数据
    tracking_df = collect_optical_flow_data(cap, start_frame_data, setup_data, out)
    
    used_method = start_frame_data['used_method'] if start_frame_data else "none"
    logger.info(f"数据收集完成，使用特征检测方法: {used_method}")
    
    return tracking_df, used_method


def calculate_flow_statistics(df: pd.DataFrame, pixel_to_meter: float, fps: float, speed_adjustment_factor: float = 1.0) -> Dict[str, float]:
    """
    计算最终流速统计
    
    Args:
        df: 过滤后的特征点数据DataFrame
        pixel_to_meter: 像素到米的转换系数
        fps: 视频帧率
        
    Returns:
        Dict: 包含各种流速统计指标的字典
    """
    logger.info("开始计算流速统计...")
    
    if df.empty:
        logger.warning("数据为空，无法计算流速统计")
        return {
            'avg_speed': 0.0,
            'min_speed': 0.0,
            'max_speed': 0.0,
            'valid_frames': 0,
            'total_data_points': 0
        }
    
    # 按时间分组，计算每帧的平均位移
    frame_avg_displacement = df.groupby('time')['displacement'].mean()
    
    if frame_avg_displacement.empty:
        logger.warning("无有效帧数据")
        return {
            'avg_speed': 0.0,
            'min_speed': 0.0,
            'max_speed': 0.0,
            'valid_frames': 0,
            'total_data_points': len(df)
        }
    
    # 计算每帧的速度：平均位移 * 像素转换系数 * 帧率 * 调整系数
    frame_speeds = frame_avg_displacement * pixel_to_meter * fps * speed_adjustment_factor
    
    # 计算统计指标
    stats = {
        'avg_speed': float(frame_speeds.mean()),
        'min_speed': float(frame_speeds.min()),
        'max_speed': float(frame_speeds.max()),
        'valid_frames': len(frame_speeds),
        'total_data_points': len(df),
        'std_speed': float(frame_speeds.std()),
        'median_speed': float(frame_speeds.median())
    }
    
    logger.info(f"流速统计完成 - 平均: {stats['avg_speed']:.4f} m/s, "
               f"范围: [{stats['min_speed']:.4f}, {stats['max_speed']:.4f}] m/s, "
               f"有效帧数: {stats['valid_frames']}")
    
    return stats


def apply_combined_filters(df: pd.DataFrame, filter_pipeline: OTVFilterPipeline = None) -> pd.DataFrame:
    """
    应用所有过滤标记的组合，返回最终有效的数据
    
    Args:
        df: 包含所有过滤标记的完整数据DataFrame
        filter_pipeline: 过滤管道对象，用于获取过滤开关信息
        
    Returns:
        pd.DataFrame: 通过所有过滤条件的数据
    """
    logger.info("应用组合过滤条件...")
    
    if df.empty:
        return df
    
    # 初始化组合条件为True
    combined_mask = pd.Series([True] * len(df), index=df.index)
    
    # 逐一应用各种过滤条件（根据开关决定是否应用）
    filter_conditions = []
    
    # 检查方向过滤
    if filter_pipeline and filter_pipeline.enable_direction_filter and 'direction_valid' in df.columns:
        combined_mask &= df['direction_valid']
        filter_conditions.append('direction_valid')
    elif 'direction_valid' in df.columns and (filter_pipeline is None):
        # 向后兼容：没有 filter_pipeline 时默认应用所有过滤
        combined_mask &= df['direction_valid']
        filter_conditions.append('direction_valid')
    
    # 检查轨迹静态点过滤
    if filter_pipeline and filter_pipeline.enable_trajectory_filter and 'trajectory_dynamic' in df.columns:
        combined_mask &= df['trajectory_dynamic']
        filter_conditions.append('trajectory_dynamic')
    elif 'trajectory_dynamic' in df.columns and (filter_pipeline is None):
        combined_mask &= df['trajectory_dynamic']
        filter_conditions.append('trajectory_dynamic')
    
    # 检查分位法过滤
    if filter_pipeline and filter_pipeline.enable_quartile_filter and 'quartile_valid' in df.columns:
        combined_mask &= df['quartile_valid']
        filter_conditions.append('quartile_valid')
    elif 'quartile_valid' in df.columns and (filter_pipeline is None):
        combined_mask &= df['quartile_valid']
        filter_conditions.append('quartile_valid')
    
    # 检查时间窗口动态过滤
    if filter_pipeline and filter_pipeline.enable_window_filter and 'window_active' in df.columns:
        combined_mask &= df['window_active']
        filter_conditions.append('window_active')
    elif 'window_active' in df.columns and (filter_pipeline is None):
        combined_mask &= df['window_active']
        filter_conditions.append('window_active')
    
    # 应用组合过滤
    filtered_df = df[combined_mask].copy()
    
    logger.info(f"组合过滤完成 - 应用条件: {filter_conditions}")
    logger.info(f"过滤结果: {len(filtered_df)}/{len(df)} 数据点通过所有过滤条件")
    
    return filtered_df


def create_flow_speed_trend_chart(filter_stats, final_flow_stats, output_path_halfway):
    """创建流速变化趋势图表，显示每层过滤后的流速变化"""
    logger.info("生成流速变化趋势图...")
    
    try:
        # 收集每层过滤的流速数据
        filter_names = ['original data', 'direction filter', 'trajectory static point filter', 'quartile filter', 'window dynamic filter', 'final result']
        stage_names = ['original', 'direction', 'trajectory', 'quartile', 'window']
        
        avg_speeds = []
        min_speeds = []
        max_speeds = []
        data_points = []
        
        # 提取每层过滤的流速数据
        for stage in stage_names:
            if stage in filter_stats and 'flow_stats' in filter_stats[stage]:
                flow_stats = filter_stats[stage]['flow_stats']
                if flow_stats:
                    avg_speeds.append(flow_stats.get('avg_speed', 0))
                    min_speeds.append(flow_stats.get('min_speed', 0))
                    max_speeds.append(flow_stats.get('max_speed', 0))
                    data_points.append(flow_stats.get('total_data_points', 0))
                else:
                    avg_speeds.append(0)
                    min_speeds.append(0)
                    max_speeds.append(0)
                    data_points.append(0)
            else:
                avg_speeds.append(0)
                min_speeds.append(0)
                max_speeds.append(0)
                data_points.append(0)
        
        # 添加最终结果
        avg_speeds.append(final_flow_stats.get('avg_speed', 0))
        min_speeds.append(final_flow_stats.get('min_speed', 0))
        max_speeds.append(final_flow_stats.get('max_speed', 0))
        data_points.append(final_flow_stats.get('total_data_points', 0))
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        x_positions = range(len(filter_names))
        
        # 子图1：流速变化
        ax1.plot(x_positions, avg_speeds, 'o-', linewidth=2, markersize=8, label='average speed', color='blue')
        ax1.fill_between(x_positions, min_speeds, max_speeds, alpha=0.3, color='blue', label='speed range')
        ax1.plot(x_positions, min_speeds, '--', alpha=0.7, color='green', label='min speed')
        ax1.plot(x_positions, max_speeds, '--', alpha=0.7, color='red', label='max speed')
        
        ax1.set_xlabel('filter stage', fontsize=12)
        ax1.set_ylabel('speed (m/s)', fontsize=12)
        ax1.set_title('speed trend of each filter stage', fontsize=14, fontweight='bold')
        ax1.set_xticks(x_positions)
        ax1.set_xticklabels(filter_names, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 在每个点上标注数值
        for i, (avg, min_val, max_val) in enumerate(zip(avg_speeds, min_speeds, max_speeds)):
            ax1.annotate(f'{avg:.4f}', (i, avg), textcoords="offset points", 
                        xytext=(0,10), ha='center', fontsize=9)
        
        # 子图2：数据点数量变化
        ax2.bar(x_positions, data_points, alpha=0.7, color='orange', label='data points')
        ax2.set_xlabel('filter stage', fontsize=12)
        ax2.set_ylabel('data points', fontsize=12)
        ax2.set_title('data points trend of each filter stage', fontsize=14, fontweight='bold')
        ax2.set_xticks(x_positions)
        ax2.set_xticklabels(filter_names, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3, axis='y')
        
        # 在每个柱子上标注数值
        for i, count in enumerate(data_points):
            ax2.annotate(f'{count}', (i, count), textcoords="offset points", 
                        xytext=(0,3), ha='center', fontsize=9)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(
            os.path.dirname(output_path_halfway),
            f"{os.path.basename(output_path_halfway).split('_halfway')[0]}_flow_speed_trend.png"
        )
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"流速变化趋势图已保存: {chart_path}")
        
    except Exception as e:
        logger.error(f"生成流速变化趋势图失败: {e}")

# 默认配置 (可以被外部配置覆盖)
DEFAULT_OTV_PARAMS = {
    'mode': 'real',
    'input_video_path': 'data/output/ch01_20250414145900_0140_0220.mp4',
    'check_features_points': False,
    'direction_concentration_range': 90,
    'min_feature_points_rediscover': 5,
    'max_search_frames': 10,  # 最大搜索帧数，用于寻找合适的起始帧
    'frame_quality_threshold': 0.3,  # 帧质量阈值，用于选择起始帧
    'orb_fallback_enabled': True,  # 是否启用ORB作为备选特征检测器
    'orb_nfeatures': 2000,  # ORB检测器的最大特征点数量
    'orb_search_frames': 5,  # ORB检测器搜索的最大帧数
    'orb_quality_threshold_offset': -0.1  # ORB质量阈值相对于主阈值的偏移量
}

def calculate_angle(new_pt, old_pt):
    """计算特征点移动的角度，返回0-360度范围内的角度值"""
    dx = new_pt[0] - old_pt[0]
    dy = new_pt[1] - old_pt[1]
    angle = round(math.degrees(math.atan2(dy, dx)), 2)
    # 转换到0-360度范围
    if angle < 0:
        angle += 360
    return angle

def sort_polygon_points_clockwise(points):
    """将多边形顶点按顺时针方向排序"""
    # 计算多边形的中心点
    center = np.mean(points, axis=0)
    
    # 计算每个点相对于中心点的角度
    def get_angle(point):
        return np.arctan2(point[1] - center[1], point[0] - center[0])
    
    # 按角度排序
    sorted_points = sorted(points, key=get_angle, reverse=True)
    return np.array(sorted_points, dtype=np.int32)

def detect_static_regions(cap, roi_mask, num_frames=5, threshold=10):
    """检测ROI内的静态区域，如文字、标识等"""
    static_mask = np.ones_like(roi_mask) * 255  # 初始化为全白（非静态）
    
    if num_frames < 2:
        return static_mask
    
    frames = []
    original_pos = cap.get(cv2.CAP_PROP_POS_FRAMES)
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
    
    # 读取前几帧
    for i in range(num_frames):
        ret, frame = cap.read()
        if not ret:
            break
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        frames.append(gray)
    
    # 恢复原始位置
    cap.set(cv2.CAP_PROP_POS_FRAMES, original_pos)
    
    if len(frames) < 2:
        return static_mask
    
    # 计算帧间差异的累积
    diff_sum = np.zeros_like(frames[0], dtype=np.float32)
    for i in range(1, len(frames)):
        diff = cv2.absdiff(frames[i-1], frames[i]).astype(np.float32)
        diff_sum += diff
    
    # 平均差异
    avg_diff = diff_sum / (len(frames) - 1)
    
    # 标记变化小的区域为静态（黑色）
    static_mask = np.where((avg_diff < threshold) & (roi_mask > 0), 0, 255).astype(np.uint8)
    
    return static_mask

def filter_grid_and_text_features(points, grid_step=50, static_mask=None):
    """过滤掉可能是网格线交点或文字区域的特征点"""
    if not points:
        return points
    
    filtered_points = []
    for point_data in points:
        x, y = point_data['coords'].ravel()
        
        # 过滤网格交点附近的点（容差5像素）
        is_near_grid_x = abs(x % grid_step) < 5 or abs(x % grid_step) > (grid_step - 5)
        is_near_grid_y = abs(y % grid_step) < 5 or abs(y % grid_step) > (grid_step - 5)
        
        # 过滤文字区域（左上角区域，用于方向指示）
        is_in_text_area = (x < 200 and y < 150)
        
        # 检查是否在静态区域
        is_in_static_area = False
        if static_mask is not None:
            if 0 <= int(y) < static_mask.shape[0] and 0 <= int(x) < static_mask.shape[1]:
                is_in_static_area = static_mask[int(y), int(x)] == 0
        
        # 如果不在网格交点附近、不在文字区域、且不在静态区域，则保留
        if not (is_near_grid_x and is_near_grid_y) and not is_in_text_area and not is_in_static_area:
            filtered_points.append(point_data)
    
    return filtered_points


def evaluate_frame_quality(gray_frame, roi_mask, feature_points):
    """
    评估帧质量，用于选择合适的起始帧
    
    Args:
        gray_frame: 灰度图像
        roi_mask: ROI掩膜
        feature_points: 检测到的特征点
        
    Returns:
        float: 质量评分 (0-1之间，越高越好)
    """
    if feature_points is None or len(feature_points) == 0:
        return 0.0
    
    # 1. 特征点数量评分 (0-0.4)
    num_points = len(feature_points)
    point_score = min(num_points / 100.0, 0.4)  # 100个点为满分0.4
    
    # 2. 图像对比度评分 (0-0.3)
    roi_area = gray_frame[roi_mask > 0]
    if len(roi_area) > 0:
        contrast = roi_area.std() / 255.0  # 标准差作为对比度指标
        contrast_score = min(contrast * 2, 0.3)  # 标准差0.15为满分0.3
    else:
        contrast_score = 0.0
    
    # 3. 特征点分布均匀性评分 (0-0.3)
    if len(feature_points) >= 4:
        # 计算特征点在ROI内的分布均匀性
        points_2d = feature_points.reshape(-1, 2)
        
        # 获取ROI边界
        roi_coords = np.where(roi_mask > 0)
        if len(roi_coords[0]) > 0:
            roi_min_y, roi_max_y = roi_coords[0].min(), roi_coords[0].max()
            roi_min_x, roi_max_x = roi_coords[1].min(), roi_coords[1].max()
            
            # 将ROI分成4个象限，检查每个象限是否有特征点
            mid_x = (roi_min_x + roi_max_x) / 2
            mid_y = (roi_min_y + roi_max_y) / 2
            
            quadrants = [0, 0, 0, 0]  # 左上、右上、左下、右下
            for point in points_2d:
                x, y = point
                if x < mid_x and y < mid_y:
                    quadrants[0] = 1
                elif x >= mid_x and y < mid_y:
                    quadrants[1] = 1
                elif x < mid_x and y >= mid_y:
                    quadrants[2] = 1
                else:
                    quadrants[3] = 1
            
            distribution_score = sum(quadrants) / 4.0 * 0.3  # 4个象限都有点为满分0.3
        else:
            distribution_score = 0.0
    else:
        distribution_score = 0.0
    
    total_score = point_score + contrast_score + distribution_score
    return min(total_score, 1.0)


def detect_features_with_method(frame_gray_enhanced, roi_mask, method, feature_params, orb_nfeatures):
    """
    使用指定方法检测特征点
    
    Args:
        frame_gray_enhanced: 增强后的灰度图像
        roi_mask: ROI掩膜
        method: 特征检测方法 ("goodFeaturesToTrack" 或 "orb")
        feature_params: goodFeaturesToTrack的参数
        orb_nfeatures: ORB特征点数量
        
    Returns:
        tuple: (检测到的特征点数组, 检测方法名称) 或 (None, method) 如果失败
    """
    try:
        if method == "goodFeaturesToTrack":
            candidate_points = cv2.goodFeaturesToTrack(frame_gray_enhanced, mask=roi_mask, **feature_params)
            return candidate_points, method
        elif method == "orb":
            orb = cv2.ORB_create(nfeatures=orb_nfeatures)
            keypoints = orb.detect(frame_gray_enhanced, mask=roi_mask)
            if keypoints:
                # 将KeyPoint对象转换为goodFeaturesToTrack格式
                orb_points = np.array([kp.pt for kp in keypoints], dtype=np.float32).reshape(-1, 1, 2)
                return orb_points, method
            else:
                return None, method
        else:
            logger.error(f"不支持的特征检测方法: {method}")
            return None, method
    except Exception as e:
        logger.error(f"特征检测方法 {method} 执行失败: {e}")
        return None, method


def mark_initial_features_on_frame(frame, initial_points, output_path_base="debug_initial_features.jpg"):
    """
    在帧上标记最原始的初始特征点，用于调试
    
    Args:
        frame: 要标记的帧图像
        initial_points: 原始检测到的特征点 (cv2.goodFeaturesToTrack的输出)
        output_path_base: 输出图片的基础路径
    
    Returns:
        marked_frame: 标记了特征点的帧图像
    """
    if initial_points is None or len(initial_points) == 0:
        logger.warning("没有初始特征点可以标记")
        return frame
    
    marked_frame = frame.copy()
    
    # 在图像上标记每个初始特征点
    for i, pt in enumerate(initial_points):
        x, y = pt.ravel().astype(int)
        # 绘制红色圆点
        cv2.circle(marked_frame, (x, y), 8, (0, 0, 255), 2)
        # 添加点的编号
        cv2.putText(marked_frame, str(i), (x + 10, y - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 添加标题信息
    cv2.putText(marked_frame, f"Initial Features: {len(initial_points)} points", 
               (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    cv2.putText(marked_frame, "Red circles: Original detected features", 
               (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 1)
    
    # 保存标记后的图像
    try:
        cv2.imwrite(output_path_base, marked_frame)
        logger.info(f"原始特征点调试图像已保存: {output_path_base}")
    except Exception as e:
        logger.error(f"保存调试图像失败: {e}")
    
    return marked_frame


def draw_grid(frame, grid_step=50, draw_direction=False, dominant_direction=None, concentration_range=None):
    """在帧上绘制网格和坐标轴，以及可选的主导方向指示"""
    h, w = frame.shape[:2]
    # 绘制垂直线和标签
    for x in range(0, w, grid_step):
        cv2.line(frame, (x, 0), (x, h), (200, 200, 200), 1)
        cv2.putText(frame, str(x), (x + 5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    # 绘制水平线和标签
    for y in range(0, h, grid_step):
        cv2.line(frame, (0, y), (w, y), (200, 200, 200), 1)
        cv2.putText(frame, str(y), (5, y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    
    # 绘制主导方向指示（如果提供了主导方向）
    if draw_direction and dominant_direction is not None:
        # 在左上角绘制方向指示
        center_x, center_y = 100, 100  # 箭头中心点
        arrow_length = 50  # 箭头长度

        # 绘制主导方向箭头（红色）
        dominant_rad = math.radians(dominant_direction)
        end_x = int(center_x + arrow_length * math.cos(dominant_rad))
        end_y = int(center_y + arrow_length * math.sin(dominant_rad))
        cv2.arrowedLine(frame, (center_x, center_y), (end_x, end_y), (0, 0, 255), 2, tipLength=0.3)
        
        # 绘制集中度范围（淡蓝色）
        if concentration_range is not None:
            half_range = concentration_range // 2
            min_angle = dominant_direction - half_range
            max_angle = dominant_direction + half_range
            
            # 绘制最小角度线
            min_rad = math.radians(min_angle)
            min_end_x = int(center_x + arrow_length * math.cos(min_rad))
            min_end_y = int(center_y + arrow_length * math.sin(min_rad))
            cv2.line(frame, (center_x, center_y), (min_end_x, min_end_y), (255, 200, 0), 1)
            
            # 绘制最大角度线
            max_rad = math.radians(max_angle)
            max_end_x = int(center_x + arrow_length * math.cos(max_rad))
            max_end_y = int(center_y + arrow_length * math.sin(max_rad))
            cv2.line(frame, (center_x, center_y), (max_end_x, max_end_y), (255, 200, 0), 1)
            
            # 绘制文字说明
            cv2.putText(frame, f"主导方向: {dominant_direction:.1f}°", (center_x - 90, center_y - 20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            cv2.putText(frame, f"集中度范围: ±{half_range}°", (center_x - 90, center_y + 70), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 200, 0), 1)
    
    return frame

def process_video_real_mode(cap, output_path_halfway, config_params, out=None):
    """
    重构版本：处理视频的实际模式，采用两阶段过滤架构
    
    Args:
        cap: 视频捕获对象
        output_path_halfway: 输出路径
        config_params: 配置参数字典
        out: VideoWriter对象，用于输出视频（可选）
        
    Returns:
        tuple: (frame_count, min_speed, avg_speed, max_speed)
    """
    logger.info("开始新版本的视频处理...")
    
    # 提取配置参数
    roi_points = config_params.get('roi_points')
    feature_params = config_params.get('feature_params')
    lk_params = config_params.get('lk_params')
    pixel_to_meter = config_params.get('pixel_to_meter', 0.0086)
    check_feature_points = config_params.get('check_feature_points', False)
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 检测静态区域
    logger.info("检测静态区域...")
    roi_mask = np.zeros_like(cv2.cvtColor(cap.read()[1], cv2.COLOR_BGR2GRAY))
    if roi_points is not None and len(roi_points) > 0:
        current_roi_points = np.array(roi_points, dtype=np.int32)
        current_roi_points = sort_polygon_points_clockwise(current_roi_points)
        cv2.fillPoly(roi_mask, [current_roi_points], 255)
    else:
        roi_mask = np.ones_like(roi_mask, dtype=np.uint8) * 255
    
    static_mask = detect_static_regions(cap, roi_mask)
    
    # 阶段1：数据收集
    logger.info("=" * 50)
    logger.info("阶段1: 数据收集")
    logger.info("=" * 50)
    
    tracking_df, used_detection_method = collect_tracking_data(
        cap=cap,
        roi_points=roi_points,
        feature_params=feature_params,
        lk_params=lk_params,
        check_feature_points=check_feature_points,
        static_mask=static_mask,
        max_search_frames=config_params.get('max_search_frames', 10),
        frame_quality_threshold=config_params.get('frame_quality_threshold', 0.3),
        orb_fallback_enabled=config_params.get('orb_fallback_enabled', True),
        orb_nfeatures=config_params.get('orb_nfeatures', 2000),
        orb_search_frames=config_params.get('orb_search_frames', 5),
        orb_quality_threshold_offset=config_params.get('orb_quality_threshold_offset', -0.1),
        out=out,
        config_params=config_params,
        output_path_halfway=output_path_halfway
    )
    
    if tracking_df.empty:
        logger.error("数据收集失败，无法继续处理")
        return 0, 0.0, 0.0, 0.0, used_detection_method
    
    # 阶段2：后处理过滤
    logger.info("=" * 50)
    logger.info("阶段2: 后处理过滤")
    logger.info("=" * 50)
    
    # 初始化过滤管道
    filter_pipeline = OTVFilterPipeline(config_params, pixel_to_meter, fps)
    
    # 存储每层过滤的统计信息
    filter_stats = {}
    current_df = tracking_df.copy()
    
    # 第一层：无过滤环节的流速计算（原始数据）
    logger.info("-" * 30)
    logger.info("执行第一层：原始数据流速计算（无过滤）")
    logger.info("-" * 30)
    original_flow_stats = filter_pipeline._calculate_flow_stats_for_filter(current_df, "原始数据（无过滤）")
    filter_stats['original'] = {
        'total_points': len(current_df),
        'unique_points': current_df['point_id'].nunique(),
        'flow_stats': original_flow_stats
    }
    
    # 第二层：方向过滤（标记模式）
    logger.info("-" * 30)
    logger.info("执行第二层：方向过滤")
    logger.info("-" * 30)
    direction_result = filter_pipeline.filter_by_direction(current_df)
    current_df = direction_result.marked_data  # 使用包含标记的完整数据
    filter_stats['direction'] = direction_result.filter_stats
    filter_stats['direction']['flow_stats'] = direction_result.flow_stats
    
    # 第三层：轨迹静态点过滤（标记模式）
    logger.info("-" * 30)
    logger.info("执行第三层：轨迹静态点过滤")
    logger.info("-" * 30)
    trajectory_result = filter_pipeline.filter_static_trajectories(current_df)
    current_df = trajectory_result.marked_data  # 使用包含标记的完整数据
    filter_stats['trajectory'] = trajectory_result.filter_stats
    filter_stats['trajectory']['flow_stats'] = trajectory_result.flow_stats
    
    # 第四层：分位法过滤（标记模式）
    logger.info("-" * 30)
    logger.info("执行第四层：自适应分位法过滤")
    logger.info("-" * 30)
    quartile_result = filter_pipeline.filter_by_quartile(current_df)
    current_df = quartile_result.marked_data  # 使用包含标记的完整数据
    filter_stats['quartile'] = quartile_result.filter_stats
    filter_stats['quartile']['flow_stats'] = quartile_result.flow_stats
    
    # 第五层：时间窗口动态过滤（标记模式）
    logger.info("-" * 30)
    logger.info("执行第五层：时间窗口动态过滤")
    logger.info("-" * 30)
    window_result = filter_pipeline.filter_by_time_window(current_df, fps)
    final_marked_df = window_result.marked_data  # 包含所有标记的完整数据
    filter_stats['window'] = window_result.filter_stats
    filter_stats['window']['flow_stats'] = window_result.flow_stats
    
    # 阶段3：基于标记组合的流速统计
    logger.info("=" * 50)
    logger.info("阶段3: 基于标记组合的流速统计")
    logger.info("=" * 50)
    
    # 计算最终组合过滤的流速统计
    final_combined_df = apply_combined_filters(final_marked_df, filter_pipeline)
    speed_adjustment_factor = config_params.get('speed_adjustment_factor', 1.0)
    flow_stats = calculate_flow_statistics(final_combined_df, pixel_to_meter, fps, speed_adjustment_factor)
    
    # 保存结果
    save_analysis_results(
        output_path_halfway=output_path_halfway,
        tracking_df=tracking_df,
        final_marked_df=final_marked_df,  # 包含所有标记的完整数据
        filter_stats=filter_stats,
        flow_stats=flow_stats,
        config_params=config_params
    )
    
    # 生成流速变化趋势图
    create_flow_speed_trend_chart(filter_stats, flow_stats, output_path_halfway)
    
    # 返回结果
    processed_frames = tracking_df['time'].max() * fps if not tracking_df.empty else 0
    return (
        int(processed_frames),
        flow_stats['min_speed'],
        flow_stats['avg_speed'],
        flow_stats['max_speed'],
        used_detection_method
    )


def save_analysis_results(output_path_halfway, tracking_df, final_marked_df, filter_stats, flow_stats, config_params):
    """保存分析结果到文件"""
    logger.info("保存分析结果...")
    
    # 创建结果文本文件
    result_file_path = os.path.join(
        os.path.dirname(output_path_halfway), 
        f"{os.path.basename(output_path_halfway).split('_halfway')[0]}_otv_results_v2.txt"
    )
    
    try:
        with open(result_file_path, 'w', encoding='utf-8') as f:
            f.write("=== OTV 分析结果 (重构版本) ===\n\n")
            
            # 基本信息
            f.write(f"视频文件: {os.path.basename(output_path_halfway).split('_halfway')[0]}\n")
            f.write(f"分析方法: OTV (OpenCV Optical Flow) - 两阶段过滤架构\n")
            f.write(f"像素与米转换系数: {config_params.get('pixel_to_meter', 0.0086)}\n\n")
            
            # 数据收集统计
            f.write("=== 数据收集阶段 ===\n")
            f.write(f"原始数据点总数: {len(tracking_df)}\n")
            f.write(f"唯一特征点数量: {tracking_df['point_id'].nunique() if not tracking_df.empty else 0}\n\n")
            
            # 过滤统计
            f.write("=== 过滤阶段统计 ===\n")
            for stage, stats in filter_stats.items():
                # 为不同阶段设置合适的标题
                if stage == 'original':
                    f.write(f"\n原始数据（无过滤）:\n")
                else:
                    f.write(f"\n{stage.upper()} 过滤:\n")
                
                for key, value in stats.items():
                    f.write(f"  {key}: {value}\n")
                
                # 如果有流速统计，也写入文件
                if 'flow_stats' in stats and stats['flow_stats']:
                    if stage == 'original':
                        f.write(f"  原始数据流速统计:\n")
                    else:
                        f.write(f"  过滤后流速统计:\n")
                    for flow_key, flow_value in stats['flow_stats'].items():
                        if isinstance(flow_value, float):
                            f.write(f"    {flow_key}: {flow_value:.4f}\n")
                        else:
                            f.write(f"    {flow_key}: {flow_value}\n")
            
            # 最终流速统计
            f.write("\n=== 最终流速统计 ===\n")
            for key, value in flow_stats.items():
                f.write(f"{key}: {value}\n")
            
        logger.info(f"结果文件已保存: {result_file_path}")
        
    except Exception as e:
        logger.error(f"保存结果文件失败: {e}")
    
    # 保存CSV数据 - 使用原始完整数据，便于核查
    csv_file_path = os.path.join(
        os.path.dirname(output_path_halfway),
        f"{os.path.basename(output_path_halfway).split('_halfway')[0]}_otv_data_v2.csv"
    )
    
    try:
        # 保存包含所有过滤标记的完整数据，便于核查
        final_marked_df.to_csv(csv_file_path, index=False, encoding='utf-8')
        logger.info(f"CSV数据已保存（包含所有过滤标记的完整数据）: {csv_file_path}")
        logger.info(f"CSV包含 {len(final_marked_df)} 个数据点，{final_marked_df['point_id'].nunique()} 个特征点")
        
        # 显示CSV文件包含的列信息
        filter_columns = [col for col in final_marked_df.columns if col.endswith('_valid') or col.endswith('_dynamic') or col.endswith('_active')]
        if filter_columns:
            logger.info(f"CSV包含过滤标记列: {filter_columns}")
    except Exception as e:
        logger.error(f"保存CSV文件失败: {e}")

def process_video_pre_mode(cap, out, total_frames, roi_points=None, dominant_direction=None, concentration_range=90, add_grid_to_output=True):
    """处理视频的预处理模式，只绘制网格和ROI"""
    frame_count = 0
    fps = cap.get(cv2.CAP_PROP_FPS)
    max_frames_to_process = total_frames

    # 如果视频长度大于0且fps大于0，则计算30秒对应的帧数
    if total_frames > 0 and fps > 0:
        thirty_seconds_frames = int(30 * fps)
        if total_frames > thirty_seconds_frames:
            max_frames_to_process = thirty_seconds_frames
            print(f"OTV分析器 (Pre 模式): 视频长度超过30秒，将只处理前 {max_frames_to_process} 帧 (约30秒)。")
    
    if roi_points is not None:
        # 确保ROI点是整数类型的numpy数组
        roi_points = np.array(roi_points, dtype=np.int32)
        # 确保ROI点按顺时针排序
        roi_points = sort_polygon_points_clockwise(roi_points)
    
    while True:
        ret, frame = cap.read()
        if not ret: break
        
        if frame_count >= max_frames_to_process:
            print(f"OTV分析器 (Pre 模式): 已达到处理帧数上限 ({max_frames_to_process} 帧)。")
            break
            
        output_frame = frame.copy()
        if add_grid_to_output:
            print(f"DEBUG: Pre模式绘制网格，add_grid_to_output={add_grid_to_output}")
            output_frame = draw_grid(output_frame, draw_direction=True, 
                                    dominant_direction=dominant_direction, 
                                    concentration_range=concentration_range)
        else:
            print(f"DEBUG: Pre模式跳过网格绘制，add_grid_to_output={add_grid_to_output}")
        
        if roi_points is not None:
            cv2.polylines(output_frame, [roi_points], isClosed=True, color=(255, 0, 0), thickness=2)
        
        out.write(output_frame)
        frame_count += 1
        
        # 统一打印进度
        if frame_count % 30 == 0:
            print(f"OTV分析器: 已处理 {frame_count}/{max_frames_to_process if max_frames_to_process != total_frames else (total_frames if total_frames > 0 else '?')} 帧 (Pre 模式)")
    
    return frame_count

def analyze_video_segment_with_otv(video_path_str: str, config_params: dict) -> tuple[float, float, float, str] | None:
    """
    使用OpenCV的光流法 (OTV) 分析视频片段并计算流速。

    Args:
        video_path_str: 视频文件的路径。
        config_params: 包含分析所需参数的字典，
                       应包含 'otv_specific_params' 键，其值为OTV特定参数。

    Returns:
        一个包含 (最小流速, 平均流速, 最大流速, 特征检测方法) 的元组，如果处理失败则返回 None。
    """
    video_path = Path(video_path_str)
    if not video_path.exists():
        print(f"错误: OTV分析器 - 视频文件未找到: {video_path}")
        return None

    otv_specific_params = DEFAULT_OTV_PARAMS.copy()
    if 'otv_specific_params' in config_params:
        otv_specific_params.update(config_params['otv_specific_params'])
    
    # 获取运行模式
    mode = otv_specific_params.get('mode', 'real')
    check_feature_points = otv_specific_params.get('check_feature_points', False)
    roi_points_config = otv_specific_params.get('roi_points')
    feature_params = otv_specific_params.get('feature_params', dict(maxCorners=500, qualityLevel=0.01, minDistance=5, blockSize=7))
    print(f"OTV分析器: 从配置读取的feature_params: {feature_params}")
    lk_params = otv_specific_params.get('lk_params', dict(winSize=(15, 15), maxLevel=2, criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)))
    pixel_to_meter = otv_specific_params.get('pixel_to_meter') # 从传递的参数中获取
    if pixel_to_meter is None:
        logger.error("错误: OTV分析器 - pixel_to_meter 参数未在 otv_specific_params 中提供。")
        # 使用默认值作为后备
        pixel_to_meter = 0.0086
        logger.warning(f"使用默认 pixel_to_meter 值: {pixel_to_meter}")

    output_dir_param = otv_specific_params.get('output_dir') # 获取参数
    if output_dir_param is None:
        # 如果调用方没有提供 output_dir，则使用默认值 "data/output/otv"
        output_dir = Path("data/output/otv")
    else:
        # 如果调用方提供了 output_dir，则使用它
        output_dir = Path(output_dir_param)
        
    # output_dir = Path(otv_specific_params.get('output_dir', 'data/output')) # 旧逻辑
    min_feature_points_rediscover = otv_specific_params.get('min_feature_points_rediscover', 5)
    direction_concentration_range = otv_specific_params.get('direction_concentration_range', 90)
    feature_points_option = otv_specific_params.get('feature_points_option', 'all')
    trajectory_static_threshold = otv_specific_params.get('trajectory_static_threshold', 10.0)
    quartile_filter_min_points = otv_specific_params.get('quartile_filter_min_points', 20)
    add_grid_to_output = otv_specific_params.get('add_grid_to_output_video', True)
    add_visual_elements_to_trimmed_video = otv_specific_params.get('add_visual_elements_to_trimmed_video', False)
    window_avg_time_seconds = otv_specific_params.get('window_avg_time_seconds', 1.0)
    motion_decay_threshold = otv_specific_params.get('motion_decay_threshold', 0.1)
    print(f"DEBUG: add_grid_to_output_video从配置中读取值: {add_grid_to_output}")
    print(f"DEBUG: add_visual_elements_to_trimmed_video从配置中读取值: {add_visual_elements_to_trimmed_video}")
    print(f"DEBUG: window_avg_time_seconds从配置中读取值: {window_avg_time_seconds}")
    print(f"DEBUG: motion_decay_threshold从配置中读取值: {motion_decay_threshold}")

    output_dir.mkdir(parents=True, exist_ok=True)
    
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"错误: OTV分析器 - 无法打开视频: {video_path}")
        return None

    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    ret, first_frame = cap.read()
    if not ret:
        print("错误: OTV分析器 - 无法读取视频的第一帧。")
        cap.release()
        return None
    
    height, width = first_frame.shape[:2]
    
    # 重置到第一帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
    
    # 设置视频输出文件路径
    if mode == 'pre':
        output_path = output_dir / f"{video_path.stem}_pre_flow.mp4"
    else:  # real mode
        output_path = output_dir / f"{video_path.stem}_output_flow.mp4"
    
    output_path_halfway = output_dir / f"{video_path.stem}_halfway_flow.mp4"
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    fps = cap.get(cv2.CAP_PROP_FPS)
    out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
    
    # 根据模式调用不同的处理函数
    frame_count = 0
    avg_speed = 0.0
    min_speed = 0.0
    max_speed = 0.0
    
    if mode == 'pre':
        frame_count = process_video_pre_mode(cap, out, total_frames, roi_points_config, None, direction_concentration_range, add_grid_to_output)
        # Pre模式不计算速度，可以返回0或None，这里返回None以示区分
        cap.release()
        out.release()
        print(f"OTV分析器: 视频已保存到: {str(output_path)}")
        return None
    else:  # real mode
        frame_count, min_s, avg_s, max_s, used_detection_method = process_video_real_mode(
                cap, str(output_path_halfway), otv_specific_params, out
            )
        
        min_speed, avg_speed, max_speed = min_s, avg_s, max_s
    
    # 释放资源
    cap.release()
    out.release()
    print(f"OTV分析器: 视频已保存到: {str(output_path)}")
    
    if frame_count > 0 : # 只有在实际处理了帧的情况下才认为速度有效
        return (min_speed, avg_speed, max_speed, used_detection_method)
    else:
        if otv_specific_params.get('mode') == 'pre':
            print("\n独立OTV分析完成 (Pre 模式)。Pre 模式不计算流速，仅生成预览视频。")
        else: # Real 模式或其他导致 calculated_speeds 为 None 的情况
            print("\n独立OTV分析失败或未返回有效速度。请检查视频文件、配置参数或处理过程中是否有错误日志。")
        return None

def main_otv_analyzer():
    """独立运行OTV分析器的入口函数。"""
    print("===== 独立运行 OTV 分析器 =====")
    
    default_config_path = Path('config/batch_config.yaml')
    config_data = {}
    if default_config_path.exists():
        try:
            with open(default_config_path, 'r') as f:
                config_data = yaml.safe_load(f)
            print(f"已加载配置文件: {default_config_path}")
        except yaml.YAMLError as e:
            print(f"警告: 解析配置文件 {default_config_path} 失败: {e}")
        except Exception as e:
            print(f"警告: 加载配置文件 {default_config_path} 时发生未知错误: {e}")

    otv_standalone_params = config_data.copy() # DEFAULT_OTV_PARAMS.copy()
    if 'otv_params' in config_data:
        otv_standalone_params.update(config_data['otv_params'])
        print("OTV参数已从配置文件加载 (注意: pixel_to_meter应在全局配置)。")
        # 调试：检查feature_params是否正确读取
        if 'feature_params' in otv_standalone_params:
            print(f"从配置文件读取的feature_params: {otv_standalone_params['feature_params']}")
        else:
            print("警告: 配置文件中未找到feature_params")
    
    # 优先从全局配置获取 pixel_to_meter
    if 'pixel_to_meter' in config_data:
        otv_standalone_params['pixel_to_meter'] = config_data['pixel_to_meter']
        print(f"已从全局配置加载 pixel_to_meter: {otv_standalone_params['pixel_to_meter']}")
    elif 'pixel_to_meter' not in otv_standalone_params: # 如果全局没有，且otv_params中也没有（例如旧配置文件）
        print("警告: 'pixel_to_meter' 未在配置文件中找到 (全局或otv_params)。将使用硬编码的默认值 0.0086 进行独立测试。")
        otv_standalone_params['pixel_to_meter'] = 0.0086 # 硬编码的备用值，仅用于独立测试
    else:
        # 如果 otv_standalone_params 中已存在 pixel_to_meter (例如来自旧的 otv_params)
        print(f"使用来自 otv_params 的 pixel_to_meter: {otv_standalone_params['pixel_to_meter']} (建议移至全局配置)")

    if 'roi_points' not in otv_standalone_params and 'roi_points' in config_data: # 尝试从全局获取ROI
        otv_standalone_params['roi_points'] = config_data['roi_points']

    # 如果上述步骤后 pixel_to_meter 仍未设置，则发出警告并可能设置一个最终的默认值
    if 'pixel_to_meter' not in otv_standalone_params:
        print("严重警告: pixel_to_meter 未能通过任何方式配置。独立测试可能不准确。")
        # otv_standalone_params['pixel_to_meter'] = 0.0086 # 最后的防线

    # 创建输出目录（如果不存在）
    # output_dir = Path(otv_standalone_params.get('output_dir', 'data/output/otv_standalone_test')) # 旧的独立测试路径
    # 对于独立运行，直接使用 "data/output/otv"
    output_dir_str_for_standalone = otv_standalone_params.get('output_dir')
    if output_dir_str_for_standalone is None:
        output_dir_for_standalone = Path("data/output/otv")
        otv_standalone_params['output_dir'] = str(output_dir_for_standalone) # 更新参数字典以传递给分析函数
    else:
        output_dir_for_standalone = Path(output_dir_str_for_standalone)
        
    output_dir_for_standalone.mkdir(parents=True, exist_ok=True)
    batch_settings = otv_standalone_params.get('batch_settings',{})
    test_video_path_str = batch_settings.get('input_videos',[])[0]

    config_for_analyzer_call = {'otv_specific_params': otv_standalone_params}

    if otv_standalone_params['mode'] == 'pre':
        print("已选择 Pre 模式")
    else:
        print("已选择 Real 模式")
        
        # 仅在 Real 模式下询问是否检查特征点
        if otv_standalone_params['check_feature_points']:
            print("将在处理到一半时暂停，以便检查和选择特征点")
        else:
            print("将直接处理完整个视频")

    print(f"\n开始使用OTV分析视频: {test_video_path_str}")
    print(f"使用参数: {config_for_analyzer_call['otv_specific_params']}")
    
    calculated_speeds = analyze_video_segment_with_otv(test_video_path_str, config_for_analyzer_call)
    
    if calculated_speeds is not None:
        min_s, avg_s, max_s = calculated_speeds
        print(f"\n独立OTV分析完成。")
        print(f"  计算得到的最小流速: {min_s:.4f} m/s")
        print(f"  计算得到的平均流速: {avg_s:.4f} m/s")
        print(f"  计算得到的最大流速: {max_s:.4f} m/s")
    else:
        if otv_standalone_params.get('mode') == 'pre':
            print("\n独立OTV分析完成 (Pre 模式)。Pre 模式不计算流速，仅生成预览视频。")
        else: # Real 模式或其他导致 calculated_speeds 为 None 的情况
            print("\n独立OTV分析失败或未返回有效速度。请检查视频文件、配置参数或处理过程中是否有错误日志。")

if __name__ == "__main__":
    main_otv_analyzer() 