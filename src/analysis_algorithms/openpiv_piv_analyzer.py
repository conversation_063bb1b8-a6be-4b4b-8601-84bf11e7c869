#!/usr/bin/env python
import json
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
import cv2
import sys
import os
import time # 用于可能的延时和性能分析
import warnings # 用于忽略警告
# 导入OpenPIV相关库
try:
    from openpiv import tools, pyprocess, validation, filters, scaling
except ImportError as e:
    print(f"导入OpenPIV库时出错: {e}", file=sys.stderr)
    print("请确保OpenPIV库已正确安装，可使用pip install openpiv命令安装", file=sys.stderr)
    sys.exit(1)

# 假设logging_utils在父目录的utils下
try:
    from ..utils.logging_utils import setup_logger # 相对导入
except ImportError:
    # 如果作为脚本直接运行，尝试修改sys.path
    sys.path.append(str(Path(__file__).resolve().parents[2])) # 添加项目根目录到sys.path
    from src.utils.logging_utils import setup_logger

# 设置日志 - 根据文件位置调整logger名称
logger = setup_logger("openpiv_analyzer")

def _perform_piv_for_frame_pair(
    frame_a_processed: np.ndarray,
    frame_b_processed: np.ndarray,
    config_dict: dict,
    dt: float,
    pixel_to_meter: float,
    offset_x: int,
    offset_y: int,
    output_dir_for_pair: Path,
    background_visualization_frame: np.ndarray,
    video_name_stem: str,
    pair_identifier_string: str
):
    """
    对一对预处理过的帧执行PIV分析、保存结果并生成可视化图像。
    
    Args:
        frame_a_processed: 经过灰度化和ROI裁剪（如果应用）的第一帧。
        frame_b_processed: 经过灰度化和ROI裁剪（如果应用）的第二帧。
        config_dict: 包含PIV参数的字典。
        dt: 帧间时间间隔 (秒)。
        pixel_to_meter: 物理尺度转换因子 (米/像素)。
        offset_x: ROI裁剪在X方向的偏移量。
        offset_y: ROI裁剪在Y方向的偏移量。
        output_dir_for_pair: 此帧对结果的输出目录。
        background_visualization_frame: 用于可视化的背景帧 (通常是原始的frame_a，未裁剪)。
        video_name_stem: 视频文件的主名，用于命名输出文件。
        pair_identifier_string: 帧对的唯一标识字符串 (例如 "pair_0001")。
        
    Returns:
        tuple: (pair_avg_speed_mps, pair_max_speed_mps, pair_min_speed_mps)
               如果分析失败或无有效向量，则返回 (0.0, 0.0, 0.0)。
    """
    piv_params = config_dict.get("piv_params", {})
    winsize = piv_params.get("window_size", 32)
    overlap = piv_params.get("overlap", 16)
    searchsize = piv_params.get("search_area_size", 64)
    sig2noise_threshold = piv_params.get("sig2noise_threshold", 1.05)
    correlation_method = piv_params.get("correlation_method", "circular")
    outlier_method = piv_params.get("outlier_method", "localmean")
    outlier_max_iter = piv_params.get("outlier_max_iter", 3)
    outlier_kernel_size = piv_params.get("outlier_kernel_size", 3)

    output_dir_for_pair.mkdir(parents=True, exist_ok=True)
    logger.debug(f"开始处理帧对 {pair_identifier_string}，输出到 {output_dir_for_pair}")

    pair_avg_speed_mps, pair_max_speed_mps, pair_min_speed_mps = 0.0, 0.0, 0.0
    valid_speed_scaled_for_stats = np.array([]) # 初始化为空，用于统计

    try:
        # 1. 执行PIV分析 (使用处理后的帧)
        u0, v0, sig2noise = pyprocess.extended_search_area_piv(
            frame_a_processed.astype(np.int32),
            frame_b_processed.astype(np.int32),
            window_size=winsize,
            overlap=overlap,
            dt=dt,
            search_area_size=searchsize,
            sig2noise_method='peak2peak',
            correlation_method=correlation_method
        )
        
        # 2. 获取相对坐标 (基于处理后的帧尺寸)
        x_relative, y_relative = pyprocess.get_coordinates(
            image_size=frame_a_processed.shape,
            search_area_size=searchsize,
            overlap=overlap
        )
        # 将相对坐标调整回原始图像坐标系
        x = x_relative + offset_x
        y = y_relative + offset_y

        # 3. 验证和滤波
        mask = validation.sig2noise_val(sig2noise, threshold=sig2noise_threshold)
        u_filtered, v_filtered = filters.replace_outliers(
            u0, v0, mask,
            method=outlier_method,
            max_iter=outlier_max_iter,
            kernel_size=outlier_kernel_size
        )
        u_filtered[mask] = np.nan # 标记无效向量
        v_filtered[mask] = np.nan

        # 4. 缩放: 像素/dt -> 物理单位 (米/秒)
        # 注意：OpenPIV的pyprocess.extended_search_area_piv已经返回的是 u,v in pixels/dt
        # 所以 u_scaled (m/s) = u_filtered (pixels/dt) * pixel_to_meter (m/pixel) / (1/dt) (1/s) 是错误的
        # 正确的是: u_scaled (m/s) = u_filtered (pixels/dt) * pixel_to_meter (m/pixel)
        # 因为 dt 已经在 PIV 计算中被考虑，结果的速度单位是 像素/dt。
        # scaling.uniform 将速度从 像素/dt 转换为 物理单位/秒，需要提供 dt 和 pixel_to_meter
        
        # 为避免混淆，这里直接使用 u_filtered * pixel_to_meter，假设 u_filtered 是 像素/帧间隔时间
        # pixel_to_meter 本身的定义需要清晰：是 物理长度/像素数
        # dt 是 帧间隔对应的真实时间（秒）
        # 速度 (物理单位/秒) = (像素/帧间隔) * (物理单位/像素) / (帧间隔时间/帧间隔)
        #                  = (像素/帧间隔) * (物理单位/像素) / dt
        # 如果 extended_search_area_piv 的 dt 参数使返回的 u0, v0 是 像素/秒，则：
        # 速度 (物理单位/秒) = (像素/秒) * (物理单位/像素)
        #
        # 查阅 OpenPIV 文档: The velocities u,v are in pixels/dt.
        # 所以， u_scaled (m/s) = u_filtered (pixels/dt) * pixel_to_meter (m/pixel)
        # 这个理解是正确的。

        u_scaled = np.round(u_filtered * pixel_to_meter, 4) # 单位: 米/dt (如果dt=1, 则是米/帧时间) -> 应该是 米/秒 (如果dt以秒为单位)
        v_scaled = np.round(v_filtered * pixel_to_meter, 4) # 单位: 米/dt

        # 修正: extended_search_area_piv 中的 dt 参数是帧间时间间隔。
        # 返回的 u0, v0 是以 pixels/dt 为单位。所以乘以 pixel_to_meter 后是 m/dt。
        # 若要得到 m/s，因为 dt 本身就是秒，所以 m/dt 就是 m/s。
        # u_scaled (m/s) = u_filtered (pixels/dt) * pixel_to_meter (m/pixel)
        # v_scaled (m/s) = v_filtered (pixels/dt) * pixel_to_meter (m/pixel)

        # 5. 保存结果到文本文件 (物理单位)
        result_txt_path = output_dir_for_pair / f"{video_name_stem}_{pair_identifier_string}_piv_result.txt"
        count_saved_vectors = 0
        with open(str(result_txt_path), 'w') as f:
            f.write("x_pixel y_pixel u_mps v_mps sig2noise_val mask_applied\n")
            for r_idx in range(u_scaled.shape[0]):
                for c_idx in range(u_scaled.shape[1]):
                    # 保存所有点，包括NaN，让后续处理决定如何使用
                    f.write(f"{x[r_idx, c_idx]} {y[r_idx, c_idx]} {u_scaled[r_idx, c_idx]} {v_scaled[r_idx, c_idx]} {sig2noise[r_idx, c_idx]} {int(mask[r_idx, c_idx])}\n")
                    if not np.isnan(u_scaled[r_idx, c_idx]) and not np.isnan(v_scaled[r_idx, c_idx]):
                        count_saved_vectors +=1
        logger.debug(f"为 {pair_identifier_string} 保存了 {count_saved_vectors} 个有效PIV向量到: {result_txt_path}")

        # 6. 创建可视化
        result_img_path = output_dir_for_pair / f"{video_name_stem}_{pair_identifier_string}_piv_result.png"
        fig, ax = plt.subplots(figsize=(10, int(10 * background_visualization_frame.shape[0]/background_visualization_frame.shape[1]) if background_visualization_frame.shape[1] > 0 else 8))
        plt.rcParams['font.family'] = 'DejaVu Sans'
        ax.set_title(f'PIV Vectors for {pair_identifier_string} ({video_name_stem})', fontsize=14)
        ax.set_xlabel('X position (pixels)', fontsize=12)
        ax.set_ylabel('Y position (pixels)', fontsize=12)
        ax.imshow(background_visualization_frame, cmap='gray', alpha=0.8)

        try:
            with np.errstate(invalid='ignore'): # 忽略 sqrt(nan)
                speed_scaled = np.sqrt(u_scaled**2 + v_scaled**2)
            valid_speed_scaled_for_stats = speed_scaled[~np.isnan(speed_scaled)]
        except FloatingPointError:
            logger.warning(f"计算速度大小时遇到浮点错误 for {pair_identifier_string}。")
            speed_scaled = np.full_like(u_scaled, np.nan)
            valid_speed_scaled_for_stats = np.array([])

        valid_plot_mask = ~np.isnan(u_scaled) & ~np.isnan(v_scaled)
        num_valid_plot_vectors = np.sum(valid_plot_mask)

        if num_valid_plot_vectors > 0:
            with warnings.catch_warnings(): # 新增的警告捕获上下文
                warnings.filterwarnings(
                    'ignore',
                    category=UserWarning,
                    message="Warning: 'partition' will ignore the 'mask' of the MaskedArray."
                )

                x_valid_plot = x[valid_plot_mask]
                y_valid_plot = y[valid_plot_mask]
                u_valid_scaled_plot = u_scaled[valid_plot_mask]
                v_valid_scaled_plot = v_scaled[valid_plot_mask]
                speed_valid_for_color_plot = speed_scaled[valid_plot_mask]

                step = max(1, int(np.sqrt(num_valid_plot_vectors) // 25))
                median_speed_for_scale = np.median(speed_valid_for_color_plot) if len(speed_valid_for_color_plot) > 0 else 0.0
                with warnings.catch_warnings(): # 原有的警告捕获块
                    warnings.filterwarnings('ignore', category=RuntimeWarning)
                    scale_value = max(1.0, median_speed_for_scale * 50.0) if median_speed_for_scale > 1e-6 else 50.0

                Q = ax.quiver(x_valid_plot[::step], y_valid_plot[::step],
                             u_valid_scaled_plot[::step], v_valid_scaled_plot[::step],
                             speed_valid_for_color_plot[::step],
                             scale=scale_value, scale_units='xy', width=0.003, cmap='jet', angles='xy')
                try:
                    cbar = plt.colorbar(Q, ax=ax, label='Velocity (m/s)', shrink=0.8)
                    cbar.ax.tick_params(labelsize=10)
                except Exception as cbar_err:
                    logger.warning(f"无法创建 colorbar for {pair_identifier_string}: {cbar_err}")

                if len(speed_valid_for_color_plot) > 0:
                    ref_vel = np.percentile(speed_valid_for_color_plot, 90)
                    if ref_vel > 1e-9:
                        ref_vel_display = float(f"{ref_vel:.2g}")
                        ax.quiverkey(Q, 0.85, 0.05, ref_vel_display, f'{ref_vel_display} m/s',
                                     labelpos='E', coordinates='axes', color='black', fontproperties={'size': 10})
        else:
            logger.info(f"帧对 {pair_identifier_string} 没有有效的速度向量可以绘制。")
        
        # 绘制ROI轮廓 (如果ROI应用了) - 使用原始图像坐标系的ROI点
        roi_points_list = config_dict.get("common_params", {}).get("roi_points", None)
        if offset_x > 0 or offset_y > 0: # 表示ROI裁剪被应用了
            if roi_points_list and len(roi_points_list) >=3:
                 roi_polygon = plt.Polygon(np.array(roi_points_list), fill=False, edgecolor='r', linewidth=1.5)
            ax.add_patch(roi_polygon)


        ax.set_aspect('equal', adjustable='box')
        ax.set_xlim(0, background_visualization_frame.shape[1])
        ax.set_ylim(background_visualization_frame.shape[0], 0)
        plt.tight_layout(pad=1.5)
        plt.savefig(str(result_img_path), dpi=150)
        plt.close(fig)
        logger.debug(f"为 {pair_identifier_string} 保存PIV结果图像到: {result_img_path}")

        # 7. 计算此帧对的统计数据
        if len(valid_speed_scaled_for_stats) > 0:
            pair_avg_speed_mps = float(np.mean(valid_speed_scaled_for_stats))
            pair_max_speed_mps = float(np.max(valid_speed_scaled_for_stats))
            pair_min_speed_mps = float(np.min(valid_speed_scaled_for_stats))
        else: # 如果没有有效速度，则统计值为0
            pair_avg_speed_mps = 0.0
            pair_max_speed_mps = 0.0
            pair_min_speed_mps = 0.0
            logger.info(f"帧对 {pair_identifier_string} 没有有效向量计算统计。")

        
    except Exception as e:
        logger.error(f"处理帧对 {pair_identifier_string} 时发生错误: {e}", exc_info=True)
        return 0.0, 0.0, 0.0 # 返回0表示此帧对处理失败

    return pair_avg_speed_mps, pair_max_speed_mps, pair_min_speed_mps


def analyze_video_segment_with_openpiv(
    video_path: str,
    config_path: str,
    pixel_to_meter: float,
    roi_override_points: np.ndarray = None
):
    """
    对指定的视频片段进行完整的PIV分析，循环处理帧对并聚合结果。
    
    Args:
        video_path: 待分析的视频片段的完整路径。
        config_path: PIV参数配置文件路径 (JSON)。
        pixel_to_meter: 物理尺度转换因子 (米/像素)。
        roi_override_points (optional): NumPy数组, 包含ROI坐标点。如果提供, 将覆盖配置文件中的ROI。
        
    Returns:
        tuple: (final_avg_of_min_speeds, final_avg_of_avg_speeds, final_avg_of_max_speeds, successful_pairs_count)
               如果分析失败或无有效帧对，速度值为0.0，计数为0。
    """
    logger.info(f"开始分析视频片段: {video_path} using config: {config_path}")
    output_dir_base = "data/output/piv" # 固定输出基础目录

    # 1. 加载配置
    try:
        with open(config_path, 'r') as f:
            config_dict = json.load(f)
        logger.info(f"已加载配置文件: {config_path}")
    except Exception as e:
        logger.error(f"加载配置文件 {config_path} 时出错: {e}")
        return 0.0, 0.0, 0.0, 0

    common_params = config_dict.get("common_params", {})
    dt = common_params.get("dt", 0.02) # 帧间时间间隔, 秒

    # 2. 确定ROI
    current_roi_points_list = common_params.get("roi_points", None)
    if roi_override_points is not None and roi_override_points.size > 0 :
        if isinstance(roi_override_points, np.ndarray) and roi_override_points.ndim == 2 and roi_override_points.shape[0] >=3 and roi_override_points.shape[1] == 2:
            current_roi_points_list = roi_override_points.tolist() # 转为list给config_dict用
            config_dict["common_params"]["roi_points"] = current_roi_points_list # 更新config_dict中的值
            logger.info(f"使用覆盖的ROI点: {len(current_roi_points_list)}个点")
        else:
            logger.warning("提供的roi_override_points格式不正确，将尝试使用配置文件中的ROI。")
    
    # 将 current_roi_points_list (可能是列表或None) 转换为 NumPy 数组用于裁剪
    roi_for_cropping = None
    if current_roi_points_list is not None and len(current_roi_points_list) >= 3:
        roi_for_cropping = np.array(current_roi_points_list)
        if not (roi_for_cropping.ndim == 2 and roi_for_cropping.shape[1] == 2):
             logger.warning("配置文件或覆盖的ROI点格式不正确，将不使用ROI裁剪。")
             roi_for_cropping = None # 重置为None

    # 3. 创建输出目录
    video_path_obj = Path(video_path)
    video_name_stem = video_path_obj.stem
    # 输出目录结构: output_dir_base / video_name_stem_piv_results /
    segment_output_root_dir = Path(output_dir_base) / f"{video_name_stem}_piv_results"
    segment_output_root_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"视频片段分析结果将保存到: {segment_output_root_dir}")

    # 4. 打开视频并获取信息
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        logger.error(f"无法打开视频: {video_path}")
        return 0.0, 0.0, 0.0, 0
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    if fps <= 0:
        logger.warning(f"无法获取视频 {video_path} 的FPS，将使用默认值 25.0 fps。")
        fps = 25.0
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    logger.info(f"视频 {video_path}: FPS={fps:.2f}, 总帧数={total_frames}")

    # 5. 计算帧跳过数量
    # frame_skip_for_dt 是指第二帧相对于第一帧的索引差
    # 例如，dt=0.04s, fps=25 -> skip=1 (取第0和第1帧，时间差1/25=0.04s)
    # dt=0.08s, fps=25 -> skip=2 (取第0和第2帧，时间差2/25=0.08s)
    frame_skip_for_dt = max(1, int(round(dt * fps)))
    logger.info(f"根据 dt={dt}s 和 fps={fps:.2f}, 计算得到帧对的帧间隔 frame_skip_for_dt = {frame_skip_for_dt}")
    if frame_skip_for_dt == 0: # 避免 dt 过小导致跳过0帧
        logger.warning("计算的 frame_skip_for_dt 为0，强制设为1。请检查dt和fps设置。")
        frame_skip_for_dt = 1


    # 6. 初始化结果存储列表
    all_segment_avg_speeds = []
    all_segment_max_speeds = []
    all_segment_min_speeds = []
    
    pair_counter = 0
    successful_pairs_count = 0
    # 帧读取指针。每次循环，frame1_ptr 指向第一帧，frame2_ptr 指向第二帧。
    # 下一次循环，frame1_ptr 向前移动。
    # 移动步长决定了帧对之间是否有重叠或跳跃。
    # 简单连续采样: frame1_ptr 每次增加 frame_skip_for_dt
    frame1_read_idx = 0

    while True:
        # 设置当前帧对的读取位置
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame1_read_idx)
        ret1, frame1_original = cap.read()
        
        # 检查第一帧是否成功读取
        if not ret1:
            logger.info(f"无法读取索引为 {frame1_read_idx} 的第一帧，可能已到达视频末尾。")
            break

        frame2_read_idx = frame1_read_idx + frame_skip_for_dt
        # 检查第二帧的索引是否超出视频总帧数
        if frame2_read_idx >= total_frames :
            logger.info(f"计算得到的第二帧索引 {frame2_read_idx} 超出总帧数 {total_frames}。结束分析。")
            break

        cap.set(cv2.CAP_PROP_POS_FRAMES, frame2_read_idx)
        ret2, frame2_original = cap.read()

        if not ret2:
            logger.warning(f"无法读取索引为 {frame2_read_idx} 的第二帧，尽管它未超出总帧数。可能视频损坏或读取问题。跳过此帧对。")
            frame1_read_idx += frame_skip_for_dt # 尝试移动到下一可能的帧对
            continue
        
        pair_counter += 1
        pair_identifier_string = f"pair_{pair_counter:04d}"
        logger.info(f"正在处理 {pair_identifier_string}: (frame {frame1_read_idx}, frame {frame2_read_idx})")

        frame1_gray = cv2.cvtColor(frame1_original, cv2.COLOR_BGR2GRAY)
        frame2_gray = cv2.cvtColor(frame2_original, cv2.COLOR_BGR2GRAY)

        offset_x_current_pair, offset_y_current_pair = 0, 0
        frame_a_to_piv = frame1_gray
        frame_b_to_piv = frame2_gray
        
        # 应用ROI裁剪 (如果 roi_for_cropping 有效)
        if roi_for_cropping is not None:
            try:
                # 计算 ROI 边界框 (确保整数索引并在图像范围内)
                ymin = max(0, int(np.min(roi_for_cropping[:, 1])))
                ymax = min(frame1_gray.shape[0], int(np.max(roi_for_cropping[:, 1])))
                xmin = max(0, int(np.min(roi_for_cropping[:, 0])))
                xmax = min(frame1_gray.shape[1], int(np.max(roi_for_cropping[:, 0])))

                if ymax > ymin and xmax > xmin:
                    frame_a_to_piv = frame1_gray[ymin:ymax, xmin:xmax]
                    frame_b_to_piv = frame2_gray[ymin:ymax, xmin:xmax]
                    offset_y_current_pair, offset_x_current_pair = ymin, xmin
                    logger.debug(f"{pair_identifier_string}: 已应用ROI裁剪，处理尺寸: {frame_a_to_piv.shape}, 偏移量: (x={offset_x_current_pair}, y={offset_y_current_pair})")
                else:
                    logger.warning(f"{pair_identifier_string}: ROI坐标无效或导致裁剪后尺寸为零，将使用完整图像。")
            except Exception as crop_error:
                 logger.error(f"{pair_identifier_string}: 处理ROI坐标或裁剪图像时出错: {crop_error}，将使用完整图像。")
                 offset_x_current_pair, offset_y_current_pair = 0, 0 # 重置偏移量
                 frame_a_to_piv = frame1_gray # 确保使用原始灰度帧
                 frame_b_to_piv = frame2_gray
        
        # 为当前帧对创建输出子目录
        output_dir_for_this_pair = segment_output_root_dir / pair_identifier_string
        # (根据 analysis_params.save_individual_pair_results 决定是否真的创建和保存)
        # 简化：总是创建目录并调用，_perform_piv_for_frame_pair 内部可以根据配置决定是否实际写入文件
        # 但为了减少文件IO，如果配置为不保存，可以跳过部分写入。目前设计是都会写。

        avg_s, max_s, min_s = _perform_piv_for_frame_pair(
            frame_a_processed=frame_a_to_piv,
            frame_b_processed=frame_b_to_piv,
            config_dict=config_dict,
            dt=dt,
            pixel_to_meter=pixel_to_meter,
            offset_x=offset_x_current_pair,
            offset_y=offset_y_current_pair,
            output_dir_for_pair=output_dir_for_this_pair,
            background_visualization_frame=frame1_gray, # 使用原始灰度帧1作为背景
            video_name_stem=video_name_stem,
            pair_identifier_string=pair_identifier_string
        )

        # 只有当 avg_s, max_s, min_s 都非零时（或根据更具体的成功标准）才认为这对分析有效
        # _perform_piv_for_frame_pair 在错误时返回 0,0,0。
        # 如果 avg_s > 0 (或存在有效向量)，则认为成功。
        # 简单的检查：如果平均速度大于一个极小值（考虑到浮点精度和可能的真实零速）
        # 或者更鲁棒的是，_perform_piv_for_frame_pair 返回一个成功标志
        # 当前实现是，如果 len(valid_speed_scaled_for_stats) > 0，则计算统计，否则为0.
        # 所以，如果 avg_s, max_s, min_s 同时为0，可能意味着没有有效速度。
        # 但如果真实速度就是0，这也可能发生。
        # 一个更好的指标是 _perform_piv_for_frame_pair 返回处理的有效向量数。
        # 暂时以avg_s, max_s, min_s是否都为0（且非真实情况下的0）来判断。
        # 修改：如果_perform_piv_for_frame_pair能够返回有效向量数，判断会更准确。
        # 暂时假设返回的统计数据非零即为有效。
        # if not (avg_s == 0.0 and max_s == 0.0 and min_s == 0.0): # 这可能排除真实零速情况
        # 简单的处理：只要函数没在内部崩溃返回0,0,0，就认为是一次尝试
        # 实际上，我们需要区分"PIV成功但速度是0"和"PIV失败所以速度是0"
        # 改为：如果_perform_piv_for_frame_pair中count_saved_vectors > 0 (已移入_perform_piv_for_frame_pair)
        # 或者更简单，只要_perform_piv_for_frame_pair的调用本身没抛异常，就收集其结果。
        # 如果某些帧对确实没有流动，其速度指标自然是0.
        logger.info(f"完成视频片段从{frame1_read_idx}到{frame2_read_idx}的处理，{pair_identifier_string}: 收集到的速度指标: avg_s={avg_s}, max_s={max_s}, min_s={min_s}")
        all_segment_avg_speeds.append(avg_s)
        all_segment_max_speeds.append(max_s)
        all_segment_min_speeds.append(min_s)
        successful_pairs_count += 1 # 每次调用都认为是一次成功的尝试，除非内部完全失败

        # 更新下一对帧的起始读取位置 (连续采样)
        frame1_read_idx += frame_skip_for_dt
    
    cap.release()
    logger.info(f"视频 {video_path} 处理完毕。共分析了 {successful_pairs_count} 个帧对。")

    # 7. 聚合结果
    final_avg_of_avg_speeds = np.mean(all_segment_avg_speeds) if all_segment_avg_speeds else 0.0
    final_avg_of_max_speeds = np.mean(all_segment_max_speeds) if all_segment_max_speeds else 0.0
    final_avg_of_min_speeds = np.mean(all_segment_min_speeds) if all_segment_min_speeds else 0.0
    
    # 8. 保存汇总结果
    summary_stats_path = segment_output_root_dir / f"_video_summary_stats.json"
    summary_data = {
        "video_path": video_path,
        "config_path": config_path,
        "total_frames_in_video": total_frames,
        "fps": fps,
        "dt_config": dt,
        "frame_skip_for_dt_calculated": frame_skip_for_dt,
        "pixel_to_meter": pixel_to_meter,
        "roi_applied": roi_for_cropping is not None,
        "roi_points_used": current_roi_points_list if roi_for_cropping is not None else "None",
        "analyzed_frame_pairs": successful_pairs_count,
        "average_of_average_speeds_mps": float(final_avg_of_avg_speeds),
        "average_of_maximum_speeds_mps": float(final_avg_of_max_speeds),
        "average_of_minimum_speeds_mps": float(final_avg_of_min_speeds),
        "individual_pair_avg_speeds": [float(s) for s in all_segment_avg_speeds],
        "individual_pair_max_speeds": [float(s) for s in all_segment_max_speeds],
        "individual_pair_min_speeds": [float(s) for s in all_segment_min_speeds],
    }
    try:
        with open(summary_stats_path, 'w') as f:
            json.dump(summary_data, f, indent=4)
        logger.info(f"已保存视频片段的汇总统计数据到: {summary_stats_path}")
    except Exception as e:
        logger.error(f"保存汇总统计数据失败: {e}")

    analysis_params = config_dict.get("analysis_params", {})
    save_individual_pair_results = analysis_params.get("save_individual_pair_results", True) # 默认为True
    
    if not save_individual_pair_results:
        logger.info("配置为不保存单个帧对的详细结果，将尝试清理...")
        # 清理单个帧对的目录。这部分比较复杂，因为_perform_piv_for_frame_pair总是会写。
        # 一个更优的策略是_perform_piv_for_frame_pair内部检查这个flag，决定是否写文件。
        # 暂时，如果设置为False，这里不主动删除，依赖用户理解或后续清理脚本。
        # 或者，可以迭代删除已创建的 pair_* 目录。
        # for item in segment_output_root_dir.iterdir():
        #     if item.is_dir() and item.name.startswith("pair_"):
        #         # shutil.rmtree(item) # 危险操作，需要shutil
        #         logger.info(f"模拟删除: {item}") #  暂不实际删除
        pass


    return float(final_avg_of_min_speeds), float(final_avg_of_avg_speeds), float(final_avg_of_max_speeds), successful_pairs_count

if __name__ == "__main__":
    # 这个 main 块用于独立测试 openpiv_analyzer.py
    logger.info("正在以独立模式运行 openpiv_analyzer.py")

    # === 配置测试参数 ===
    # 请确保这些路径和文件存在，或者根据您的环境修改
    # 使用相对路径，假设脚本在 src/piv/ 目录下，数据和配置在项目根目录下的 data/ 和 config/
    project_root = Path(__file__).resolve().parents[2]
    
    # test_video_path = project_root / "data" / "video" / "ch01_20250414145900.mp4" # 替换为您的测试视频
    # test_video_path = project_root / "data" / "output" / "test_video_trimmed_0-10_to_0-15.mp4"
    test_video_path = project_root / "data" / "video" / "default_sample.mp4" # 使用一个通用的短视频
    
    test_config_path = project_root / "config" / "piv_config.json"
    test_output_dir_base = project_root / "data" / "output" / "piv" # 基础输出目录，与函数内固定值一致
    
    # 创建一个简单的测试视频，如果default_sample.mp4不存在
    if not test_video_path.exists():
        logger.info(f"测试视频 {test_video_path} 不存在，尝试创建一个...")
        try:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            # 创建一个5秒，25fps，320x240的视频
            out_test_vid = cv2.VideoWriter(str(test_video_path), fourcc, 25.0, (320, 240))
            for i in range(125): # 5 seconds * 25 fps
                frame = np.zeros((240, 320, 3), dtype=np.uint8)
                # 添加一些简单的移动元素，方便PIV分析
                cv2.circle(frame, (50 + i*2 , 120), 20, (0, 255, 0), -1)
                cv2.putText(frame, f'Frame {i}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                out_test_vid.write(frame)
            out_test_vid.release()
            logger.info(f"已创建测试视频: {test_video_path}")
        except Exception as e:
            logger.error(f"创建测试视频失败: {e}")
            sys.exit(1)


    # 确保测试配置文件存在
    if not test_config_path.exists():
        logger.warning(f"测试配置文件 {test_config_path} 不存在，将创建一个默认配置。")
        default_config_data = {
            "common_params": {
                "dt": 0.04, # 假设视频帧率为25fps，0.04s对应1帧间隔
                "roi_points": None # [[50,50], [250,50], [250,200], [50,200]] # 可选的ROI
            },
            "piv_params": {
                "window_size": 32,
                "overlap": 16,
                "search_area_size": 64,
                "sig2noise_threshold": 1.05,
                "correlation_method": "circular", # 或 "linear"
                "outlier_method": "localmean", # 或 "median", "universal"
                "outlier_max_iter": 3,
                "outlier_kernel_size": 3
            },
            "analysis_params": {
                "save_individual_pair_results": True
            }
        }
        try:
            test_config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(test_config_path, 'w') as f_cfg:
                json.dump(default_config_data, f_cfg, indent=4)
            logger.info(f"已创建默认测试配置文件: {test_config_path}")
        except Exception as e_cfg:
            logger.error(f"创建默认测试配置文件失败: {e_cfg}")
            sys.exit(1)
            
    # 定义一个示例ROI（可选）
    # test_roi_points = np.array([[50, 50], [280, 50], [280, 200], [50, 200]])
    test_roi_points = None # 不使用覆盖ROI，测试配置文件中的ROI（如果有）

    if not test_video_path.exists():
        logger.error(f"测试视频文件不存在: {test_video_path}")
        sys.exit(1)
    if not test_config_path.exists():
        logger.error(f"测试配置文件不存在: {test_config_path}")
        sys.exit(1)

    logger.info(f"将使用视频: {test_video_path}")
    logger.info(f"将使用配置: {test_config_path}")
    logger.info(f"输出将保存到: {test_output_dir_base}")
    if test_roi_points is not None:
        logger.info(f"将使用覆盖ROI点: {test_roi_points.tolist()}")

    # 执行分析
    min_speed, avg_speed, max_speed, num_pairs = analyze_video_segment_with_openpiv(
        video_path=str(test_video_path),
        config_path=str(test_config_path),
        pixel_to_meter=0.001,  # 为测试提供一个 pixel_to_meter 值
        roi_override_points=test_roi_points
    )

    if num_pairs > 0:
        logger.info("===== 视频片段分析完成 =====")
        logger.info(f"  成功分析的帧对数: {num_pairs}")
        logger.info(f"  片段的最小速度 (均值): {min_speed:.4f} m/s")
        logger.info(f"  片段的平均速度 (均值): {avg_speed:.4f} m/s")
        logger.info(f"  片段的最大速度 (均值): {max_speed:.4f} m/s")
        logger.info(f"  详细结果和汇总统计已保存到 '{test_output_dir_base}/{Path(test_video_path).stem}_piv_results' 目录中。")
    else:
        logger.error("视频片段分析未能成功处理任何帧对。")

    logger.info("openpiv_analyzer.py 独立运行结束。") 