#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频裁剪工具测试脚本
此脚本演示如何使用 src/utils/video_trimmer.py 中的视频裁剪功能
"""

import os
from src.utils.video_trimmer import trim_video
import numpy as np
# ===== 配置区域 =====
# 请在此处修改配置参数

# 输入视频文件路径
INPUT_VIDEO = 'data/video/ch01_20250420085900.mp4'  # 替换为实际视频路径

# 裁剪时间范围
START_TIME = '1:40'  # 开始时间
END_TIME = '1:56'    # 结束时间

# 是否添加网格和ROI多边形
ADD_VISUAL_ELEMENTS = False  # True: 添加网格和ROI, False: 不添加

# 自定义ROI点 (如果不需要自定义，保持为None)
CUSTOM_ROI = np.array([[550,150],[1000,150],[1250,250],[1250,500],[1100,500],[900,350],[800,350]], np.int32)

# 输出目录
OUTPUT_DIR = 'data/output'

# ===== 主程序 =====

def trim_video_with_settings():
    """使用配置的参数裁剪视频"""
    print("===== 视频裁剪工具 =====")
    
    # 检查示例视频是否存在
    if not os.path.exists(INPUT_VIDEO):
        print(f"错误: 视频文件 {INPUT_VIDEO} 不存在")
        print("请修改脚本顶部的 INPUT_VIDEO 变量指向正确的视频文件")
        return
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    print(f"视频裁剪配置:")
    print(f"- 输入视频: {INPUT_VIDEO}")
    print(f"- 裁剪范围: {START_TIME} 到 {END_TIME}")
    print(f"- {'添加' if ADD_VISUAL_ELEMENTS else '不添加'}网格和ROI多边形")
    print(f"- 输出目录: {OUTPUT_DIR}")
    
    # 调用视频裁剪函数
    print("\n开始裁剪视频...")
    output_video = trim_video(
        INPUT_VIDEO, 
        START_TIME, 
        END_TIME, 
        roi_points=CUSTOM_ROI, 
        output_dir=OUTPUT_DIR, 
        add_visual_elements=ADD_VISUAL_ELEMENTS
    )
    
    if output_video:
        print(f"\n视频裁剪成功，输出文件: {output_video}")

if __name__ == "__main__":
    print("视频裁剪工具测试脚本")
    print("说明: 本工具可以裁剪视频，可选是否添加网格和ROI多边形")
    print("提示: 要修改裁剪选项，请直接编辑脚本顶部的配置区域")
    
    # 使用配置的参数裁剪视频
    trim_video_with_settings() 