#!/usr/bin/env python
import json
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

# 导入OpenPIV相关库
try:
    from openpiv import tools, pyprocess, validation, filters, scaling
except ImportError as e:
    print(f"导入OpenPIV库时出错: {e}", file=sys.stderr)
    print("请确保OpenPIV库已正确安装，可使用pip install openpiv命令安装", file=sys.stderr)
    sys.exit(1)
import sys
import os

from src.utils.logging_utils import setup_logger
from src.utils.piv_preprocessor import prepare_frames_for_piv

# 设置日志
logger = setup_logger("piv_openpiv_simple")

def analyze_video_piv_simple(video_path, config_path, output_dir=None):
    """
    对视频文件进行简化版PIV流速分析 (直接使用OpenPIV核心函数)

    Args:
        video_path: 视频文件路径
        config_path: PIV参数配置文件路径
        output_dir: 输出目录，默认为data/output_piv

    Returns:
        dict: 包含PIV分析结果的字典
    """
    # 加载配置
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        logger.info(f"已加载配置文件: {config_path}")
    except Exception as e:
        logger.error(f"加载配置文件时出错: {e}")
        return None

    # 提取参数
    common_params = config.get("common_params", {})
    openpiv_params = config.get("openpiv_params", {})

    dt = common_params.get("dt", 0.02)
    scaling_factor = common_params.get("scaling_factor", 1.0) # 单位: 米/像素
    roi_points_list = common_params.get("roi_points", None)
    # 确保 roi_points 是一个 NumPy 数组，即使它是 None 或空的
    if roi_points_list is not None and len(roi_points_list) > 0:
        roi_points = np.array(roi_points_list)
    else:
        roi_points = None


    winsize = openpiv_params.get("window_size", 32)
    overlap = openpiv_params.get("overlap", 16)
    searchsize = openpiv_params.get("search_area_size", 64)
    sig2noise_threshold = openpiv_params.get("sig2noise_threshold", 1.05)
    correlation_method = openpiv_params.get("correlation_method", "circular")
    outlier_method = openpiv_params.get("outlier_method", "localmean")
    outlier_max_iter = openpiv_params.get("outlier_max_iter", 3)
    outlier_kernel_size = openpiv_params.get("outlier_kernel_size", 3)


    # 设置输出目录
    video_name = Path(video_path).stem
    if output_dir is None:
        output_dir = Path("data/output_piv") / video_name
    else:
        output_dir = Path(output_dir) / video_name

    output_dir.mkdir(parents=True, exist_ok=True)

    # 设置图像处理目录
    image_dir = Path("data/image") / video_name
    image_dir.mkdir(parents=True, exist_ok=True)

    try:
        # 1. 处理视频帧
        logger.info(f"开始处理视频: {video_path}")
        frame_a_path, frame_b_path, fps = prepare_frames_for_piv(
            video_path,
            config,
            image_dir=image_dir,
            logger=logger
        )

        if frame_a_path is None or frame_b_path is None:
            logger.error("视频帧处理失败")
            return None

        logger.info(f"视频帧处理完成, 帧率: {fps}. 图像保存至: {image_dir}")

        # 2. 加载图像帧
        frame_a = tools.imread(frame_a_path)
        frame_b = tools.imread(frame_b_path)
        logger.info(f"图像尺寸: {frame_a.shape}")

        # -- 开始修改: ROI 处理 --
        offset_x, offset_y = 0, 0
        frame_a_processed = frame_a
        frame_b_processed = frame_b
        roi_applied_successfully = False # 标志位，指示ROI是否成功应用

        if roi_points is not None and roi_points.ndim == 2 and roi_points.shape[0] >= 3 and roi_points.shape[1] == 2:
            try:
                # 计算 ROI 边界框 (确保整数索引并在图像范围内)
                ymin = max(0, int(np.min(roi_points[:, 1])))
                ymax = min(frame_a.shape[0], int(np.max(roi_points[:, 1])))
                xmin = max(0, int(np.min(roi_points[:, 0])))
                xmax = min(frame_a.shape[1], int(np.max(roi_points[:, 0])))

                if ymax > ymin and xmax > xmin:
                    # 裁剪图像
                    frame_a_processed = frame_a[ymin:ymax, xmin:xmax]
                    frame_b_processed = frame_b[ymin:ymax, xmin:xmax]
                    # 记录偏移量
                    offset_y, offset_x = ymin, xmin
                    logger.info(f"已应用ROI裁剪，处理尺寸: {frame_a_processed.shape}, 偏移量: (x={offset_x}, y={offset_y})")
                    roi_applied_successfully = True
                else:
                    logger.warning("ROI坐标无效或导致裁剪后尺寸为零，将使用完整图像进行分析。")
                    # roi_points 保持不变，让后续绘图逻辑可以绘制（可能无效的）ROI框
            except Exception as crop_error:
                 logger.error(f"处理ROI坐标或裁剪图像时出错: {crop_error}，将使用完整图像进行分析。")
                 frame_a_processed = frame_a # 确保使用原始帧
                 frame_b_processed = frame_b
                 offset_x, offset_y = 0, 0 # 重置偏移量
        else:
             if roi_points is not None: # 如果 roi_points 存在但不符合要求
                 logger.warning("提供的ROI点数少于3个、格式不正确或为空，将使用完整图像进行分析。")
             else: # 如果 roi_points 本身就是 None
                 logger.info("未提供ROI，将使用完整图像进行分析。")
        # -- 结束修改: ROI 处理 --

        # 3. 执行PIV分析 (使用处理后的帧)
        logger.info(f"执行PIV分析，窗口大小={winsize}, 重叠={overlap}, 搜索区域={searchsize}")
        u0, v0, sig2noise = pyprocess.extended_search_area_piv(
            frame_a_processed.astype(np.int32),
            frame_b_processed.astype(np.int32),
            window_size=winsize,
            overlap=overlap,
            dt=dt, # dt 在这里通常表示帧间时间间隔，单位秒
            search_area_size=searchsize,
            sig2noise_method='peak2peak',
            correlation_method=correlation_method
        )
        # 使用 np.nanmax/nanmin 处理可能全为 NaN 的情况
        # u0_max_val = np.nanmax(u0) if not np.all(np.isnan(u0)) else np.nan
        # v0_max_val = np.nanmax(v0) if not np.all(np.isnan(v0)) else np.nan
        # u0_min_val = np.nanmin(u0) if not np.all(np.isnan(u0)) else np.nan
        # v0_min_val = np.nanmin(v0) if not np.all(np.isnan(v0)) else np.nan
        # logger.info(f"原始PIV结果 (u, v) in pixels/dt - max u0: {u0_max_val}, max v0: {v0_max_val}")
        # logger.info(f"原始PIV结果 (u, v) in pixels/dt - min u0: {u0_min_val}, min v0: {v0_min_val}")


        # 4. 获取坐标 (使用处理后的帧的尺寸)
        x_relative, y_relative = pyprocess.get_coordinates(
            image_size=frame_a_processed.shape,
            search_area_size=searchsize,
            overlap=overlap
        )

        # 将相对坐标调整回原始图像坐标系
        x = x_relative + offset_x
        y = y_relative + offset_y


        # 5. 验证和滤波
        mask = validation.sig2noise_val(sig2noise, threshold=sig2noise_threshold)
        u_filtered, v_filtered = filters.replace_outliers(
            u0, v0, mask,
            method=outlier_method,
            max_iter=outlier_max_iter,
            kernel_size=outlier_kernel_size
        )
        # 标记无效向量 (NaN) - 确保掩码应用正确
        u_filtered[mask] = np.nan
        v_filtered[mask] = np.nan


        # 6. 缩放: 像素/dt -> 物理单位 (米/秒)
        u_scaled = u_filtered * scaling_factor # 单位: 米/dt
        v_scaled = v_filtered * scaling_factor # 单位: 米/dt
        # u_scaled_max = np.nanmax(u_scaled) if not np.all(np.isnan(u_scaled)) else np.nan
        # v_scaled_max = np.nanmax(v_scaled) if not np.all(np.isnan(v_scaled)) else np.nan
        # u_scaled_min = np.nanmin(u_scaled) if not np.all(np.isnan(u_scaled)) else np.nan
        # v_scaled_min = np.nanmin(v_scaled) if not np.all(np.isnan(v_scaled)) else np.nan
        # logger.info(f"缩放后速度 (u, v) in m/s - max u: {u_scaled_max}, max v: {v_scaled_max}")
        # logger.info(f"缩放后速度 (u, v) in m/s - min u: {u_scaled_min}, min v: {v_scaled_min}")


        # 7. 保存结果到文本文件 (物理单位)
        result_txt_path = output_dir / f"{video_name}_piv_result.txt"
        try:
            count_saved_vectors = 0
            with open(str(result_txt_path), 'w') as f:
                f.write("x_pixel y_pixel u_mps v_mps mask\\n") # 修改表头
                for i in range(u_scaled.shape[0]):
                    for j in range(u_scaled.shape[1]):
                        if not np.isnan(u_scaled[i, j]) and not np.isnan(v_scaled[i, j]):
                            f.write(f"{x[i, j]} {y[i, j]} {u_scaled[i, j]} {v_scaled[i, j]} {int(mask[i, j])}\\n")
                            count_saved_vectors +=1
            logger.info(f"已保存 {count_saved_vectors} 个有效PIV结果数据(物理单位速度，像素坐标)到: {result_txt_path}")
            if count_saved_vectors == 0:
                logger.warning("没有有效的PIV结果被保存到文本文件。")
        except Exception as e:
             logger.error(f"保存PIV结果到文本文件时出错: {e}")
             result_txt_path = None # 标记保存失败


        # 8. 创建可视化 (使用物理单位的速度和像素坐标)
        result_img_path = output_dir / f"{video_name}_piv_result.png"
        try:
            fig, ax = plt.subplots(figsize=(10, int(10 * frame_a.shape[0]/frame_a.shape[1]) if frame_a.shape[1] > 0 else 8))
            plt.rcParams['font.family'] = 'DejaVu Sans' # 确保字体可用
            ax.set_title(f'PIV Vector Field ({video_name})', fontsize=14)
            ax.set_xlabel('X position (pixels)', fontsize=12)
            ax.set_ylabel('Y position (pixels)', fontsize=12)

            # 显示背景图像
            ax.imshow(frame_a, cmap='gray', alpha=0.8)

            # 计算速度大小 (物理单位)
            try:
                with np.errstate(invalid='ignore'): # 忽略 sqrt(nan) 等警告
                    speed_scaled = np.sqrt(u_scaled**2 + v_scaled**2)
                valid_speed_scaled = speed_scaled[~np.isnan(speed_scaled)] if not np.all(np.isnan(speed_scaled)) else np.array([])
            except FloatingPointError:
                logger.warning("计算速度大小时遇到浮点错误，可能所有速度都是NaN。")
                speed_scaled = np.full_like(u_scaled, np.nan)
                valid_speed_scaled = np.array([])


            # 过滤掉 NaN 值以进行绘图
            valid_plot_mask = ~np.isnan(u_scaled) & ~np.isnan(v_scaled)
            num_valid_plot_vectors = np.sum(valid_plot_mask)
            logger.info(f"找到 {num_valid_plot_vectors} 个有效向量用于绘图。")


            if num_valid_plot_vectors > 0:
                x_valid = x[valid_plot_mask]
                y_valid = y[valid_plot_mask]
                u_valid_scaled = u_scaled[valid_plot_mask]
                v_valid_scaled = v_scaled[valid_plot_mask]
                speed_valid_for_color = speed_scaled[valid_plot_mask]


                 # 自动调整步长避免拥挤
                step = max(1, int(np.sqrt(num_valid_plot_vectors) // 25)) if num_valid_plot_vectors > 0 else 1
                logger.info(f"向量显示步长: {step}")

                 # 自动调整缩放比例
                median_speed = np.median(valid_speed_scaled) if len(valid_speed_scaled) > 0 else 0.0
                # 调整scale，值越小，箭头越长
                scale_value = max(1.0, median_speed * 50.0) if median_speed > 1e-6 else 50.0 # 避免 median_speed 为 0 或极小
                logger.info(f"Quiver scale 因子: {scale_value}")


                Q = ax.quiver(x_valid[::step],
                             y_valid[::step],
                             u_valid_scaled[::step],
                             v_valid_scaled[::step],
                             speed_valid_for_color[::step], # 颜色基于物理速度
                             scale=scale_value,
                             scale_units='xy',
                             width=0.003, # matplotlib 通常能处理好相对宽度
                             cmap='jet',
                             angles='xy')

                try:
                    cbar = plt.colorbar(Q, ax=ax, label='Velocity (m/s)', shrink=0.8)
                    cbar.ax.tick_params(labelsize=10)
                except Exception as cbar_err:
                    logger.warning(f"无法创建 colorbar: {cbar_err}. 可能所有速度值都是NaN。")


                # 添加参考向量 (使用物理速度)
                if len(valid_speed_scaled) > 0:
                    ref_vel = np.percentile(valid_speed_scaled, 90) # 使用90分位数作为参考
                    if ref_vel > 1e-9: # 避免显示几乎为零的参考向量
                        ref_vel_display = float(f"{ref_vel:.2g}") # 格式化显示
                        ax.quiverkey(Q, 0.85, 0.05, ref_vel_display, f'{ref_vel_display} m/s',
                                     labelpos='E', coordinates='axes', color='black', fontproperties={'size': 10})
                    else:
                        logger.info("参考速度接近于零或为NaN，不显示参考向量。")
            else:
                logger.warning("没有有效的速度向量可以绘制。")


            # 绘制ROI
            if roi_points is not None and roi_points.ndim == 2 and roi_points.shape[0] >= 3 and roi_points.shape[1] == 2:
                roi_polygon = plt.Polygon(roi_points, fill=False, edgecolor='r', linewidth=1.5)
                ax.add_patch(roi_polygon)
            elif roi_points is not None: # 如果 roi_points 存在但不符合要求
                 logger.warning("ROI 点数少于3个或格式不正确，无法绘制ROI多边形。")

            # 添加统计信息
            if len(valid_speed_scaled) > 0:
                stats_text = (f"Mean: {np.mean(valid_speed_scaled):.3f} m/s | "
                              f"Max: {np.max(valid_speed_scaled):.3f} m/s | "
                              f"Valid vectors: {num_valid_plot_vectors}")
                plt.figtext(0.5, 0.01, stats_text, ha='center', fontsize=10, wrap=True)
            else:
                plt.figtext(0.5, 0.01, "No valid vectors for statistics", ha='center', fontsize=10, wrap=True)

            ax.set_aspect('equal', adjustable='box')
            ax.set_xlim(0, frame_a.shape[1])
            ax.set_ylim(frame_a.shape[0], 0) # Y轴反转以匹配图像坐标

            plt.tight_layout(pad=1.5) # 调整布局，避免元素重叠
            plt.savefig(str(result_img_path), dpi=150)
            plt.close(fig) # 关闭图形，释放内存
            logger.info(f"已保存PIV结果图像到: {result_img_path}")

        except Exception as e:
            logger.error(f"创建PIV可视化时出错: {e}")
            import traceback
            traceback.print_exc()
            result_img_path = None # 标记可视化失败


        # 9. 整理并返回结果
        results = {
            "config": config,
            "frame_a_path": str(frame_a_path), # 确保路径是字符串
            "frame_b_path": str(frame_b_path), # 确保路径是字符串
            "fps": fps,
            "output_dir": str(output_dir),
            "result_txt_path": str(result_txt_path) if result_txt_path else None,
            "result_img_path": str(result_img_path) if result_img_path else None,
            "roi_applied": roi_applied_successfully,
        }

        # 计算统计信息
        if len(valid_speed_scaled) > 0:
            results["avg_speed_mps"] = float(np.mean(valid_speed_scaled))
            results["max_speed_mps"] = float(np.max(valid_speed_scaled))
            results["min_speed_mps"] = float(np.min(valid_speed_scaled))
            logger.info(f"平均速度: {results['avg_speed_mps']:.4f} 米/秒")
            logger.info(f"最大速度: {results['max_speed_mps']:.4f} 米/秒")
            logger.info(f"最小速度: {results['min_speed_mps']:.4f} 米/秒")
        else:
            logger.warning("没有有效的速度向量计算统计信息。")
            results["avg_speed_mps"] = 0.0
            results["max_speed_mps"] = 0.0
            results["min_speed_mps"] = 0.0


        logger.info("PIV分析完成")
        return results

    except Exception as e:
        logger.error(f"PIV处理过程中发生未预料的错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 默认参数，确保文件存在或修改为实际路径
    video_path = "data/video/ch01_20250414145900.mp4"
    config_path = "config/piv_config.json"

    logger.info(f"处理视频: {video_path}")
    logger.info(f"使用配置: {config_path}")

    # 执行PIV分析
    results = analyze_video_piv_simple(str(video_path), str(config_path))

    if results:
        logger.info("PIV分析成功完成。")
        if results.get("result_txt_path"):
            logger.info(f"结果数据文件: {results['result_txt_path']}")
        if results.get("result_img_path"):
            logger.info(f"结果图像文件: {results['result_img_path']}")
    else:
        logger.error("PIV分析失败。") 