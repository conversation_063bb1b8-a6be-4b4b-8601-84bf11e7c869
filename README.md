# 视频流速计算

## 需求描述
核心需求是使用水站外部的摄像头获取的录像来进行流速计算，从而替代传统的流速仪，降低成本，获取更多的流速数据。

### 核心问题
1. 流速识别的时间步长是怎么样的？以及能接受的时延是多少？
2. 流速识别的精度是多少？
3. 硬件条件是什么样的？或者说可以接受的预算成本是多少？
4. 流速识别要适配哪些场景？晴天？雨天？大风？雾天？夜晚？

## 项目介绍

### 项目目的
项目的核心目的就是实现多种算法的流速视觉计算。其次是比较多种算法，了解各种算法的优缺点和适用场景。然后则是在实际的生产环境中，使用算法进行流速的检测。（这里不一定是只用一种算法，可能是使用多种算法同时进行计算）

### 项目结构
```
├── config
├── data: 数据相关
├── lib: 库相关
│   └── pypiv:PIV算法库
├── PIV: 光流法相关
├── src: 项目核心代码
│   ├── analysis_algorithms: 具体算法实现的代码，有的是调用库，有的是自己实现
│   │   ├── opencv_otv_analyzer.py: opencv实现的otv算法，可以单独执行，作为算法开发时测试用；也可以调用使用
│   │   └── opencv_piv_analyzer.py: pypiv库实现的piv算法，可以单独执行，作为算法开发时测试用；也可以调用使用
│   └── utils:工具函数
│       ├── adjust_config.py: 调整配置文件
│       ├── logging_utils.py: 日志相关
│       ├── piv_preprocessor.py: piv预处理
│       ├── video_trimmer.py: 视频裁剪
│       └── visualize_setup.py: 可视化设置
├── batch_process_videos.py: 批量处理视频
├── requirements.txt: 项目依赖
├── README.md: 项目说明
├── LICENSE: 项目许可证
├── .gitignore: 忽略文件
```

# 开源库介绍

## awive

### config.py

- 配置文件，用于配置视频流速计算的参数

#### 1. dataset 部分
这部分控制数据加载相关的参数： config.py:209-232

 - image_dataset_dp: 图像数据集目录路径（如果使用图像序列而非视频）。(可选, 与 video_fp 二选一必需)
 - image_suffix: 图像文件后缀。(可选, 默认: "jpg")
 - image_number_offset: 图像编号的起始偏移量。(可选, 默认: 0)
 - image_path_prefix: 图像文件名前缀。(可选, 默认: "")
 - image_path_digits: 图像文件名中数字的位数。(可选, 默认: 4)
 - video_fp: 视频文件路径。(可选, 与 image_dataset_dp 二选一必需)
 - gcp: 地面控制点配置，用于图像校正。(必需)
   - apply: 是否应用地面控制点校正。(必需)
   - pixels: 至少四个像素坐标，格式为[[x1,y1], ..., [x4,y4]]。(必需, 如果 apply=True)
   - meters: 至少四个米坐标，格式为[[x1,y1], ..., [x4,y4]]。(可选, 与 distances 二选一必需, 如果 apply=True)
   - distances: GCP之间的距离（米），键为字符串表示的元组（如"(0,1)"）。(可选, 与 meters 二选一必需, 如果 apply=True)。这里的硬编码似乎只能处理4个点和6对距离
   - ground_truth: 地面真实数据，包含位置和速度信息。(可选)

#### 2. preprocessing 部分
这部分控制图像预处理相关的参数： config.py:182-206

 - rotate_image: 图像旋转角度（度）。(可选, 默认: 0)
 - pre_roi: 预裁剪区域，格式为((x1,y1), (x2,y2))。(必需)
 - roi: 最终感兴趣区域，格式为((x1,y1), (x2,y2))。(必需)
 - image_correction: 图像校正参数，包含镜头畸变校正参数。(必需)
   - apply: 是否应用图像校正。(必需)
   - k1: 径向畸变系数。(必需, 如果 apply=True)
   - c: 切向畸变系数。(必需, 如果 apply=True)
   - f: 焦距。(必需, 如果 apply=True)
 - ppm: 像素/米，表示空间分辨率。(必需, 如果需要输出米/秒单位的速度)
 - resolution: 处理分辨率比例。(可选, 默认: 1.0)

#### 3. otv 部分（Optical Tracking Velocimetry）
这部分控制OTV算法的参数： config.py:222-284

 - mask_path: 掩码图像路径。(可选, 默认: None)
 - partial_min_angle/partial_max_angle: 临时特征验证的角度约束（度）。(可选, 默认: 135/225)
 - final_min_angle/final_max_angle: 最终轨迹验证的角度约束（度）。(可选, 默认: 160/200)
 - final_min_distance: 有效轨迹的最小位移（像素）。(可选, 默认: 8)
 - max_features: 帧间跟踪的最大特征数量。(可选, 默认: 7000)
 - region_step: 区域步长（目前未使用）。(可选, 默认: 240)
 - lines_width: 提取速度向量的线宽。(必需)
 - features: 特征检测参数。(可选, 有默认配置)
   - maxcorner: 最大角点数。(可选, 默认: 300)
   - qualitylevel: 特征质量阈值。(可选, 默认: 0.2)
   - mindistance: 最小距离。(可选, 默认: 2)
   - blocksize: 块大小。(可选, 默认: 2)
 - lk: Lucas-Kanade光流法参数。(可选, 有默认配置)
   - winsize: 窗口大小。(可选, 默认: 15)
   - max_level: 最大金字塔层级。(可选, 默认: 4)
   - max_count: 最大迭代次数。(可选, 默认: 20)
   - epsilon: 收敛阈值。(可选, 默认: 0.03)
   - flags: 标志位。(可选, 默认: 0)
   - radius: 半径。(可选, 默认: 7)
   - min_eigen_threshold: 最小特征值阈值。(可选, 默认: 0.01)

#### 4. stiv 部分（Space-Time Image Velocimetry）
这部分控制STIV算法的参数： config.py:287-297 (整个 stiv 部分是可选的)

 - window_shape: 分析窗口大小。(可选, 默认: (51, 51))
 - filter_window: 滤波窗口大小。(必需, 如果 stiv 部分存在)
 - overlap: 窗口重叠度。(可选, 默认: 31)
 - ksize: 核大小。(可选, 默认: 7)
 - polar_filter_width: 极坐标滤波宽度。(必需, 如果 stiv 部分存在)
 - lines_range: 每条线的分析像素范围列表。(必需, 如果 stiv 部分存在)

#### 5. water_level 部分
这部分控制水位检测的参数： config.py:300-306 (整个 water_level 部分是可选的)

 - buffer_length: 缓冲区长度。(必需, 如果 water_level 部分存在)
 - roi: 水位检测的感兴趣区域。(必需, 如果 water_level 部分存在)
 - roi2: 水位检测的第二个感兴趣区域。(必需, 如果 water_level 部分存在)
 - kernel_size: 形态学操作的核大小。(必需, 如果 water_level 部分存在)

#### 6. water_flow 部分
这部分控制水流相关的参数： config.py:321-327

 - area: 流域面积（平方米）。(必需, 如果需要计算流量)

#### 7. lines 参数
这部分控制流速计算的线高度列表： config.py:338-341

 - lines: 提取速度向量的线高度列表。(必需)

### 坐标相关配置的注意事项

为了确保 `awive` 程序能够正确处理图像和视频，并避免因坐标配置不当导致的错误，请注意以下参数之间的关系和约束：

1.  **图像尺寸基准**: 所有的坐标参数（如 `pre_roi`, `roi`, `gcp.pixels`, `water_level.roi`, `water_level.roi2`, `lines` 中的 Y 坐标）最终都必须落在经过处理后的图像边界内。处理步骤包括：
    *   **旋转** (`preprocessing.rotate_image`): 旋转会改变图像的有效边界和坐标系。
    *   **缩放** (`preprocessing.resolution`): 缩放会改变图像的整体尺寸。
    *   **初始裁剪** (`preprocessing.pre_roi`): 定义了后续处理的基础区域。

2.  **ROI 嵌套关系**:
    *   最终感兴趣区域 `preprocessing.roi` 必须完全包含在初始裁剪区域 `preprocessing.pre_roi` 定义的矩形内部。如果 `pre_roi` 覆盖了整个图像，则 `roi` 只需在整个图像范围内即可。
    *   坐标格式为 `((x1, y1), (x2, y2))`，其中 `(x1, y1)` 是左上角坐标，`(x2, y2)` 是右下角坐标。必须满足 `x1 < x2` 且 `y1 < y2`。

3.  **GCP 配置**:
    *   `dataset.gcp.pixels` (像素坐标列表) 和 `dataset.gcp.meters` (米坐标列表) 必须包含相同数量的点，且至少需要 4 个点才能进行有效的透视变换。
    *   `gcp.pixels` 中定义的像素坐标必须位于**原始**图像的边界内。
    *   如果通过 `gcp.distances` 提供距离而非直接提供米坐标，请确保 `distances` 字典中包含所有必要的点对距离，其键的格式为 `"(i,j)"`（例如 `"(0,1)"`）。

4.  **掩码 (Mask)**:
    *   如果提供了 `otv.mask_path`，掩码图像的尺寸必须与经过**旋转 (`rotate_image`)、缩放 (`resolution`) 和初始裁剪 (`pre_roi`)** 后的图像尺寸完全一致。掩码用于指定哪些区域**不**参与特征跟踪。

5.  **水位检测 ROI**:
    *   `water_level.roi` 和 `water_level.roi2` 定义的区域必须位于**最终处理完成**的图像范围内（即经过旋转、缩放、`pre_roi` 和 `roi` 裁剪后的图像区域）。

6.  **流速计算线**:
    *   `lines.lines` 列表中定义的 Y 坐标（水平线的高度）必须介于最终处理图像的顶部和底部之间（`0 <= y < height`）。
    *   为了得到有意义的流速结果，这些线通常应设置在最终分析区域 `preprocessing.roi` 的垂直范围内。

**检查顺序建议**:

配置坐标时，建议按以下顺序考虑和检查：

1.  确定原始图像/视频尺寸。
2.  设置旋转 `rotate_image` 和缩放 `resolution`。计算旋转和缩放后的图像尺寸。
3.  根据旋转/缩放后的尺寸设置 `pre_roi`。
4.  在 `pre_roi` 定义的区域内设置 `roi`。
5.  在**原始**图像尺寸内设置 `gcp.pixels`。
6.  根据经过旋转、缩放、`pre_roi` 裁剪后的图像尺寸，准备或检查 `mask_path` 对应的掩码图像。
7.  根据最终处理图像（应用了所有旋转、缩放、裁剪）的尺寸和 `roi` 范围，设置 `water_level.roi`, `water_level.roi2`, 和 `lines.lines`。

### 配置示例 (针对 1920x1080 视频)

以下是一个简化的配置示例，展示了如何为 1920x1080 像素的视频设置一些关键的坐标相关参数：

```json
{
  "dataset": {
    "image_dataset_dp": null,
    "video_fp": "path/to/your/video.mp4", // 必需 (与 image_dataset_dp 二选一)
    "gcp": {                         // 必需
      "apply": false,               // 必需
      // 注意：即使 apply 为 false，根据模型定义，仍需提供以下字段的占位符
      "pixels": [],                 // 技术上必需 (至少4个, 但在 apply=false 时无意义)
      "meters": [],                 // 技术上必需 (或 distances, 在 apply=false 时无意义)
      "distances": null             // 技术上必需 (或 meters, 在 apply=false 时无意义)
    }
  },
  "preprocessing": {
    "pre_roi": [[100, 100], [1820, 980]], // 必需 (示例值)
    "roi": [[200, 200], [1720, 880]],     // 必需 (示例值)
    "image_correction": {            // 必需
      "apply": false,               // 必需
      // 注意：即使 apply 为 false，根据模型定义，仍需提供以下字段
      "k1": 0.0,                    // 技术上必需
      "c": 0,                       // 技术上必需
      "f": 0.0                      // 技术上必需
    },
    "ppm": 50,                       // 必需 (用于米/秒输出，示例值)
    "resolution": 1.0                // 可选，但包含在此处以明确
  },
  "otv": {                           // 必需 (因为它是默认算法之一)
    "lines_width": 5,                // 必需 (示例值)
    }
  },
  "stiv": {
    "window_shape": [51, 51],
    "filter_window": 64,
    "overlap": 0,
    "lines_range": [[700, 700], [750, 750], [800, 800]],
    "polar_filter_width": 15
    }
  "water_level": {
    "roi": [[800, 700], [1600, 800]],
    "roi2": [[800, 550], [950, 650]],
    "kernel_size": 5,
    "buffer_length": 10
  },
  "water_flow": {
    "area": 10.0                     // 必需 (示例值, 如果需要计算流量)
  },
  "lines": [300, 400, 500] // 必需 (示例值)
}
```

**说明**:

*   原始视频尺寸为 1920x1080。
*   `pre_roi` 设置为 `[[100, 100], [1820, 980]]`，表示裁剪掉图像四周各 100 像素的边缘区域。处理后的图像尺寸变为 (1820-100) x (980-100) = 1720x880。
*   `roi` 设置为 `[[200, 200], [1720, 880]]`，是在 `pre_roi` 裁剪后的基础上，进一步将分析区域的左上角向内移动 100 像素（相对于 `pre_roi` 的左上角，即原始图像的 (100+100, 100+100) = (200, 200)），右下角保持在 `pre_roi` 的右下角（相对于 `pre_roi` 的右下角，即原始图像的 (1820, 980)）。最终分析区域的尺寸为 (1720-200) x (880-200) = 1520x680，其坐标相对于**应用 pre_roi 之后**的图像是 `[[100, 100], [1620, 780]]`，而相对于**原始**图像是 `[[200, 200], [1720, 880]]`。
*   `lines` 的 Y 坐标 `[300, 400, 500, 600, 700]` 均落在最终 `roi` 的 Y 坐标范围 (200 到 880) 内。
*   这个示例中未启用旋转、分辨率缩放、GCP 和掩码，简化了坐标计算。如果启用这些功能，需要相应调整 `roi` 和 `lines` 的坐标。

## AdaptivePIV

AdaptivePIV 包提供了几种不同的 PIV 分析方法，主要的配置参数在 `PIV/analysis.py` 文件中的几个设置类里定义。

### 共有参数

以下是 AdaptivePIV 中不同设置类共有的参数：

- `init_WS`: 初始窗口尺寸。影响初始阶段的速度场计算精度和计算量。**建议值: 64 或 32 的倍数，如 64 或 32。需为奇数，如 65 或 33。范围 [5, 245]。**
- `final_WS`: 最终窗口尺寸。影响最终速度场的分辨率和精度。**建议值: 32 或 16 的倍数，如 32 或 16。需为奇数，如 33 或 17。范围 [5, 245]，且小于等于 `init_WS`。**
- `n_iter_main`: 主迭代次数。在此阶段窗口尺寸和间距会减小。增加此值通常能提高精度。**建议值: 3。范围 [1, 10]。**
- `n_iter_ref`: 细化迭代次数。在此阶段窗口尺寸和位置固定，进一步优化结果。增加此值能微调结果。**建议值: 1。范围 [0, 10]。**
- `vec_val`: 向量验证方法。用于去除异常或不准确的速度向量。**建议值: 'NMT' (Normalized Median Test)。选项: 'NMT', None。**
- `interp`: 插值方法。用于将稀疏的速度向量场插值为密集场。**建议值: 'struc_cub' (结构化三次插值) 或 'unstruc_cub' (非结构化三次插值，取决于具体类)。选项: 'struc_lin', 'struc_cub', 'unstruc_cub'。**
- `verbosity`: 详细程度级别。控制程序输出信息的多少。**建议值: 2 (BASIC)。选项: 1 (ESSENTIAL), 2 (BASIC), 3 (TERSE)。**

以下参数在 AdaptStructSettings 和 AdaptSettings 中共有：

- `part_detect`: 粒子检测方法。用于在图像中识别粒子或纹理特征。**建议值: 'simple'。选项: 'simple', 'local_thr'。**
- `sd_P_target`: 估计种子密度时，每个核的目标粒子数。影响自适应窗口尺寸的计算。**建议值: 20。**
- `target_init_NI`: 第一次迭代中，每个相关窗口的目标粒子数。用于计算初始自适应窗口大小。**建议值: 20。**
- `target_fin_NI`: 最后一次迭代中，每个相关窗口的目标粒子数。用于计算最终自适应窗口大小。**建议值: 8。**

### 各设置类特有参数

#### WidimSettings 特有参数

- `WOR`: 窗口重叠率。增加重叠率可以提高速度场的分辨率和精度，但会增加计算量。**建议值: 0.5。范围 [0, 1)。**

#### AdaptStructSettings 特有参数

- `init_spacing`: 初始采样点间距（像素）。**建议值: 根据图像分辨率和粒子密度设定，例如 32 或 16。如果为 'auto'，则自动计算。必须大于等于 2。**
- `final_spacing`: 最终采样点间距（像素）。决定最终速度场网格的密度。**建议值: 根据所需的空间分辨率设定，例如 16 或 8。如果为 'auto'，则自动计算。必须大于等于 2。**

#### AdaptSettings 特有参数

- `init_N_windows`: 第一次迭代中使用的初始窗口数。**建议值: 2500。**
- `final_N_windows`: `n_iter_main` 次迭代后分析中拥有的最终窗口数。**建议值: 10000。**
- `distribution_method`: 窗口分布方法。**建议值: 'AIS'。选项: 'AIS'。**
- `idw`: 是否使用反距离加权进行向量验证。**建议值: True。**

## pypiv

pypiv 包提供了直接 PIV 和自适应 PIV 方法。

### 共有参数

以下是 DirectPIV 和 AdaptivePIV 共有的初始化参数：

- `image_a`: 第一帧图像。输入 NumPy 数组。
- `image_b`: 第二帧图像。输入 NumPy 数组。
- `window_size`: 询问窗口大小。**建议值: 32。**
- `search_size`: 搜索窗口大小。**建议值: 32。**
- `distance`: 窗口间距。**建议值: 16。**

### 各类特有参数及函数参数

#### DirectPIV 特有函数参数

- `method` (用于 `correlate_frames` 函数): 峰值检测算法的方法。**建议值: 'gaussian'。选项: 'gaussian', '9point'。**

#### AdaptivePIV 特有初始化参数

- `piv_object`: DirectPIV 或 AdaptivePIV 对象。作为自适应分析的起始点。**建议值: 先使用 DirectPIV 得到初步结果，再将该 DirectPIV 对象传入。**
- `deformation`: 变形方法。影响图像如何根据初步的速度场进行变形。**建议值: 'forward'。选项: 'forward', 'central'。**
- `ipmethod`: 插值方法，传递给插值器。用于 deformed image 的插值。**建议值: 'bilinear'。**

## OpenPIV

OpenPIV 是一个广泛使用的开源 Python 库，用于进行粒子图像测速（PIV）。`piv_openpiv_simple_v2.py` 脚本展示了如何使用 OpenPIV 的核心功能进行 PIV 分析。主要的配置参数通常通过 JSON 文件传入该脚本。

### 通过配置 (config.json) 控制的参数

这些参数通常定义在脚本读取的 JSON 配置文件中，用于控制整个 PIV 处理流程。

#### common_params 部分

- `dt`: 图像帧之间的时间间隔（秒）。用于将像素位移除以物理速度。**建议值: 根据视频里的流速来定，最好不要太紧，确保有明显的流速。**
- `scaling_factor`: 空间缩放因子（米/像素）。用于将像素坐标和位移转换为物理单位。**建议值: 根据实际场景标定得到。**
- `roi_points`: 感兴趣区域的顶点坐标列表（像素）。用于在结果可视化中标记区域。**建议值: 根据需要分析的区域手动设定。格式: `[[x1, y1], [x2, y2], ..., [xn, yn]]`。**

#### openpiv_params 部分 (直接传递给 OpenPIV 函数)

- `window_size`: 互相关计算的询问窗口大小（像素）。影响计算速度和空间分辨率。**建议值: 32 或 64。通常为 2 的幂次方。**
- `overlap`: 询问窗口之间的重叠像素数。增加重叠可以提高速度场的分辨率。**建议值: `window_size / 2`，例如 16 或 32。**
- `search_area_size`: 搜索区域的大小（像素）。需要大于或等于 `window_size`。**建议值: 通常等于 `window_size` 或稍大，例如 32 或 64。在 `extended_search_area_piv` 中作为 `search_area_size` 参数。**
- `sig2noise_threshold`: 信噪比阈值。用于 `validation.sig2noise_val` 函数，过滤低质量的向量。**建议值: 1.0 到 1.3 之间。**
- `correlation_method`: 互相关计算方法。**建议值: 'circular' 或 'linear'。选项: 'circular', 'linear'。**
- `outlier_method`: 异常向量检测与替换方法。用于 `filters.replace_outliers` 函数。**建议值: 'localmean' 或 'median'。选项: 'localmean', 'median', 'vector_direction'。**
- `outlier_max_iter`: 异常值替换的最大迭代次数。用于 `filters.replace_outliers` 函数。**建议值: 3。**
- `outlier_kernel_size`: 局部异常值检测的核大小（像素）。用于 `filters.replace_outliers` 函数。**建议值: 3。**

### 核心 OpenPIV 函数参数 (由脚本调用)

- `pyprocess.extended_search_area_piv(frame_a, frame_b, window_size, overlap, dt, search_area_size, sig2noise_method, correlation_method)`:
    - `frame_a`, `frame_b`: 输入的图像帧 (NumPy 数组, 通常为 int32 类型)。
    - `window_size`: 询问窗口大小 (来自配置)。
    - `overlap`: 窗口重叠 (来自配置)。
    - `dt`: 时间间隔 (来自配置)。
    - `search_area_size`: 搜索区域大小 (来自配置)。
    - `sig2noise_method`: 计算信噪比的方法。**建议值: 'peak2peak'。**
    - `correlation_method`: 互相关方法 (来自配置)。
- `pyprocess.get_coordinates(image_size, search_area_size, overlap)`:
    - `image_size`: 图像尺寸 `(height, width)`。
    - `search_area_size`: 搜索区域大小 (来自配置)。
    - `overlap`: 窗口重叠 (来自配置)。
- `validation.sig2noise_val(sig2noise, threshold)`:
    - `sig2noise`: `pyprocess` 函数返回的信噪比矩阵。
    - `threshold`: 信噪比阈值 (来自配置)。
- `filters.replace_outliers(u, v, mask, method, max_iter, kernel_size)`:
    - `u`, `v`: 原始速度分量场。
    - `mask`: `validation` 函数返回的布尔掩码 (True 表示无效/异常值)。
    - `method`: 替换方法 (来自配置)。
    - `max_iter`: 最大迭代次数 (来自配置)。
    - `kernel_size`: 核大小 (来自配置)。
- `tools.imread(filepath)`: 读取图像文件。
- `tools.save(filepath, x, y, u, v, mask)`: 将 PIV 结果保存到文本文件 (注意: 保存的是像素单位的速度)。


# 项目简单使用方法介绍

## 1. 使用`batch_process_videos.py`脚本使用piv或者otv算法进行批量处理

### 1.1 参数配置
1. 将要处理的视频上传到`data/video`目录下，然后修改`config/batch_config.yaml`文件中的`video_dir`参数为视频的相对路径。
2. 将`config/batch_config.yaml`文件中的`analysis_method`参数设置为`otv`，然后将`otv_params`中的`mode`参数设置为`pre`，表示使用预处理模式，然后设置`input_video_path`参数为待处理的视频路径。并确保`add_visual_elements_to_trimmed_video`参数为`True`，表示在处理过程中添加可视化元素。
3. 运行`src/analysis_algorithms/opencv_otv_analyzer.py`脚本，开始处理。这时候会得到一个`data/output/pre_flow.mp4`的视频，下载这个视频并进行查看
4. 这个时候基于上面的视频，核心要调整3个参数：
    - `config/batch_config.yaml`文件中的`roi_points`参数，这个参数用于设置流速计算的区域，需要根据网格记录相应的区域端点，格式是`[x,y]`。并注释好是哪一个站的，也不要删除之前站的配置，注释掉，写一个新的就好。
    - `config/batch_config.yaml`文件中的`expected_flow_direction`参数，这个参数用于设置流速计算的方向，用来过滤流速方向偏差太大的数据，这个方向是通过视频观察出来的。当前是水平向右的是0度，竖直向上的是270度。
    - `config/batch_config.yaml`文件中的`pixel_to_meter`参数，这个参数用于设置像素和米之间的转换关系，需要根据实际的网格大小来设置。当前方式是在视频范围内找一个参照物，然后在视频内找到横向和纵向的像素长度，然后计算出该物体的实际像素长度，再结合运维人员提供的实际长度计算出这个参数。$实际长度（米）/实际像素长度（像素）$ 。
5. 调整好上面的参数后，进一步配置`config/piv_config.json`文件里的`input_videos`和`time_ranges`参数，`input_videos`参数用于设置待处理的视频路径，`time_ranges`参数用于设置待处理的视频片段，格式是`[start_time, end_time]`，单位是秒。

### 1.2 运行
1. 如果是要使用openpiv，则在`config/batch_config.yaml`文件中将`analysis_method`参数设置为`openpiv`，然后运行`batch_process_videos.py`脚本，开始处理。
2. 裁剪的视频可以在目录`data/output/trimmed`下找到，OTV分析的结果可以在目录`data/output/otv`下找到。具体流速数据则是在`data/output/otv/flow_speed_results.csv`文件中。
3. 如果是要使用otv，则在`config/batch_config.yaml`文件中将`analysis_method`参数设置为`otv`，然后运行`batch_process_videos.py`脚本，开始处理。结果在`data/output/otv`目录下。具体流速数据则是在`data/output/otv/flow_speed_results.csv`文件中。

## 2. otv算法介绍

### 算法概述
OTV (Optical Track Velocimetry) 是基于OpenCV光流法的流速测量算法，通过跟踪视频中的特征点运动来计算流体速度。该算法结合了Lucas-Kanade光流跟踪、方向集中度分析和多层次数据过滤技术。

### 核心处理流程

#### 2.1 特征点检测与初始化
- **特征点检测**：使用`cv2.goodFeaturesToTrack`在ROI区域内检测角点特征
- **静态区域检测**：通过多帧差分识别文字、标识等静态区域
- **初始过滤**：移除网格交点、文字区域和静态区域的干扰特征点
- **参数控制**：支持`maxCorners`、`qualityLevel`、`minDistance`等检测参数调节

#### 2.2 光流跟踪与运动分析
- **Lucas-Kanade光流**：使用金字塔LK光流法跟踪特征点在连续帧间的运动
- **运动角度计算**：计算每个特征点的移动方向角度(0-360°)
- **方向集中度分析**：
  - 收集足够样本后(≥50个角度)进行方向统计
  - 使用滑动窗口找到最集中的方向区域
  - 确定主导流动方向及其集中度范围(默认±45°)
- **方向过滤**：仅保留符合主导方向的特征点参与流速计算

#### 2.3 数据收集与存储
算法在处理过程中收集三类数据：
- **实时流速数据**：每帧的瞬时流速，用于实时显示和滑动窗口统计
- **帧级数据**：每帧的统计信息（位移、有效点数、流速等）
- **特征点级数据**：每个特征点在每帧的详细信息（坐标、位移、有效性等）

#### 2.4 多层次数据过滤机制

数据过滤分为两个阶段：**实时过滤阶段**和**后处理过滤阶段**，确保结果的可靠性：

##### 实时过滤阶段

###### 第一层：初始特征点过滤
- **时机**：特征点检测后立即执行
- **方法**：`filter_grid_and_text_features`函数
- **目标**：移除网格交点、文字区域和静态区域的干扰特征点
- **作用范围**：影响后续所有计算
- **实现方式**：传统循环处理，实时过滤

##### 后处理过滤阶段

收集完所有时间帧的数据后，使用pandas/numpy等高效计算库进行批量处理：

###### 第二层：方向过滤
- **时机**：数据收集完成后统一执行
- **方法**：基于完整数据集进行方向集中度分析
- **目标**：识别主导流动方向，过滤方向不一致的特征点
- **分析步骤**：
  - 收集所有特征点的移动角度数据
  - 使用直方图和滑动窗口找到最集中的方向区域
  - 确定主导流动方向及其集中度范围(默认±45°)
  - 标记符合方向要求的数据点
- **实现方式**：pandas DataFrame批量角度计算和过滤

###### 第三层：轨迹静态点过滤
- **时机**：方向过滤完成后执行
- **方法**：`filter_static_trajectory_points`函数，基于numpy数组计算
- **目标**：移除总位移小于阈值的静态点
- **判断标准**：起始位置到最终位置的总位移 ≥ 阈值(默认10像素)
- **实现方式**：numpy向量化计算总位移

###### 第四层：四分位法异常值过滤
- **时机**：轨迹静态点过滤后执行
- **方法**：基于pandas的统计函数进行四分位数计算
- **目标**：去除位移数据中的异常值
- **判断标准**：[Q1-1.5×IQR, Q3+1.5×IQR]范围内的数据
- **启用条件**：有效点数 > 阈值(默认20个点)
- **实现方式**：pandas.quantile()和布尔索引

###### 第五层：时间窗口动态过滤
- **时机**：四分位法过滤后执行，作为最终过滤步骤
- **方法**：基于pandas时间序列分析
- **目标**：识别特征点的活跃时间范围，排除运动衰减后的数据
- **分析方法**：
  - 使用pandas.rolling()计算滑动时间窗口(默认1秒)的平均位移
  - 检测运动衰减：窗口平均位移降至初始值的阈值比例(默认10%)以下
  - 确定每个特征点的有效运动时间段
  - 应用双重过滤条件：方向有效 AND 在活跃时间范围内
- **实现方式**：pandas groupby + rolling + 布尔索引

#### 2.5 流速计算与统计

##### 瞬时流速计算
```
瞬时流速 = (平均位移距离 × 像素转换系数) / 时间间隔
其中：
- 平均位移距离：当前帧所有有效特征点的位移均值
- 像素转换系数：pixel_to_meter参数
- 时间间隔：相邻帧之间的实际时间差
```

##### 多种统计指标
基于不同过滤层次提供多种统计结果：

1. **全局统计**：基于所有帧的最小、平均、最大流速
2. **滑动窗口统计**：最近N帧(默认20帧)的平均流速，用于实时显示
3. **四分位过滤统计**：去除异常值后的流速统计
4. **活跃时间范围统计**：基于时间窗口动态过滤的最终结果
   - 只计算同时满足"方向有效"和"在活跃时间范围内"的数据点
   - 使用DataFrame按时间分组计算每帧平均位移
   - 提供最准确和可靠的流速测量结果

#### 2.6 用户交互与质量控制

##### 特征点检查模式
- 生成前半段视频，显示特征点编号
- 支持用户选择特定特征点用于后半段计算
- 提供可视化反馈，提高结果可信度

##### 自适应特征点管理
- 监控特征点数量，低于阈值时自动重新检测
- 保持特征点ID的连续性，便于轨迹分析
- 动态调整跟踪参数，适应不同场景

#### 2.7 结果输出与可视化

##### 数据导出
- **结果文件**：包含各层过滤统计指标的文本报告
- **CSV数据**：详细的特征点轨迹数据，包含时间、坐标、位移、有效性、活跃状态等信息
- **可视化图表**：特征点位移随时间变化的趋势图

##### 视频输出
- **轨迹可视化**：绿色轨迹表示有效方向，红色表示无效方向
- **网格坐标系**：提供空间参考和方向指示
- **实时信息显示**：当前流速、有效点数等关键指标

### 数据流向与处理逻辑

```
原始视频帧
    ↓
特征点检测 → 初始过滤(网格/文字/静态区域) [实时过滤阶段]
    ↓
光流跟踪 → 数据收集(特征点轨迹、角度、位移等)
    ↓
完整数据集 [后处理过滤阶段 - 使用pandas/numpy批量处理]
    ↓
方向过滤(全局方向集中度分析)
    ↓
轨迹静态点过滤(总位移阈值)
    ↓
四分位法过滤(异常值检测)
    ↓
时间窗口动态过滤(活跃时间范围)
    ↓
最终流速统计结果
```

### 处理阶段对比

| 阶段 | 处理方式 | 数据量 | 计算效率 | 准确性 |
|------|----------|--------|----------|--------|
| **实时过滤** | 逐帧处理 | 小 | 低 | 基础 |
| **后处理过滤** | 批量处理 | 大 | 高 | 精确 |

### 算法优势
1. **两阶段处理架构**：实时过滤保证基本质量，后处理过滤确保最终精度
2. **高效批量计算**：使用pandas/numpy进行向量化操作，显著提升计算效率
3. **全局数据分析**：基于完整时间序列进行方向和活跃性分析，结果更准确
4. **多重验证机制**：通过5层过滤确保结果可靠性，每层针对不同类型的噪声和干扰
5. **分层数据处理**：从粗粒度到细粒度，逐步提高数据质量
6. **自适应处理**：能够处理特征点丢失、重新检测等复杂情况
7. **用户可控**：支持参数调节和交互式特征点选择
8. **详细输出**：提供完整的分析过程数据和多层次统计结果