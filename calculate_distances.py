#!/usr/bin/env python3
"""
计算经纬度坐标之间的距离，用于生成AWIVE配置文件
"""
import math
import json

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    使用Haversine公式计算两个经纬度点之间的距离（米）
    
    Args:
        lat1, lon1: 第一个点的纬度和经度
        lat2, lon2: 第二个点的纬度和经度
        
    Returns:
        float: 距离（米）
    """
    # 地球半径（米）
    R = 6371000
    
    # 转换为弧度
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # 计算差值
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    # Haversine公式
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    # 距离
    distance = R * c
    return distance

def main():
    # 定义坐标点（经度，纬度）
    # 按照用户提供的顺序：右下角、右上角、左上角、左下角
    coords = [
        [121.746625, 29.858377],  # 0: 右下角，像素坐标 [1260, 180]
        [121.746658, 29.858407],  # 1: 右上角，像素坐标 [1000, 130]  
        [121.746700, 29.858496],  # 2: 左上角，像素坐标 [75, 80]
        [121.746569, 29.858501]   # 3: 左下角，像素坐标 [745, 500]
    ]
    
    # 对应的像素坐标
    pixels = [
        [1260, 180],  # 0: 右下角
        [1000, 130],  # 1: 右上角
        [75, 80],     # 2: 左上角
        [745, 500]    # 3: 左下角
    ]
    
    print("坐标点信息：")
    labels = ["右下角", "右上角", "左上角", "左下角"]
    for i, (coord, pixel, label) in enumerate(zip(coords, pixels, labels)):
        print(f"{i}: {label} - 经纬度: {coord}, 像素: {pixel}")
    
    print("\n计算点对距离：")
    
    # 计算所有点对之间的距离
    distances = {}
    for i in range(len(coords)):
        for j in range(i+1, len(coords)):
            lon1, lat1 = coords[i]
            lon2, lat2 = coords[j]
            dist = haversine_distance(lat1, lon1, lat2, lon2)
            distances[f"({i},{j})"] = round(dist, 1)
            
            print(f"({i},{j}): {labels[i]} 到 {labels[j]} = {dist:.1f}米")
    
    print(f"\n生成的distances配置：")
    print(json.dumps(distances, indent=2))
    
    return distances, pixels

if __name__ == "__main__":
    main()
