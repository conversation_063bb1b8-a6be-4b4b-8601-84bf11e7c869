---
description: When AI performs Git - related operations in a project (such as committing code, creating branches, resolving conflicts, viewing changes, rolling back versions, etc.), this rule file should be loaded to ensure that the operations comply with best practices and avoid destructive behavior.
globs: 
alwaysApply: false
---
# Git 使用规范说明（用于 AI 编程辅助）

## description

当 AI 在项目中执行与 Git 相关的操作（如提交代码、创建分支、解决冲突、查看变更、回退版本等）时，应加载本规则文件，以确保操作符合最佳实践并避免破坏性行为。

---

## 基础规则

1. **避免直接推送到主分支（main/master）**
   - 默认应使用功能分支（如：`feature/xxx`、`bugfix/xxx`）。
   - 提交完成后建议通过 Pull Request 合并。

2. **提交信息必须规范**
   - 格式示例：`<类型>: <简洁描述>`  
     类型可选：`feat`、`fix`、`docs`、`refactor`、`test`、`chore`
   - 示例：`feat: 新增登录功能接口`

3. **每次提交前执行如下操作**
   - `git status`：确认变更文件
   - `git diff`：查看具体修改
   - `git add`：选择性添加
   - `git commit`：写清楚说明
   - **必要时执行**：`git stash` 保存临时工作内容

4. **拉取代码前先提交或保存本地修改**
   - 否则易造成冲突或丢失更改

5. **合并或变基操作需谨慎**
   - `merge` 更适合保留历史分支结构
   - `rebase` 应仅用于本地尚未推送的历史整理

6. **删除分支前请确认是否已合并至主干**
   - 使用 `git branch --merged` 查看已合并分支
   - 删除命令：`git branch -d branch_name`

---

## 高级规则

1. **AI 不应自动执行 destructive 操作**
   - 如：`git reset --hard`、`git clean -fd`、强推等
   - 必须在提示用户并获得确认后才可执行

2. **优先使用简洁命令组合替代重复操作**
   - 如：`git pull --rebase` 替代 `fetch + rebase`

3. **如涉及多个子模块或仓库，须逐个检查并提交**

---

## 建议配置

- 推荐开启 Git hook 校验（如：`commitlint`、`prettier`）
- 可使用 `.gitignore` 管理忽略文件
- 对 AI 操作，建议启用 dry-run 模式或打印预执行命令，供用户确认

