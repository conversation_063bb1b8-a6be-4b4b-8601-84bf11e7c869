---
description: This rule applies to the development specifications of Python projects. The writing of technical solution design documents ensures that the core code complies with the specifications.
globs: 
alwaysApply: false
---
---
description: 此规则适用于Python项目的开发规范，技术方案设计文档的编写保证核心代码符合规范，写代码遵守该规范，确保开发质量和效率。
globs:
alwaysApply: false
---

# Python项目开发规范

## 项目结构规范
- 采用领域驱动设计(DDD)分层架构，明确划分为以下层次：
  - `api` 层：处理 HTTP 请求，参数验证，路由转发（对应FastAPI/Flask路由）
  - `service` 层：实现核心业务逻辑，协调各个组件和服务调用
  - `repository` 层：数据访问和持久化，定义数据模型
  - `core` 层：基础设施和通用工具，如日志、配置、异常定义等
  - `client` 层：外部服务调用接口定义和实现,如封装对外部API、消息队列等的调用
- **依赖方向**
  - 严格遵循依赖方向：api → service → repository
  - 禁止循环依赖
  - 上层模块不能依赖于下层模块实现细节，应通过抽象基类或协议进行依赖

## 编码规范

### 命名约定
- **文件命名**
  - 使用小写字母，使用下划线分隔单词
  - 例如：`user_service.py`、`order_model.py`
- **变量命名**
  - 使用蛇形命名法（snake_case）
  - 局部变量：`user_id`、`order_status`
  - 常量使用全大写，下划线分隔（`MAX_RETRY_COUNT`、`DEFAULT_TIMEOUT`）
- **类和函数命名**
  - 类名使用大驼峰命名法（PascalCase）：`UserService`、`OrderRepository`
  - 函数名使用蛇形命名法：`get_user_by_id`、`process_order`
  - 私有方法和属性使用单下划线前缀：`_internal_method`

### 代码组织
- **类属性顺序**
  - 首先是类变量，然后是实例变量
  - 公有属性在前，私有属性在后
- **方法声明顺序**
  - `__init__` 方法
  - 公有方法（按重要性或调用关系排序）
  - 私有方法
  - 特殊方法（`__str__`、`__repr__` 等）
- **import 声明**
  - 将 import 分组为标准库、第三方库和内部包
  - 组之间用空行分隔
  - 使用 isort 自动排序

```python
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.logger import get_logger
from app.models.user import User
```

## 类型注解规范
- **强制使用类型注解**
  - 所有函数参数和返回值必须有类型注解
  - 使用 `typing` 模块提供的泛型类型
  - 复杂类型使用 `TypeAlias` 或 `NewType`

```python
from typing import Dict, List, Optional, Union
from pydantic import BaseModel

UserID = int
UserDict = Dict[str, Union[str, int]]

async def get_user_by_id(user_id: UserID) -> Optional[User]:
    """根据用户ID获取用户信息"""
    pass

def process_users(users: List[User]) -> Dict[UserID, UserDict]:
    """批量处理用户数据"""
    pass
```

## 错误处理规范
- **使用项目标准异常类型**
  - 定义统一的业务异常基类
  - 使用具体的异常类型表示不同的错误情况
  - 保持错误码和错误信息的一致性

```python
class BusinessException(Exception):
    """业务异常基类"""
    def __init__(self, code: str, message: str, details: Optional[Dict] = None):
        self.code = code
        self.message = message
        self.details = details or {}
        super().__init__(message)

class UserNotFoundException(BusinessException):
    def __init__(self, user_id: int):
        super().__init__(
            code="USER_NOT_FOUND",
            message=f"用户不存在: {user_id}",
            details={"user_id": user_id}
        )
```

- **错误传播**
  - 在服务层中，使用适当的异常包装底层错误
  - 记录足够的上下文信息用于调试
- **错误日志记录**
  - 只在错误发生的源头记录日志，避免重复记录
  - 使用项目统一的日志框架
  - 在记录错误时包含足够的上下文信息

```python
import logging
from app.core.logger import get_logger

logger = get_logger(__name__)

async def get_user_data(user_id: int) -> User:
    try:
        user = await user_repository.get_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)
        return user
    except Exception as e:
        logger.error(f"获取用户数据失败: user_id={user_id}, error={str(e)}", exc_info=True)
        raise BusinessException("DATA_ACCESS_ERROR", "数据访问失败")
```

## 异步编程规范
- **使用 asyncio 进行异步编程**
  - 所有IO操作都应使用异步方法
  - 正确使用 `async/await` 语法
  - 避免在异步函数中使用阻塞操作

```python
import asyncio
from typing import List

async def process_multiple_users(user_ids: List[int]) -> List[User]:
    """并发处理多个用户"""
    tasks = [get_user_by_id(user_id) for user_id in user_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    users = []
    for result in results:
        if isinstance(result, Exception):
            logger.error(f"处理用户失败: {result}")
            continue
        users.append(result)
    
    return users
```

- **资源管理**
  - 使用上下文管理器管理资源
  - 确保数据库连接、文件句柄等资源正确释放
  - 使用连接池管理数据库连接

## 性能优化规范
- **避免不必要的计算**
  - 使用生成器表达式代替列表推导式（当适用时）
  - 合理使用缓存装饰器
  - 预分配已知大小的数据结构

```python
from functools import lru_cache
from typing import Iterator

@lru_cache(maxsize=128)
def get_user_permissions(user_id: int) -> List[str]:
    """获取用户权限（带缓存）"""
    pass

def process_large_dataset(data: Iterator[Dict]) -> Iterator[Dict]:
    """使用生成器处理大数据集"""
    for item in data:
        if item.get('status') == 'active':
            yield transform_item(item)
```

- **高效的数据库操作**
  - 使用批量操作减少数据库往返
  - 实现合理的查询优化
  - 使用数据库连接池

```python
async def batch_create_users(users_data: List[Dict]) -> List[User]:
    """批量创建用户"""
    async with get_db_session() as session:
        users = [User(**data) for data in users_data]
        session.add_all(users)
        await session.commit()
        return users
```

- **使用高性能计算库**
  - 数值计算优先使用 `numpy` 替代纯Python循环
  - 数据处理使用 `pandas` 进行向量化操作
  - 避免在大数据集上使用Python原生的for循环
  
```python
# 好的实践：使用numpy进行向量化计算
import numpy as np
import pandas as pd

def calculate_user_scores_vectorized(data: pd.DataFrame) -> pd.Series:
    """使用向量化操作计算用户分数"""
    return data['base_score'] * data['multiplier'] + data['bonus']

# 避免的做法：使用Python循环
def calculate_user_scores_loop(data: List[Dict]) -> List[float]:
    """避免：使用循环计算（性能较差）"""
    results = []
    for item in data:
        score = item['base_score'] * item['multiplier'] + item['bonus']
        results.append(score)
    return results
```

## 测试规范
- **单元测试覆盖**
  - 为所有关键业务逻辑编写单元测试
  - 使用 pytest 作为测试框架
  - 测试文件使用 `test_` 前缀，放在 `tests/` 目录下
- **模拟外部依赖**
  - 使用 `unittest.mock` 或 `pytest-mock` 模拟外部服务
  - 使用依赖注入便于测试
- **异步测试**
  - 使用 `pytest-asyncio` 进行异步测试
  - 正确处理异步上下文
- **基准测试**
  - 为性能关键路径编写基准测试
  - 使用 `pytest-benchmark` 或 `timeit` 进行性能测试
  - 使用性能分析工具（如 `cProfile`、`py-spy`）发现瓶颈

```python
import pytest
from unittest.mock import AsyncMock, patch
from app.services.user_service import UserService
from pytest_benchmark import benchmark

@pytest.mark.asyncio
async def test_get_user_by_id_success():
    """测试成功获取用户"""
    # Arrange
    user_id = 1
    expected_user = User(id=user_id, name="test_user")
    
    with patch('app.repositories.user_repository.get_by_id') as mock_get:
        mock_get.return_value = expected_user
        
        # Act
        result = await UserService.get_user_by_id(user_id)
        
        # Assert
        assert result == expected_user
        mock_get.assert_called_once_with(user_id)

def test_user_processing_performance(benchmark):
    """测试用户处理性能"""
    users_data = generate_test_users(1000)
    result = benchmark(process_users_batch, users_data)
    assert len(result) == 1000
```

## 项目标准组件使用指南
- **日志记录**
  - 使用 `structlog` 或项目自定义日志框架
  - 在关键流程节点记录日志，但避免过度记录
- **配置管理**
  - 使用 `pydantic-settings` 管理配置
  - 避免硬编码配置值
- **HTTP 客户端**
  - 使用 `httpx` 进行异步HTTP调用
  - 设置合理的超时和重试策略
- **缓存使用**
  - 使用 Redis 或内存缓存实现缓存策略
  - 实现合适的缓存失效策略

## 代码示例

### API 层示例
```python
# api/v1/user.py
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional

from app.services.user_service import UserService
from app.core.deps import get_current_user
from app.core.logger import get_logger

router = APIRouter()
logger = get_logger(__name__)

class UserCreateRequest(BaseModel):
    name: str
    email: str
    age: Optional[int] = None

class UserResponse(BaseModel):
    id: int
    name: str
    email: str
    age: Optional[int]

@router.post("/users", response_model=UserResponse)
async def create_user(
    request: UserCreateRequest,
    current_user: User = Depends(get_current_user)
) -> UserResponse:
    """创建用户"""
    try:
        user = await UserService.create_user(request.dict())
        return UserResponse.from_orm(user)
    except BusinessException as e:
        logger.error(f"创建用户失败: {e.message}", extra={"code": e.code})
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        logger.error(f"创建用户异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="内部服务器错误")
```

### Service 层示例
```python
# services/user_service.py
from typing import Dict, List, Optional
from app.models.user import User
from app.repositories.user_repository import UserRepository
from app.core.exceptions import BusinessException
from app.core.logger import get_logger

logger = get_logger(__name__)

class UserService:
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository
    
    async def create_user(self, user_data: Dict) -> User:
        """创建用户"""
        # 参数验证
        if not user_data.get('email'):
            raise BusinessException("INVALID_PARAM", "邮箱不能为空")
        
        # 检查邮箱是否已存在
        existing_user = await self.user_repository.get_by_email(user_data['email'])
        if existing_user:
            raise BusinessException("EMAIL_EXISTS", "邮箱已存在")
        
        # 创建用户
        try:
            user = await self.user_repository.create(user_data)
            logger.info(f"用户创建成功: user_id={user.id}")
            return user
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}", exc_info=True)
            raise BusinessException("CREATE_FAILED", "用户创建失败")
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        try:
            return await self.user_repository.get_by_id(user_id)
        except Exception as e:
            logger.error(f"获取用户失败: user_id={user_id}, error={str(e)}")
            raise BusinessException("GET_FAILED", "获取用户失败")
```

### Repository 层示例
```python
# repositories/user_repository.py
from typing import Optional, List, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.user import User
from app.core.database import get_session
from app.core.logger import get_logger

logger = get_logger(__name__)

class UserRepository:
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, user_data: Dict) -> User:
        """创建用户"""
        user = User(**user_data)
        self.session.add(user)
        await self.session.commit()
        await self.session.refresh(user)
        return user
    
    async def get_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        stmt = select(User).where(User.id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        stmt = select(User).where(User.email == email)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_all(self, limit: int = 100, offset: int = 0) -> List[User]:
        """获取用户列表"""
        stmt = select(User).limit(limit).offset(offset)
        result = await self.session.execute(stmt)
        return result.scalars().all()
```

## 示例

```python
<example>
# 良好实践：正确的异步错误处理和日志记录
from typing import Optional
from app.core.exceptions import BusinessException
from app.core.logger import get_logger
from app.repositories.user_repository import UserRepository

logger = get_logger(__name__)

class UserService:
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository
    
    async def get_user_profile(self, user_id: int) -> Optional[User]:
        """获取用户资料"""
        # 参数验证
        if not user_id or user_id <= 0:
            raise BusinessException("INVALID_PARAM", "用户ID无效")
        
        try:
            # 调用仓储层
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise BusinessException("USER_NOT_FOUND", "用户不存在")
            
            return user
        except BusinessException:
            # 业务异常直接抛出
            raise
        except Exception as e:
            # 包装系统异常并记录日志
            logger.error(
                f"获取用户资料失败: user_id={user_id}",
                exc_info=True,
                extra={"user_id": user_id}
            )
            raise BusinessException("SERVICE_ERROR", "获取用户资料失败")
</example>

<example type="invalid">
# 不良实践：不规范的错误处理和日志记录
import logging
from app.repositories.user_repository import UserRepository

class UserService:
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository
    
    def get_user_profile(self, user_id):  # 缺少类型注解和async
        # 缺少参数验证
        try:
            user = self.user_repository.get_by_id(user_id)  # 缺少await
        except Exception as e:
            # 错误：使用print而非日志框架
            print(f"Error getting user: {e}")
            # 错误：使用标准logging而非项目日志框架
            logging.error(f"Failed to get user {user_id}: {e}")
            # 错误：直接返回None而不是抛出具体异常
            return None
        
        return user
</example>
```