---
description: 
globs: 
alwaysApply: false
---
# 项目通用开发规范
## 1. 项目结构规范
### 目录结构
```
project_name/
├── docs/                   # 项目文档
├── src/                    # 源代码
│   ├── core/               # 核心功能
│   ├── utils/              # 工具函数
├── tests/                  # 测试文件
├── scripts/                # 脚本文件
├── config/                 # 配置文件
├── data/                   # 数据文件
├── .gitignore              # git忽略列表
├── .cursorignore           # cursor忽略列表
└── README.md               # 项目说明
```
### 组织原则
- 保持项目结构清晰，遵循模块化原则
- 相关功能应放在同一目录下
- 使用适当的目录命名，反映其包含内容

## 2. 代码规范
### 命名规范
- 使用一致的命名风格，遵循所选编程语言的惯例
- 名称应具有描述性，避免使用无意义的缩写
- 保持命名风格在整个项目中的一致性
- 避免使用关键字和保留字作为变量名
### 代码质量原则
- 遵循 SOLID 设计原则
- 避免代码重复（DRY原则）
- 保持代码简洁、清晰、易读
- 考虑代码的可维护性和可扩展性
### 代码格式化
- 使用自动化代码格式化工具
- 在项目中配置统一的格式化规则
- 通过 pre-commit 钩子确保代码格式一致性
### 异常处理
- 合理使用异常处理机制
- 提供清晰的错误信息
- 记录必要的错误日志
- 优雅处理边界情况

## 3. 文档规范
### 代码注释
- 为复杂的业务逻辑和算法添加说明性注释
- 函数和类应包含用途、参数和返回值的描述
- 使用所选编程语言的标准注释格式
- 注释应与代码保持同步更新
### 项目文档
- 及时更新 README 和技术文档
- 使用中文编写文档
- 包含必要的安装和使用说明
- 记录重要的架构决策
### 国际化准备
- 代码注释使用中文
- 错误信息和日志使用中文描述
- 预留国际化支持的接口

## 4. 开发环境
### 依赖管理
- 使用项目对应语言的包管理工具
- 锁定依赖版本，确保构建稳定性
- 定期更新依赖，修复安全隐患
- 优先使用现有库和工具，避免重新发明轮子
### 环境隔离
- 使用虚拟环境或容器化技术隔离项目依赖
- 提供清晰的环境搭建文档
- 统一团队开发环境配置

## 5. 版本控制规范
### 基础配置
- 使用 Git 作为版本控制系统
- 设置合适的 .gitignore 文件
- 保护主分支，实施分支权限控制
### 分支管理
- 采用规范的分支开发流程（如 Git Flow 或 Trunk Based Development）
- 主分支保持稳定，开发在特性分支进行
- 定期清理过期分支
### 提交规范
- 使用清晰的 commit 信息
- 每个提交专注于单一功能或修复
- 合理使用 tag 标记版本
### 代码审查
- 所有代码变更必须经过审查
- 遵循代码审查清单
- 及时响应审查意见
- 确保代码质量和一致性

## 6. 测试规范
### 测试原则
- 遵循测试金字塔原则
- 保持测试代码的整洁和可维护性
- 避免测试代码重复
- 关注测试覆盖率
### 测试类型
- 单元测试：测试独立功能单元
- 集成测试：测试模块间交互
- 端到端测试：测试完整业务流程
- 性能测试：关键功能性能验证

## 7. 安全规范
- 使用环境变量存储敏感配置
- 避免在代码中硬编码敏感信息
- 定期更新依赖包版本
- 实施安全扫描