# CSV批量视频裁剪工具使用说明

## 功能概述

这个工具用于根据CSV文件批量裁剪大型视频文件，主要解决以下问题：
- 处理GB级别的大型视频文件
- 根据中文时间格式精确裁剪视频片段
- 自动添加缓冲时间避免遗漏关键内容
- 批量处理多个视频文件

## 使用方法

### 1. 准备CSV文件

CSV文件应包含以下4列：
- `日期`: 格式为 `2025-05-13`
- `开始时间`: 格式为 `下午3时40分00秒`
- `结束时间`: 格式为 `下午3时41分59秒`  
- `视频名称`: 格式为 `ch03_20250512153226.mp4`

示例CSV内容：
```csv
日期,开始时间,结束时间,视频名称
2025-05-12,下午3时40分00秒,下午3时41分59秒,ch03_20250512153226.mp4
2025-05-13,上午7时15分00秒,上午7时16分59秒,ch03_20250509054129.mp4
```

### 2. 配置文件设置

编辑 `config/csv_batch_config.yaml` 文件：
```yaml
paths:
  video_base_path: "/home/<USER>/data/xiwu/"  # 原始视频路径
  output_dir: "data/video/"                 # 输出目录
  csv_file: "config/selected.csv"           # CSV文件路径

trimming:
  buffer_seconds: 60        # 缓冲时间（秒）
  min_duration: 30          # 最小裁剪时长
```

### 3. 运行命令

#### 测试时间解析功能
```bash
python src/utils/video_trimmer.py --test-time
```

#### 执行批量处理
```bash
python src/utils/video_trimmer.py --csv-batch
```

#### 交互式单个视频裁剪
```bash
python src/utils/video_trimmer.py
```

## 功能特点

### 智能缓冲处理
- 自动在指定时间段前后添加缓冲时间（默认60秒）
- 处理视频边界情况，避免超出视频长度
- 确保最小裁剪时长（默认30秒）

### 数据验证
- 自动验证CSV数据格式
- 检查时间逻辑错误（结束时间早于开始时间）
- 识别异常时间段（过长或过短）
- 验证视频文件名格式

### 错误处理
- 跳过不存在的视频文件
- 跳过已存在的输出文件
- 详细的错误报告和处理统计

## 时间格式说明

### 支持的中文时间格式
- `上午10时25分00秒` → 10:25:00 AM
- `下午3时40分00秒` → 3:40:00 PM  
- `上午12时05分00秒` → 12:05:00 AM (午夜)
- `下午12时10分00秒` → 12:10:00 PM (正午)

### 时间转换逻辑
- 上午12时 = 00:xx (午夜)
- 上午1-11时 = 01:xx - 11:xx
- 下午12时 = 12:xx (正午)
- 下午1-11时 = 13:xx - 23:xx

## 输出文件

### 文件命名规则
输出文件按以下格式命名：
```
{原始文件名}_{开始秒数}_{结束秒数}.mp4
```

例如：
```
ch03_20250512153226_56400_56519.mp4
```

### 输出目录结构
```
data/
└── video/              # 裁剪后的视频文件
    ├── ch03_xxx.mp4
    └── ...
```

## 常见问题

### Q: 如何处理视频开头的缓冲时间？
A: 工具会自动在指定时间前后各添加60秒缓冲时间，可以在配置文件中调整。

### Q: 如果视频文件不存在怎么办？
A: 工具会跳过不存在的文件并在结果中标记为失败，继续处理其他文件。

### Q: 如何查看处理进度？
A: 工具会显示当前处理的文件序号和总数，以及每个文件的处理状态。

### Q: 时间格式解析错误怎么办？
A: 工具会在开始处理前验证所有时间格式，发现错误会提示用户确认是否继续。

## 注意事项

1. **磁盘空间**: 确保输出目录有足够空间存储裁剪后的视频
2. **处理时间**: 大型视频文件处理可能需要较长时间
3. **文件路径**: 确保视频基础路径正确且可访问
4. **权限问题**: 确保对输入和输出目录有读写权限 