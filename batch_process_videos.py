#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量视频处理和流速计算脚本
此脚本用于批量裁剪视频并计算流速，最后将结果汇总到CSV文件中。
支持通过YAML配置文件选择不同的分析方法（piv, otv, stiv）。
"""

import os
import csv
import re
import time
from pathlib import Path
from datetime import datetime

from src.utils.video_trimmer import trim_video
from src.analysis_algorithms.openpiv_piv_analyzer import analyze_video_segment_with_openpiv
from src.analysis_algorithms.opencv_otv_analyzer import analyze_video_segment_with_otv, sort_polygon_points_clockwise
from src.analysis_algorithms.stiv_analyzer import analyze_video_stiv
from src.utils.config_manager import ConfigManager
from src.utils.logging_utils import configure_logging_from_config

# ===== 默认常量 =====
CONFIG_FILE_PATH = Path('config/batch_config.yaml')

# ===== 工具函数 =====

def load_config(config_path):
    """加载YAML配置文件"""
    try:
        config_manager = ConfigManager(str(config_path))
        print(f"已成功加载配置文件: {config_path}")
        return config_manager
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_path} 未找到。请确保文件存在。")
        return None
    except Exception as e:
        print(f"错误: 解析配置文件 {config_path} 失败: {e}")
        return None

def extract_filtered_speed_from_result_file(result_file):
    """从结果文本文件中提取四分位过滤后的平均流速
    
    Args:
        result_file: 流速结果文件路径
    
    Returns:
        四分位过滤后的平均流速，如果未找到则返回None
    """
    try:
        with open(result_file, 'r') as f:
            content = f.read()
            
        # 使用正则表达式查找四分位过滤后平均流速
        match = re.search(r'四分位过滤后平均流速:\s*([\d\.]+)', content)
        if match:
            return float(match.group(1))
    except Exception as e:
        print(f"读取流速数据文件出错: {e}")
    
    return None

def process_single_video(input_video, start_time, end_time, config_manager):
    """处理单个视频，裁剪并计算流速

    Args:
        input_video: 输入视频路径
        start_time: 开始时间
        end_time: 结束时间
        config_manager: ConfigManager实例

    Returns:
        元组 (文件名, 开始时间, 结束时间, 最小流速, 平均流速, 最大流速)
    """
    # 确保输入视频存在
    if not os.path.exists(input_video):
        print(f"错误: 视频文件 {input_video} 不存在")
        return None

    # 获取原始配置字典
    config = config_manager.get_raw_config()

    # 创建输出目录
    os.makedirs(config['output_dir'], exist_ok=True)
    
    # 定义裁剪视频的输出目录
    trimmed_video_output_dir = os.path.join(config['output_dir'], 'trimmed')
    os.makedirs(trimmed_video_output_dir, exist_ok=True)
    
    print(f"\n===== 开始处理视频: {os.path.basename(input_video)} =====")
    print(f"裁剪范围: {start_time} 到 {end_time}")
    
    # 从全局配置获取 pixel_to_meter
    global_pixel_to_meter = config.get('pixel_to_meter')
    if global_pixel_to_meter is None:
        print(f"错误: 全局 'pixel_to_meter' 未在配置文件 {CONFIG_FILE_PATH} 中设置。")
        return None
        
    roi_points = config.get('roi_points', None)
    roi_points = sort_polygon_points_clockwise(roi_points)
    analysis_method = config.get('analysis_method', 'otv')
    output_dir = config['output_dir']
    
    # 定义OTV分析结果的输出目录
    otv_analysis_output_dir = os.path.join(output_dir, 'otv')
    os.makedirs(otv_analysis_output_dir, exist_ok=True)
    
    if analysis_method == 'otv':
        otv_params = config.get('otv_params', {})
        add_visual_elements_to_trimmed_video = otv_params.get('add_visual_elements_to_trimmed_video', False)
    elif analysis_method == 'piv':
        piv_params = config.get('piv_params', {})
        piv_config_json_path = piv_params.get('piv_config_json_path', None)
        add_visual_elements_to_trimmed_video = False  # 默认不添加视觉元素
    elif analysis_method == 'stiv':
        add_visual_elements_to_trimmed_video = False  # STIV不需要添加视觉元素
    else:
        add_visual_elements_to_trimmed_video = False  # 默认不添加视觉元素
    
    try:
        # 1. 检查是否已存在裁剪后的视频
        print("\n[步骤1] 检查是否已存在裁剪后的视频...")
        # 根据原文件名和时间范围构建预期的输出文件名
        base_name = os.path.splitext(os.path.basename(input_video))[0]
        start_time_str = start_time.replace(':', '').zfill(4)
        end_time_str = end_time.replace(':', '').zfill(4)
        # 更新期望输出视频路径到 trimmed_video_output_dir
        expected_output_video = os.path.join(trimmed_video_output_dir, f'{base_name}_{start_time_str}_{end_time_str}.mp4')
        
        if os.path.exists(expected_output_video):
            print(f"已找到裁剪后的视频: {os.path.basename(expected_output_video)}")
            output_video = expected_output_video
        else:
            # 需要裁剪视频
            print("\n[步骤1.1] 开始裁剪视频...")
            output_video = trim_video(
                input_video, 
                start_time, 
                end_time, 
                roi_points=roi_points, 
                output_dir=trimmed_video_output_dir, # 使用新的裁剪视频输出目录
                add_visual_elements=add_visual_elements_to_trimmed_video
            )
        
        if not output_video:
            print(f"视频裁剪失败")
            return None
        
        # 2. 计算流速
        print(f"\n[步骤2] 开始计算流速，使用 {analysis_method} 算法...")
        
        if analysis_method == 'otv':
            # 从配置文件中读取完整的otv_params
            otv_params = config.get('otv_params', {})
            
            # 准备OTV分析器的配置参数，基于配置文件中的完整otv_params
            otv_specific_params = otv_params.copy()  # 复制完整的otv_params
            
            # 更新运行时特定的参数
            otv_specific_params.update({
                'input_video_path': output_video,
                'pixel_to_meter': global_pixel_to_meter,
                'roi_points': roi_points,
                'output_dir': otv_analysis_output_dir
            })
            
            otv_config = {
                'otv_specific_params': otv_specific_params
            }
            
            print(f"OTV配置参数: {otv_specific_params}")
            
            # 调用OTV分析器处理视频
            otv_results = analyze_video_segment_with_otv(output_video, otv_config)
            
            if otv_results is None:
                print(f"警告: 未能通过OTV分析器计算流速")
                min_s, avg_s, max_s = 0.0, 0.0, 0.0
                used_feature_method = "none"
            else:
                min_s, avg_s, max_s, used_feature_method = otv_results
                
        elif analysis_method == 'piv':
            # 检查PIV配置文件是否存在
            if not piv_config_json_path or not os.path.exists(piv_config_json_path):
                print(f"错误: OpenPIV配置文件不存在: {piv_config_json_path}")
                return None
                
            # 调用OpenPIV分析器处理视频
            print(f"使用OpenPIV分析，配置文件: {piv_config_json_path}")
            roi_np_array = roi_points if roi_points is not None and len(roi_points) > 0 else None
            
            min_speed_piv, avg_speed_piv, max_speed_piv, pairs_count = analyze_video_segment_with_openpiv(
                video_path=output_video,
                config_path=piv_config_json_path,
                pixel_to_meter=global_pixel_to_meter,
                roi_override_points=roi_np_array # type: ignore roi_np_array
            )
            
            if pairs_count > 0:
                print(f"OpenPIV分析完成: 处理了{pairs_count}对帧")
                print(f"最小速度: {min_speed_piv:.4f} m/s")
                print(f"平均速度: {avg_speed_piv:.4f} m/s")
                print(f"最大速度: {max_speed_piv:.4f} m/s")
                min_s, avg_s, max_s = round(min_speed_piv, 4), round(avg_speed_piv, 4), round(max_speed_piv, 4)
                used_feature_method = "piv"  # PIV方法使用自己的特征检测
            else:
                print(f"警告: OpenPIV分析未能处理任何帧对")
                min_s, avg_s, max_s = 0.0, 0.0, 0.0
                used_feature_method = "piv"

        elif analysis_method == 'stiv':
            # 使用STIV分析器处理视频
            print(f"使用STIV分析器处理视频")

            try:
                # 配置日志级别（确保DEBUG日志能正确输出）
                try:
                    configure_logging_from_config(config_manager)
                    print(f"日志级别已配置为: {config_manager.get_log_level()}")
                except Exception as log_error:
                    print(f"警告: 配置日志级别失败: {log_error}")

                # 直接使用STIVAnalyzer类，传递现有的config_manager实例
                from src.analysis_algorithms.stiv_analyzer import STIVAnalyzer
                analyzer = STIVAnalyzer.__new__(STIVAnalyzer)
                analyzer.config_manager = config_manager
                analyzer.config_path = str(CONFIG_FILE_PATH)

                # 验证STIV配置
                is_valid, errors = config_manager.validate_config("stiv")
                if not is_valid:
                    raise ValueError(f"STIV配置验证失败: {'; '.join(errors)}")

                # 调用分析方法
                stiv_results = analyzer.analyze(
                    video_path=output_video,
                    save_debug=config_manager.is_debug_enabled()
                )

                if stiv_results:
                    velocities = []
                    # STIV结果使用数字键（'0', '1', '2'等）
                    for key, line_data in stiv_results.items():
                        if key.isdigit() and isinstance(line_data, dict) and 'velocity' in line_data:
                            velocities.append(line_data['velocity'])

                    if velocities:
                        # 计算统计值
                        min_s = round(min(velocities), 4)
                        avg_s = round(sum(velocities) / len(velocities), 4)
                        max_s = round(max(velocities), 4)
                        used_feature_method = "stiv"

                        print(f"STIV分析完成: 处理了{len(velocities)}条分析线")
                        print(f"最小速度: {min_s:.4f} m/s")
                        print(f"平均速度: {avg_s:.4f} m/s")
                        print(f"最大速度: {max_s:.4f} m/s")
                    else:
                        print(f"警告: STIV分析未能计算出有效速度")
                        min_s, avg_s, max_s = 0.0, 0.0, 0.0
                        used_feature_method = "stiv"
                else:
                    print(f"警告: STIV分析未返回有效结果")
                    min_s, avg_s, max_s = 0.0, 0.0, 0.0
                    used_feature_method = "stiv"

            except Exception as e:
                print(f"STIV分析过程中发生错误: {e}")
                min_s, avg_s, max_s = 0.0, 0.0, 0.0
                used_feature_method = "stiv"

        else:
            print(f"错误: 不支持的分析方法: {analysis_method}")
            return None
        
        print(f"视频 {os.path.basename(output_video)} 的计算流速: Min={min_s:.4f}, Avg={avg_s:.4f}, Max={max_s:.4f} m/s, 特征检测方法: {used_feature_method}")
        
        # 返回结果
        return (os.path.basename(input_video), start_time, end_time, min_s, avg_s, max_s, analysis_method, used_feature_method)
    
    except Exception as e:
        print(f"处理视频时发生错误: {e}")
        return None

def main():
    """主函数，批量处理视频并汇总结果"""
    print("===== 批量视频处理和流速计算 =====")

    config_manager = load_config(CONFIG_FILE_PATH)
    if not config_manager:
        return

    # 在主函数开始时就配置全局日志系统
    try:
        configure_logging_from_config(config_manager)
        print(f"全局日志级别已配置为: {config_manager.get_log_level()}")
    except Exception as log_error:
        print(f"警告: 配置全局日志级别失败: {log_error}")

    # 获取原始配置
    config = config_manager.get_raw_config()
    batch_settings = config.get('batch_settings', {})
    input_videos = batch_settings.get('input_videos', [])
    output_dir = batch_settings.get('output_dir', 'data/output')
    result_csv = batch_settings.get('result_csv', 'flow_speed_results.csv')
    time_ranges = batch_settings.get('time_ranges', [])
    analysis_method = batch_settings.get('analysis_method', 'otv') # 获取分析方法
    
    # 检查是否有配置视频路径
    if not input_videos:
        print("错误: 未配置输入视频路径。请在脚本顶部的INPUT_VIDEOS列表中添加视频路径。")
        return
    
    print(f"视频数量: {len(input_videos)}")
    print(f"时间范围数量: {len(time_ranges)}")
    print(f"输出目录: {output_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 用于存储所有处理结果
    results = []
    
    # 遍历所有视频和时间范围
    total_tasks = len(input_videos) * len(time_ranges)
    task_count = 0
    
    for video in input_videos:
        for start_time, end_time in time_ranges:
            task_count += 1
            print(f"\n处理任务 {task_count}/{total_tasks}")
            
            processing_start_time = time.time()
            # 处理视频
            result_tuple = process_single_video(video, start_time, end_time, config_manager)
            processing_end_time = time.time()
            duration = round(processing_end_time - processing_start_time, 2)
            processing_datetime_str = datetime.fromtimestamp(processing_end_time).strftime('%Y-%m-%d %H:%M:%S')

            if result_tuple:
                # 将处理时间和算法名称添加到结果中
                file_name, s_time, e_time, min_speed_val, avg_speed_val, max_speed_val, analysis_method, feature_method = result_tuple
                results.append((processing_datetime_str, file_name, s_time, e_time, min_speed_val, avg_speed_val, max_speed_val, f"{duration}s", analysis_method, feature_method))
    
    # 如果没有成功处理任何视频，提前退出
    if not results:
        print("\n错误: 没有成功处理任何视频。请检查输入视频路径和配置。")
        return
    
    # 将结果写入CSV文件
    csv_path = os.path.join(output_dir, result_csv)
    file_exists = os.path.isfile(csv_path) and os.path.getsize(csv_path) > 0
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        if not file_exists:
            writer.writerow(['处理日期', '文件名', '开始时间', '结束时间', '最小流速(m/s)', '平均流速(m/s)', '最大流速(m/s)', '处理时长', '算法', '特征识别方案'])
        for row in results:
            # 格式化速度数值为4位小数
            formatted_row = list(row)
            formatted_row[4] = f"{row[4]:.4f}"  # 最小流速
            formatted_row[5] = f"{row[5]:.4f}"  # 平均流速
            formatted_row[6] = f"{row[6]:.4f}"  # 最大流速
            writer.writerow(formatted_row)
    
    print(f"\n处理完成！结果已保存到: {os.path.abspath(csv_path)}")
    print(f"共处理了 {len(results)} 个视频片段")

if __name__ == "__main__":
    main() 