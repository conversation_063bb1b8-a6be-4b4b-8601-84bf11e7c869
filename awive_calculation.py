#!/usr/bin/env python
import time
from pathlib import Path
import cv2
import tempfile
import json
import sys
import os
import numpy as np

# 导入调整函数
from src.utils.adjust_config import load_and_adjust_config_dict
# 导入可视化函数
from src.utils.visualize_setup import visualize_cropping_and_lines
# 导入自动视频截取功能
from src.utils.video_auto_trimmer import smart_video_preprocessing

from awive.algorithms import otv, sti, water_level_hist

# 自定义 JSON 序列化函数来处理 NumPy 类型
def convert_numpy(obj):
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    raise TypeError(f'Object of type {obj.__class__.__name__} is not JSON serializable')

def analyze_video(video_path, original_config_path, threshold=0.3, show_results=False, auto_trim=True):
    """
    对视频文件进行流速分析

    Args:
        video_path: 视频文件路径
        original_config_path: 原始配置文件路径
        threshold: 算法选择阈值，水位高于此值使用STIV，否则使用OTV。
        show_results: 是否显示处理结果
        auto_trim: 是否启用自动视频截取（超过40秒自动截取10-40秒片段）

    Returns:
        dict: 流速分析结果
    """
    # --- 初始化 ---
    original_config_dict = None
    adjusted_config_dict = None
    initial_frame = None
    temp_config_file = None
    adjusted_config_path = None

    try:
        # 1a. 加载原始配置 (可视化需要)
        try:
            with open(original_config_path, 'r') as f:
                original_config_dict = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"加载原始配置 {original_config_path} 时出错 (可视化可能失败): {e}", file=sys.stderr)
            # 不在此处返回，允许分析继续 (如果可能)
        except Exception as e:
            print(f"读取原始配置 {original_config_path} 时发生意外错误 (可视化可能失败): {e}", file=sys.stderr)

        # 1b. 加载并调整配置字典 (用于处理)
        adjusted_config_dict = load_and_adjust_config_dict(original_config_path)
        # 如果上一步失败，adjusted_config_dict 已经是 None
        if adjusted_config_dict is None: raise ValueError("无法加载或调整配置字典")

    except (FileNotFoundError, json.JSONDecodeError, ValueError) as e:
        print(f"加载或调整配置时出错: {e}", file=sys.stderr)
        return None # 或者抛出异常
    except Exception as e:
        print(f"处理配置时发生意外错误: {e}", file=sys.stderr)
        return None

    # 2. 智能视频预处理（自动截取）
    if auto_trim:
        print("检查视频长度并进行智能预处理...")
        try:
            processed_video_path, was_trimmed = smart_video_preprocessing(
                str(video_path),
                adjusted_config_dict,
                auto_trim=True,
                max_duration=40.0
            )
            if was_trimmed:
                print(f"视频已自动截取: {processed_video_path}")
                video_path = processed_video_path
            else:
                print("视频无需截取")
        except Exception as e:
            print(f"自动截取过程中出错: {e}，使用原始视频")
    else:
        print("自动截取功能已禁用")

    # 3. 更新视频路径 (在字典中)
    # 确保 'dataset' 键存在
    if 'dataset' not in adjusted_config_dict:
        adjusted_config_dict['dataset'] = {}
    video_file_path_str = str(Path(video_path).resolve())
    adjusted_config_dict['dataset']['video_fp'] = video_file_path_str # 使用解析后的字符串路径

    # --- 加载用于可视化的帧 ---
    try:
        cap = cv2.VideoCapture(video_file_path_str)
        if not cap.isOpened():
            print(f"错误: 无法打开视频文件进行可视化: {video_file_path_str}", file=sys.stderr)
        else:
            ret, initial_frame = cap.read()
            cap.release()
            if not ret:
                print("错误: 无法从视频读取帧进行可视化。", file=sys.stderr)
                initial_frame = None # 标记失败
    except Exception as e:
        print(f"加载可视化帧时出错: {e}", file=sys.stderr)
        initial_frame = None

    # 3. 创建临时配置文件以传递给 awive 函数
    try:
        # 创建一个临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix=".json", delete=False) as temp_f:
            # 使用自定义的 default 函数
            json.dump(adjusted_config_dict, temp_f, indent=2, default=convert_numpy)
            temp_config_file = temp_f.name # 获取临时文件的路径
            adjusted_config_path = Path(temp_config_file)
        print(f"临时调整后的配置文件已创建: {adjusted_config_path}")

        # --- 现在使用 adjusted_config_path 调用 awive 函数 ---

        # 4. 检测水位
        print("检测水位...")
        # 传递调整后的配置 *路径*
        idpp = water_level_hist.main(adjusted_config_path)
        if idpp is None:
            print("警告: 未能检测到水位 (water_level_hist.main 返回 None)")
            # 决定如何处理，或许抛出错误或返回默认值
            # 目前，假设没有 idpp 就无法继续
            raise ValueError("无法检测水位")

        print(f"检测到指标值 (idpp): {idpp}") # 稍微修改了标签

        # --- 执行可视化 (如果可能) ---
        use_stiv_algorithm = idpp > threshold # 确定算法类型
        if initial_frame is not None and original_config_dict is not None:
            video_stem = Path(video_path).stem
            output_dir = Path("data/output")
            output_dir.mkdir(parents=True, exist_ok=True)
            print("\n生成可视化图像...")
            try:
                visualize_cropping_and_lines(
                    frame=initial_frame,
                    original_config=original_config_dict,
                    adjusted_config=adjusted_config_dict,
                    output_dir=output_dir,
                    base_filename=video_stem,
                    is_stiv=use_stiv_algorithm # 传递标志
                )
            except Exception as e:
                print(f"生成可视化图像时出错: {e}", file=sys.stderr)
        else:
            print("\n信息: 跳过可视化，因为无法加载帧或原始配置。")

        # 5. 根据指标值选择算法
        t = time.process_time()
        results = None
        image = None
        if use_stiv_algorithm:
            print("指标值高于阈值，使用STIV算法...")
            # 传递调整后的配置 *路径*
            results = sti.main(adjusted_config_path)
        else:
            print("指标值低于或等于阈值，使用OTV算法...")
            # 传递调整后的配置 *路径*
            # 注意: run_otv 可能需要 Config 对象，或者它内部加载路径？
            # 假设它也接收路径
            results, image = otv.run_otv(adjusted_config_path, show_video=False) # show_video 稍后处理

        elapsed_time = time.process_time() - t

        # 6. 输出和可视化/保存结果
        if results is not None:
            print("\n分析结果:")
            # 假设 results 是类似 {'0': {'velocity': v1}, '1': ...} 的字典
            try:
                num_results = len(results)
                total_velocity = 0
                valid_velocities = 0
                for i in range(num_results):
                    key = str(i)
                    if key in results and 'velocity' in results[key]:
                        velocity = results[key]['velocity']
                        print(f"区域 {i}: 流速 = {velocity:.4f} m/s")
                        total_velocity += velocity
                        valid_velocities += 1
                    else:
                        print(f"区域 {i}: 未找到速度结果")

                if valid_velocities > 0:
                     avg_velocity = total_velocity / valid_velocities
                     print(f"\n平均流速: {avg_velocity:.4f} m/s")
                else:
                     print("\n未能计算平均流速")

            except (TypeError, KeyError) as e:
                 print(f"处理结果时出错: {e}. Results: {results}")
        else:
             print("\n算法未返回有效结果。")


        print(f"\n处理时间: {elapsed_time:.2f} 秒")

        # 保存 OTV 结果图像 (如果生成了)
        if image is not None:
            try:
                output_dir = Path("data/output")
                output_dir.mkdir(parents=True, exist_ok=True) # 确保目录存在
                video_stem = Path(video_path).stem # 获取不带扩展名的视频文件名
                output_image_path = output_dir / f"{video_stem}_otv_result.png"
                cv2.imwrite(str(output_image_path), image)
                print(f"OTV 结果图像已保存至: {output_image_path}")
            except Exception as e:
                print(f"保存 OTV 图像时出错: {e}", file=sys.stderr)
        elif not use_stiv_algorithm and image is None: # 如果是 OTV 但没有图像
             print("OTV 算法运行但未返回可保存的图像。")

        return results

    finally:
        # 7. 清理临时文件
        if temp_config_file:
            try:
                os.remove(temp_config_file)
                print(f"临时配置文件已删除: {temp_config_file}")
            except OSError as e:
                print(f"错误: 无法删除临时文件 {temp_config_file}: {e}", file=sys.stderr)

# 使用示例
if __name__ == "__main__":
    # 设置原始配置文件路径
    original_config = Path("config/awive_config_yuwangling.json").resolve()
    # 设置视频文件路径
    video_file = Path("data/video/0812_speed_720.mp4").resolve()

    if not original_config.is_file():
        print(f"错误: 原始配置文件未找到: {original_config}", file=sys.stderr)
        sys.exit(1)
    if not video_file.is_file():
         print(f"错误: 视频文件未找到: {video_file}", file=sys.stderr)
         sys.exit(1)

    # 调用函数进行分析，传递原始配置路径
    # show_results 现在仅控制是否打印 "未生成可视化图像" 这类信息，实际显示已被移除
    show_gui = False # 不再需要尝试显示 GUI
    analysis_results = analyze_video(
        video_path=video_file,
        original_config_path=original_config,
        show_results=show_gui # 主要用于控制日志信息，不再尝试显示
    )

    if analysis_results:
        print("\n分析完成。")
        # 可能在此处对 analysis_results 进行进一步处理
    else:
        print("分析未能成功完成。")