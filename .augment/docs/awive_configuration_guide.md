# AWIVE雨汪岭视频流速估算配置指南

## 核心改动总结

本文档总结了AWIVE算法在雨汪岭视频(`data/video/0812_speed_720.mp4`)流速估算中的所有优化改动和配置要点。

### 主要问题解决

| 问题 | 解决方案 | 关键改动 |
|------|----------|----------|
| 可视化双重标记 | 确认为正常设计特性 | 无需修改 |
| ROI区域不匹配稳定化区域 | 重新配置ROI参数 | 调整pre_roi和roi坐标 |
| 假设水平流向 | 适配非水平河道 | 重设计lines和lines_range |
| 长视频处理效率 | 自动截取功能 | 新增video_auto_trimmer模块 |

## 优化后的完整配置

### 配置文件：`config/awive_config_yuwangling.json`

```json
{  
  "dataset": {  
    "video_fp": "data/video/0812_speed_720.mp4",
    "gcp": {
      "apply": true,
      "pixels": [[1260, 180], [1000, 130], [75, 80], [745, 500]],
      "distances": {
        "(0,1)": 4.6, "(0,2)": 15.1, "(0,3)": 14.8,
        "(1,2)": 10.7, "(1,3)": 13.5, "(2,3)": 12.6
      }
    }
  },  
  "preprocessing": {  
    "rotate_image": false,
    "pre_roi": [[70, 75], [600, 1240]],
    "roi": [[80, 75], [550, 1200]],
    "ppm": 30,
    "resolution": 1.0,
    "image_correction": {"apply": false, "k1": 0.0, "c": 0.0, "f": 0.0}
  },  
  "otv": {"max_features": 500, "lines_width": 5},  
  "stiv": {  
    "window_shape": [51, 51],
    "filter_window": 64,
    "overlap": 0,
    "lines_range": [[75, 1150], [100, 1100], [125, 1050]],
    "polar_filter_width": 20
  },
  "water_level": {
    "roi": [[180, 200], [380, 1000]],
    "roi2": [[150, 150], [350, 950]],
    "kernel_size": 5,
    "buffer_length": 10
  },
  "lines": [200, 280, 360],
  "water_flow": {"area": 8.0}
}
```

### 关键参数优化对比

| 参数 | 原值 | 优化值 | 优化理由 |
|------|------|--------|----------|
| `ppm` | 25 | 30 | 提高像素密度，适应5米海拔视角 |
| `lines` | [250,300,350] | [200,280,360] | 移至河道中心，避开边界效应 |
| `lines_range` | [[100,1200],...] | [[75,1150],...] | 适应从左上到右下的河道走向 |
| `polar_filter_width` | 18 | 20 | 增强0.05-0.5m/s慢流速敏感度 |
| `roi` Y起始 | 100 | 75 | 更好覆盖稳定化区域 |
| `water_level.roi` | [[200,300],...] | [[180,200],...] | 精确水位检测区域 |

## 技术改进要点

### 1. GCP地面控制点配置
- **经纬度转换**：使用Haversine公式计算4个控制点间的6对距离
- **透视校正**：启用GCP校正提高测量精度
- **坐标系统**：正确处理WGS84到像素坐标的转换

### 2. 非水平河道适配
- **流向识别**：河道从左上到右下的实际走向
- **lines_range设计**：渐变X范围适应河道形状
- **分析线位置**：移动到河道中心区域

### 3. 慢流速场景优化
- **polar_filter_width增强**：从默认10提升到20
- **时段选择**：自动截取10-40秒稳定片段
- **参数协调**：ppm、ROI、水位检测区域的协调优化

### 4. 自动视频处理
- **长度检测**：自动识别超过40秒的视频
- **智能截取**：选择10-40秒中间稳定时段
- **性能提升**：处理时间从939秒降至约300秒

## 使用指南

### 基本运行
```bash
# 运行优化配置（默认启用自动截取）
python awive_calculation.py
```

### 功能控制
- **自动截取**：默认启用，可通过修改`auto_trim=False`禁用
- **配置文件**：已自动设置为`config/awive_config_yuwangling.json`
- **目标视频**：已自动设置为`data/video/0812_speed_720.mp4`

### 结果验证
```bash
# 查看可视化结果
ls data/output/0812_speed_720_visualization_*.png

# 查看处理过程
ls data/output/images/

# 测试自动截取功能
python src/utils/video_auto_trimmer.py
```

## 预期效果

### 性能改进
- **处理速度**：提升60-70%（939秒→300秒）
- **内存使用**：显著降低
- **稳定性**：选择中间时段提高分析稳定性

### 精度提升
- **检测覆盖率**：ROI优化提高水面区域覆盖
- **流向适配**：更好捕获非水平流向分量
- **慢流速敏感度**：参数优化提升检测能力

### 结果预期
- **流速范围**：0.05-0.5 m/s
- **检测区域**：3个分析区域都应有有效检测
- **流向**：负值表示与预设方向相反（正常现象）

## 故障排除

### 常见问题
1. **处理时间过长**：确认auto_trim功能已启用
2. **检测区域无效**：检查ROI是否覆盖水面
3. **流速值异常**：验证ppm和GCP配置
4. **可视化异常**：双重标记为正常现象

### 调试方法
- 查看可视化图像验证ROI设置
- 检查控制台输出的坐标调整信息
- 对比优化前后的结果差异

### 参数微调
如果结果不理想，可调整：
- `polar_filter_width`：15-25范围内调整
- `lines`位置：根据河道中心微调
- `ppm`值：根据实际场景调整

---

**配置版本**：v2.0 优化版  
**适用场景**：雨汪岭视频流速估算  
**最后更新**：2025-09-03
