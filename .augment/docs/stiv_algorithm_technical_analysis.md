# STIV算法技术分析文档

## 算法概述

STIV (Space-Time Image Velocimetry) 是一种基于时空图像分析的流速测量算法，通过分析视频序列中特定线条上的像素变化模式来估算水流速度。

## 核心原理

### 理论基础
STIV算法基于以下物理原理：
1. **时空图像构建**：将视频中每一帧的特定水平线提取出来，按时间顺序排列形成时空图像
2. **模式识别**：水流运动在时空图像中形成特定的倾斜条纹模式
3. **角度计算**：条纹的倾斜角度与流速成正比关系
4. **速度转换**：通过几何关系将角度转换为实际流速

### 数学模型
```
velocity = tan(angle) * fps / ppm / resolution
```
其中：
- `angle`: 时空图像中条纹的倾斜角度（弧度）
- `fps`: 视频帧率
- `ppm`: 像素每米比例
- `resolution`: 分辨率缩放因子

## 代码架构分析

### 主要类和组件

#### 1. STIV类 (`lib/awive/awive/algorithms/sti.py`)
**核心职责**：STIV算法的主要实现类

**关键属性**：
```python
class STIV:
    def __init__(self, config, loader, formatter, lines, images_dp=None):
        self.lines = [int(line * formatter.resolution) for line in lines]
        self.lines_range = [(int(lrange[0] * formatter.resolution), 
                            int(lrange[1] * formatter.resolution)) 
                           for lrange in config.lines_range]
        self.stis_qnt = len(lines)  # STI图像数量
        self._filter_win = w_mn * w_mn.T  # 滤波窗口
        self._polar_filter_width = config.polar_filter_width
```

#### 2. 配置类 (`lib/awive/awive/config.py`)
**STIV配置参数**：
```python
class Stiv(BaseModel):
    window_shape: tuple[int, int] = (51, 51)  # GMT方法的窗口大小
    filter_window: int                        # 滤波窗口大小
    overlap: int = 31                         # GMT方法的重叠度
    ksize: int = 7                           # GMT方法的核大小
    polar_filter_width: int                   # 极坐标滤波宽度
    lines_range: list[tuple[int, int]]        # 每条线的X坐标范围
```

## 算法处理流程

### 1. 时空图像生成 (`generate_st_images`)
```python
def generate_st_images(self) -> list[NDArray]:
    stis = [[] for _ in range(self.stis_qnt)]  # 初始化STI列表
    
    while self.loader.has_images():
        image = self.loader.read()
        # 图像预处理流程
        image = self.formatter.apply_distortion_correction(image)
        image = self.formatter.apply_roi_extraction(image)
        image = self.formatter.apply_resolution(image)
        
        # 提取每条线的像素行
        for i, (line_pos, line_range) in enumerate(zip(self.lines, self.lines_range)):
            row = image[line_pos, line_range[0]:line_range[1]]
            stis[i].append(row)
    
    return [np.array(sti) for sti in stis]
```

**关键步骤**：
1. **失真校正**：应用镜头失真校正
2. **ROI提取**：提取感兴趣区域
3. **分辨率调整**：应用分辨率缩放
4. **线条提取**：从每帧中提取指定位置的像素行
5. **STI构建**：将提取的行按时间顺序排列

### 2. STI滤波处理 (`_filter_sti`)
基于论文方法的改进滤波算法：

```python
def _filter_sti(self, sti: np.ndarray) -> NDArray:
    # 1. 裁剪和调整大小
    x = min(sti.shape)
    sti = sti[:, :x] if x == sti.shape[0] else sti[:x, :]
    sti = cv2.resize(sti, (600, 600), interpolation=cv2.INTER_LINEAR)
    
    # 2. 窗口函数滤波
    sti = self._conv2d(sti, self._filter_win)
    
    # 3. 傅里叶变换和频域滤波
    sti_ft = np.abs(np.fft.fftshift(np.fft.fft2(sti)))
    # 滤除垂直和水平模式
    c_x, c_y = int(sti_ft.shape[0]/2), int(sti_ft.shape[1]/2)
    sti_ft[c_x-self._vh_filter:c_x+self._vh_filter, :] = 0
    sti_ft[:, c_y-self._vh_filter:c_y+self._vh_filter] = 0
    
    # 4. 极坐标变换
    sti_ft_polar = self._to_polar_system(sti_ft)
    
    # 5. 极坐标域滤波
    polar_mask = self._generate_polar_mask(sti_ft_polar)
    sti_ft_polar = sti_ft_polar * polar_mask
    
    # 6. 逆变换
    sti_ft_filtered = self._to_polar_system(sti_ft_polar, "invert")
    sti_filtered = np.abs(np.fft.ifft2(np.fft.ifftshift(sti_ft_filtered)))
    
    return sti_filtered.astype(np.uint8)
```

### 3. 运动方向计算

#### 方法A：FFT方法 (`_calculate_mot_using_fft`)
```python
def _calculate_mot_using_fft(self, sti: NDArray) -> tuple[float, NDArray]:
    # 1. 边缘检测
    sti_canny = cv2.Canny(sti, 10, 10)
    
    # 2. 方形化处理
    sti_padd = self._squarify(sti_canny)
    
    # 3. FFT和极坐标变换
    sti_ft = np.abs(np.fft.fftshift(np.fft.fft2(sti_padd)))
    sti_ft_polar = self._to_polar_system(sti_ft)
    
    # 4. 积分谱分布分析
    isd = np.sum(sti_ft_polar.T, axis=0)
    freq, _ = self._get_main_freqs(isd)
    
    # 5. 角度和速度计算
    angle = 2 * math.pi * freq / sti_ft_polar.shape[0]
    velocity = self.get_velocity(angle)
    
    return velocity, mask
```

#### 方法B：GMT方法 (`_calculate_mot_using_gmt`)
```python
def _calculate_mot_using_gmt(self, sti: NDArray) -> tuple[float, NDArray]:
    window_width = int(self.config.window_shape[0] / 2)
    window_height = int(self.config.window_shape[1] / 2)
    
    angle_accumulated = 0
    c_total = 0
    
    # 滑动窗口处理
    for s in range(window_width, width - window_width, overlap):
        for e in range(window_height, height - window_height, overlap):
            image_window = sti[s-window_width:s+window_width, 
                              e-window_height:e+window_height]
            angle, coherence = self._process_sti(image_window)
            angle_accumulated += angle * coherence
            c_total += coherence
    
    mean_angle = angle_accumulated / c_total
    velocity = self.get_velocity(mean_angle)
    
    return velocity, mask
```

## 关键参数分析

### 1. polar_filter_width
**作用**：控制极坐标域滤波的宽度
**影响**：
- 较小值：对高频噪声敏感，适合快流速
- 较大值：平滑效果强，适合慢流速
- 推荐范围：10-25

### 2. lines_range
**作用**：定义每条分析线的X坐标范围
**配置策略**：
- 水平河道：固定范围 `[[x1,x2], [x1,x2], [x1,x2]]`
- 非水平河道：渐变范围 `[[x1,x2], [x1',x2'], [x1'',x2'']]`

### 3. filter_window
**作用**：控制窗口函数滤波的大小
**影响**：影响频域滤波的精度和计算复杂度

### 4. window_shape (GMT方法)
**作用**：定义GMT方法中滑动窗口的大小
**权衡**：窗口越大越稳定，但空间分辨率降低

## 算法优缺点分析

### 优点
1. **非接触式测量**：无需在水中放置设备
2. **多点同时测量**：可同时分析多条线的流速
3. **实时性好**：处理速度相对较快
4. **适应性强**：可处理不同水流条件

### 缺点
1. **对图像质量敏感**：需要清晰的水面纹理
2. **参数依赖性强**：需要针对场景调优参数
3. **假设限制**：假设流速在分析线上均匀
4. **光照影响**：强光反射或阴影会影响精度

## 核心问题确认

基于对当前实现的深入分析，确认以下四个核心问题需要解决：

### 4.1 坐标系统兼容性问题
**问题描述**：当前标定只支持像素位置+现实世界距离，无法兼容经纬度坐标系统
**现状分析**：
- 当前使用GCP（Ground Control Points）进行标定，格式为像素坐标+距离信息
- 虽然项目中有经纬度坐标的使用，但STIV算法本身不支持经纬度坐标系统
- 需要支持两种坐标系统的无缝切换

### 4.2 视频时间段选择问题
**问题描述**：对于较长视频，需要支持指定分析的开始和结束时间点
**现状分析**：
- 项目有视频裁剪功能（video_trimmer.py），但这是外部预处理
- STIV算法内部没有时间段选择功能，必须处理整个视频
- 需要在算法内部集成时间段选择功能

### 4.3 像素比例自动计算问题
**问题描述**：当前ppm（像素每米比例）需要手工配置，但理论上有了现场标定位置后应该可以自动计算
**现状分析**：
- 当前ppm需要手工配置（如"ppm": 0.01或"ppm": 30）
- 虽然有GCP标定信息（像素坐标+实际距离），但算法没有利用这些信息自动计算ppm
- 需要实现基于标定信息的ppm自动计算

### 4.4 分析线方向适配问题
**问题描述**：当前的'lines'和'lines_range'配置方式只适用于水流方向为水平的视频，无法适配不同角度的水流方向
**现状分析**：
- 当前lines_range配置方式假设水平流向
- 例如：`"lines_range": [[700, 1700], [700, 1700], [700, 1700]]`表示三条水平线都使用相同的X坐标范围
- 无法直观地适配不同角度的水流方向

## 通用视频预处理模块重构方案

基于对`lib/pyorc`库的深入分析，以下是设计通用预处理模块的完整架构方案，该模块将作为独立的预处理层，与具体的流速测量算法（PIV/OTV/STIV）解耦。

### PyORC架构分析与借鉴

#### 1. PyORC核心设计理念

**1.1 模块化分层架构**：
```
pyorc/
├── api/                    # 高级API接口层
│   ├── video.py           # 视频处理核心类
│   ├── frames.py          # 帧数据处理
│   ├── cameraconfig.py    # 相机配置管理
│   ├── velocimetry.py     # 流速测量接口
│   └── mask.py            # 掩码处理
├── cv.py                  # OpenCV底层函数封装
├── helpers.py             # 工具函数
└── velocimetry/           # 具体算法实现
    ├── ffpiv.py          # FFT-PIV算法
    └── openpiv.py        # OpenPIV算法
```

**1.2 关键设计模式**：
- **配置驱动**：所有参数通过CameraConfig统一管理
- **延迟加载**：支持lazy loading和chunked processing
- **接口统一**：通过xarray.DataArray提供统一的数据接口
- **算法解耦**：预处理与算法实现完全分离

#### 2. PyORC相机校正体系

**2.1 完整的相机参数模型**：
```python
class CameraConfig:
    def __init__(self,
        # 基础参数
        height: int, width: int,

        # 相机内参
        camera_matrix: List[List[float]] = None,  # 3x3内参矩阵
        dist_coeffs: List[List[float]] = None,    # 5个畸变系数

        # 相机外参
        rvec: List[float] = None,                 # 旋转向量
        tvec: List[float] = None,                 # 平移向量
        lens_position: List[float] = None,        # 镜头位置

        # 地理参考
        crs: Any = None,                          # 坐标参考系统
        gcps: Dict = None,                        # 地面控制点

        # 自动标定
        calibration_video: str = None,            # 标定视频

        # 处理参数
        resolution: float = 0.05,                 # 投影分辨率
        window_size: int = 10,                    # 窗口大小
        stabilize: List[List] = None,             # 稳定化区域
    )
```

**2.2 自动化校正流程**：
```python
# 1. 自动相机标定
camera_matrix, dist_coeffs = cv.calibrate_camera(
    calibration_video, chessboard_size=(9,6)
)

# 2. PnP求解外参
success, rvec, tvec = cv.solvepnp(
    dst_points, src_points, camera_matrix, dist_coeffs
)

# 3. 图像去畸变
undistorted_frame = cv.undistort_img(
    frame, camera_matrix, dist_coeffs
)

# 4. 坐标系转换
projected_coords = camera_config.project_grid(
    xs, ys, zs, swap_y_coords=True
)
```

#### 3. PyORC视频处理架构

**3.1 Video类的核心功能**：
```python
class Video:
    def __init__(self, fn, camera_config, start_frame, end_frame,
                 freq=1, chunksize=20, lazy=True, stabilize=None):
        # 视频文件管理
        # 相机配置集成
        # 帧范围控制
        # 分块处理支持
        # 稳定化处理

    def get_frames(self) -> xr.DataArray:
        # 返回标准化的帧数据数组

    def get_piv_coords(self) -> tuple:
        # 获取PIV坐标网格
```

**3.2 Frames数据处理**：
```python
@xr.register_dataarray_accessor("frames")
class Frames:
    def project(self) -> xr.DataArray:
        # 投影变换

    def normalize(self) -> xr.DataArray:
        # 图像标准化

    def get_piv_coords(self) -> tuple:
        # PIV坐标计算
```

### 通用预处理模块架构设计

#### 1. 整体架构设计

**1.1 模块组织结构**：
```
src/preprocessing/
├── __init__.py
├── core/                           # 核心处理模块
│   ├── __init__.py
│   ├── video_processor.py          # 视频处理核心类
│   ├── frame_processor.py          # 帧处理器
│   ├── camera_corrector.py         # 相机校正器
│   └── coordinate_transformer.py   # 坐标转换器
├── config/                         # 配置管理
│   ├── __init__.py
│   ├── camera_config.py           # 相机配置类
│   ├── processing_config.py       # 处理配置类
│   └── config_validator.py        # 配置验证器
├── algorithms/                     # 算法接口
│   ├── __init__.py
│   ├── base_algorithm.py          # 算法基类
│   ├── piv_interface.py           # PIV算法接口
│   ├── otv_interface.py           # OTV算法接口
│   └── stiv_interface.py          # STIV算法接口
├── utils/                          # 工具函数
│   ├── __init__.py
│   ├── calibration.py             # 自动标定工具
│   ├── validation.py              # 数据验证工具
│   └── visualization.py           # 可视化工具
└── adapters/                       # 适配器层
    ├── __init__.py
    ├── legacy_adapter.py          # 旧版配置适配器
    └── pyorc_adapter.py           # PyORC兼容适配器
```

**1.2 核心类设计**：

```python
# src/preprocessing/core/video_processor.py
class VideoProcessor:
    """通用视频预处理器"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.camera_corrector = CameraCorrector(config.camera)
        self.coordinate_transformer = CoordinateTransformer(config.coordinate)
        self.frame_processor = FrameProcessor(config.frame)

    def process_video(self, video_path: str,
                     algorithm_interface: BaseAlgorithm) -> Dict:
        """处理视频并调用指定算法"""
        # 1. 视频加载和验证
        # 2. 帧提取和预处理
        # 3. 相机校正
        # 4. 坐标转换
        # 5. 算法调用
        # 6. 结果后处理

    def get_processed_frames(self, video_path: str,
                           time_range: Tuple[str, str]) -> np.ndarray:
        """获取预处理后的帧序列"""

    def get_coordinate_grid(self, algorithm_type: str) -> Dict:
        """获取算法所需的坐标网格"""
```

#### 2. 相机校正模块设计

**2.1 CameraCorrector类**：
```python
# src/preprocessing/core/camera_corrector.py
class CameraCorrector:
    """相机畸变校正器"""

    def __init__(self, camera_config: CameraConfig):
        self.config = camera_config
        self.camera_matrix = None
        self.dist_coeffs = None
        self.rvec = None
        self.tvec = None

        self._initialize_parameters()

    def _initialize_parameters(self):
        """初始化相机参数"""
        if self.config.method == "pyorc":
            self._init_pyorc_parameters()
        elif self.config.method == "auto_calibrate":
            self._auto_calibrate()
        elif self.config.method == "manual":
            self._init_manual_parameters()

    def _auto_calibrate(self):
        """自动相机标定"""
        if self.config.calibration_video:
            self.camera_matrix, self.dist_coeffs = calibrate_camera(
                self.config.calibration_video,
                self.config.chessboard_size
            )

    def correct_frame(self, frame: np.ndarray) -> np.ndarray:
        """校正单帧图像"""
        if self.config.apply_distortion_correction:
            return cv2.undistort(frame, self.camera_matrix, self.dist_coeffs)
        return frame

    def get_camera_parameters(self) -> Dict:
        """获取相机参数"""
        return {
            "camera_matrix": self.camera_matrix,
            "dist_coeffs": self.dist_coeffs,
            "rvec": self.rvec,
            "tvec": self.tvec
        }
```

**2.2 配置格式设计**：
```yaml
# config/preprocessing_config.yaml
preprocessing:
  # 视频处理配置
  video:
    time_range: ["0:10", "0:40"]
    fps_target: null  # null表示使用原始fps
    frame_skip: 1     # 帧跳跃间隔

  # 相机校正配置
  camera:
    method: "pyorc"  # "pyorc" | "auto_calibrate" | "manual" | "none"

    # PyORC完整方案
    pyorc:
      camera_matrix:
        - [1500.0, 0.0, 640.0]
        - [0.0, 1500.0, 360.0]
        - [0.0, 0.0, 1.0]
      dist_coeffs:
        - [-0.2, 0.05, 0.001, 0.002, 0.0]
      rvec: [0.1, 0.2, 0.3]
      tvec: [0.0, 0.0, 5.0]
      lens_position: [100.0, 200.0, 10.0]

    # 自动标定方案
    auto_calibrate:
      calibration_video: "data/calibration/chessboard.mp4"
      chessboard_size: [9, 6]
      max_images: 30
      tolerance: 0.5

    # 手动配置方案
    manual:
      camera_matrix:
        - [1500.0, 0.0, 640.0]
        - [0.0, 1500.0, 360.0]
        - [0.0, 0.0, 1.0]
      dist_coeffs: [-0.2, 0.05, 0.001, 0.002, 0.0]

  # 坐标转换配置
  coordinate:
    method: "geographic"  # "pixel_distance" | "geographic" | "3d_projection"

    # 地理坐标系配置
    geographic:
      crs: "EPSG:32633"  # UTM Zone 33N
      gcps:  # 地面控制点
        src: [[100, 200], [300, 400], [500, 600], [700, 800]]
        dst: [[1000.0, 2000.0], [1100.0, 2100.0], [1200.0, 2200.0], [1300.0, 2300.0]]
        h_ref: 10.5  # 参考水位
        z_0: 100.0   # 全球参考高程
      resolution: 0.05  # 投影分辨率(m)

    # 像素-距离配置（向后兼容）
    pixel_distance:
      calibration_points:
        - pixel: [100, 200]
          real_distance: 5.0
        - pixel: [300, 400]
          real_distance: 10.0

  # 帧预处理配置
  frame:
    roi_crop: true
    roi_points: [[100, 100], [500, 100], [500, 400], [100, 400]]

    # 图像增强
    enhancement:
      apply: true
      clahe:
        clip_limit: 2.0
        tile_grid_size: [8, 8]
      gaussian_blur:
        kernel_size: 3
        sigma: 1.0

    # 稳定化
    stabilization:
      apply: false
      reference_region: [[50, 50], [150, 50], [150, 150], [50, 150]]
```

#### 3. 算法接口设计

**3.1 基础算法接口**：
```python
# src/preprocessing/algorithms/base_algorithm.py
from abc import ABC, abstractmethod

class BaseAlgorithm(ABC):
    """算法基类接口"""

    @abstractmethod
    def get_required_preprocessing(self) -> Dict:
        """返回算法所需的预处理配置"""
        pass

    @abstractmethod
    def process_frames(self, frames: np.ndarray,
                      coordinate_grid: Dict,
                      metadata: Dict) -> Dict:
        """处理预处理后的帧数据"""
        pass

    @abstractmethod
    def get_coordinate_requirements(self) -> Dict:
        """返回坐标系统要求"""
        pass

# src/preprocessing/algorithms/stiv_interface.py
class STIVInterface(BaseAlgorithm):
    """STIV算法接口"""

    def __init__(self, stiv_config: Dict):
        self.config = stiv_config

    def get_required_preprocessing(self) -> Dict:
        return {
            "frame_enhancement": True,
            "roi_crop": True,
            "coordinate_transform": True,
            "distortion_correction": True
        }

    def get_coordinate_requirements(self) -> Dict:
        return {
            "analysis_lines": True,
            "pixel_to_meter_ratio": True,
            "flow_direction": True
        }

    def process_frames(self, frames: np.ndarray,
                      coordinate_grid: Dict,
                      metadata: Dict) -> Dict:
        # 调用STIV处理器
        from src.analysis_algorithms.stiv import STIVProcessor

        processor = STIVProcessor(self.config)
        return processor.process(frames, coordinate_grid, metadata)
```

#### 4. 统一调用接口

**4.1 主处理器设计**：
```python
# src/preprocessing/__init__.py
class UniversalVideoProcessor:
    """通用视频处理器 - 统一入口"""

    def __init__(self, config_path: str):
        self.config = ProcessingConfig.from_file(config_path)
        self.video_processor = VideoProcessor(self.config)

    def process_with_algorithm(self, video_path: str,
                             algorithm_name: str,
                             algorithm_config: Dict) -> Dict:
        """使用指定算法处理视频"""

        # 1. 创建算法接口
        algorithm = self._create_algorithm_interface(
            algorithm_name, algorithm_config
        )

        # 2. 验证配置兼容性
        self._validate_config_compatibility(algorithm)

        # 3. 执行处理
        return self.video_processor.process_video(video_path, algorithm)

    def _create_algorithm_interface(self, name: str, config: Dict):
        """创建算法接口实例"""
        interfaces = {
            "stiv": STIVInterface,
            "piv": PIVInterface,
            "otv": OTVInterface
        }
        return interfaces[name](config)

    def get_preprocessing_preview(self, video_path: str,
                                frame_index: int = 0) -> np.ndarray:
        """获取预处理预览"""
        return self.video_processor.get_preview_frame(video_path, frame_index)
```

#### 5. 与现有算法的集成方案

**5.1 STIV算法集成**：
```python
# 修改 src/analysis_algorithms/stiv/stiv_processor.py
class STIVProcessor:
    def __init__(self, config):
        self.config = config
        # 移除内部的预处理逻辑

    def process(self, preprocessed_frames: np.ndarray,
               coordinate_grid: Dict,
               metadata: Dict) -> Dict:
        """处理预处理后的数据"""
        # 专注于STIV核心算法
        # 1. 生成分析线
        # 2. 提取STI
        # 3. 滤波处理
        # 4. 运动计算
        # 5. 结果输出
```

**5.2 PIV算法集成**：
```python
# 修改 src/analysis_algorithms/openpiv_piv_analyzer.py
def analyze_with_preprocessing(video_path: str, config: Dict) -> Dict:
    """使用新预处理模块的PIV分析"""

    # 1. 创建预处理器
    processor = UniversalVideoProcessor(config["preprocessing_config"])

    # 2. 处理视频
    return processor.process_with_algorithm(
        video_path, "piv", config["piv_config"]
    )
```

#### 6. 配置迁移和兼容性

**6.1 配置迁移工具**：
```python
# src/preprocessing/adapters/legacy_adapter.py
class LegacyConfigAdapter:
    """旧版配置适配器"""

    @staticmethod
    def migrate_stiv_config(old_config: Dict) -> Dict:
        """迁移STIV配置到新格式"""
        new_config = {
            "preprocessing": {
                "video": {
                    "time_range": old_config.get("time_range", ["0:00", "0:30"])
                },
                "camera": {
                    "method": "manual" if old_config.get("image_correction", {}).get("apply") else "none",
                    "manual": {
                        "camera_matrix": _convert_simple_to_matrix(old_config.get("image_correction", {})),
                        "dist_coeffs": _convert_simple_to_coeffs(old_config.get("image_correction", {}))
                    }
                },
                "coordinate": {
                    "method": "pixel_distance",
                    "pixel_distance": {
                        "calibration_points": old_config.get("calibration", [])
                    }
                }
            },
            "algorithm": {
                "stiv": old_config.get("stiv", {})
            }
        }
        return new_config
```

#### 7. 实施路径和优先级

**阶段1：核心架构搭建（2-3周）**
1. 创建预处理模块基础架构
2. 实现VideoProcessor和CameraCorrector核心类
3. 设计统一的配置格式
4. 实现基础的算法接口

**阶段2：STIV算法集成（1-2周）**
1. 重构STIV算法，移除内部预处理逻辑
2. 实现STIVInterface接口
3. 测试STIV算法在新架构下的功能
4. 验证结果一致性

**阶段3：PIV/OTV算法集成（2-3周）**
1. 重构PIV和OTV算法
2. 实现对应的算法接口
3. 统一坐标系统和数据格式
4. 性能优化和测试

**阶段4：高级功能实现（2-3周）**
1. 实现PyORC级别的相机校正功能
2. 添加自动标定和3D坐标转换
3. 实现配置迁移工具
4. 完善文档和示例

**阶段5：优化和完善（1-2周）**
1. 性能优化和内存管理
2. 错误处理和异常恢复
3. 用户界面和可视化工具
4. 最终测试和发布

### 技术优势和预期效果

#### 1. 架构优势
- **职责分离**：预处理与算法逻辑完全解耦
- **代码复用**：所有算法共享相同的预处理流程
- **标准化**：统一的接口和数据格式
- **可扩展性**：易于添加新算法和新功能

#### 2. 功能提升
- **精度提升**：基于PyORC的完整相机校正模型
- **自动化**：自动相机标定和参数优化
- **灵活性**：支持多种坐标系统和处理模式
- **兼容性**：保持与现有配置的兼容

#### 3. 维护性改进
- **模块化**：清晰的模块边界和职责划分
- **可测试性**：独立的模块便于单元测试
- **可配置性**：灵活的配置系统支持不同场景
- **可扩展性**：标准化接口便于功能扩展

这个重构方案将显著提升项目的架构质量、代码复用性和功能完整性，为未来的算法开发和功能扩展奠定坚实基础。

### 具体实施建议

#### 1. 项目结构重组

**1.1 新的目录结构**：
```
flowspeed/
├── config/
│   ├── preprocessing_config.yaml      # 新的预处理配置
│   ├── algorithms/                    # 算法专用配置
│   │   ├── stiv_config.yaml
│   │   ├── piv_config.yaml
│   │   └── otv_config.yaml
│   └── legacy/                        # 旧版配置（兼容性）
│       └── batch_config.yaml
├── src/
│   ├── preprocessing/                 # 新的预处理模块
│   │   ├── __init__.py
│   │   ├── core/
│   │   ├── config/
│   │   ├── algorithms/
│   │   ├── utils/
│   │   └── adapters/
│   ├── analysis_algorithms/           # 重构后的算法模块
│   │   ├── stiv/                     # 专注核心算法逻辑
│   │   ├── piv/                      # PIV算法重构
│   │   └── otv/                      # OTV算法重构
│   └── utils/                        # 通用工具（保留）
├── lib/                              # 外部库（保持不变）
├── data/
│   ├── calibration/                  # 新增：标定数据
│   │   ├── chessboard_videos/
│   │   └── camera_parameters/
│   └── ...
└── tests/                            # 测试目录
    ├── preprocessing/
    ├── algorithms/
    └── integration/
```

**1.2 核心文件创建清单**：

```python
# src/preprocessing/__init__.py
from .core.video_processor import VideoProcessor
from .config.processing_config import ProcessingConfig
from .algorithms.base_algorithm import BaseAlgorithm

class UniversalVideoProcessor:
    """统一视频处理入口"""
    pass

# src/preprocessing/core/video_processor.py
class VideoProcessor:
    """视频处理核心类"""
    pass

# src/preprocessing/core/camera_corrector.py
class CameraCorrector:
    """相机校正器"""
    pass

# src/preprocessing/core/coordinate_transformer.py
class CoordinateTransformer:
    """坐标转换器"""
    pass

# src/preprocessing/config/processing_config.py
class ProcessingConfig:
    """预处理配置管理"""
    pass

# src/preprocessing/algorithms/stiv_interface.py
class STIVInterface(BaseAlgorithm):
    """STIV算法接口"""
    pass
```

#### 2. 迁移策略

**2.1 渐进式迁移计划**：

**第一步：创建预处理模块骨架**
```bash
# 创建新模块结构
mkdir -p src/preprocessing/{core,config,algorithms,utils,adapters}
touch src/preprocessing/__init__.py
touch src/preprocessing/core/{__init__.py,video_processor.py,camera_corrector.py}
touch src/preprocessing/config/{__init__.py,processing_config.py}
touch src/preprocessing/algorithms/{__init__.py,base_algorithm.py,stiv_interface.py}
```

**第二步：实现核心预处理功能**
```python
# 先实现基础的视频加载和帧处理
# 然后逐步添加相机校正功能
# 最后实现坐标转换功能
```

**第三步：重构STIV算法**
```python
# 将STIV算法中的预处理逻辑移动到新模块
# 保持STIV核心算法逻辑不变
# 通过接口适配器连接新旧系统
```

**第四步：配置系统迁移**
```python
# 实现配置转换工具
# 支持旧配置格式的自动迁移
# 提供配置验证和错误提示
```

**2.2 兼容性保证**：
```python
# src/preprocessing/adapters/legacy_adapter.py
class LegacyAdapter:
    """旧版兼容适配器"""

    @staticmethod
    def run_legacy_batch_process(config_path: str):
        """运行旧版批处理流程"""
        # 1. 加载旧配置
        old_config = load_yaml(config_path)

        # 2. 转换为新配置格式
        new_config = LegacyConfigAdapter.migrate_config(old_config)

        # 3. 使用新系统处理
        processor = UniversalVideoProcessor(new_config)

        # 4. 保持输出格式兼容
        return processor.process_batch(old_config["videos"])
```

#### 3. 测试策略

**3.1 单元测试**：
```python
# tests/preprocessing/test_camera_corrector.py
class TestCameraCorrector:
    def test_pyorc_distortion_correction(self):
        """测试PyORC畸变校正"""

    def test_auto_calibration(self):
        """测试自动标定功能"""

    def test_coordinate_transformation(self):
        """测试坐标转换"""

# tests/preprocessing/test_video_processor.py
class TestVideoProcessor:
    def test_frame_extraction(self):
        """测试帧提取功能"""

    def test_roi_processing(self):
        """测试ROI处理"""
```

**3.2 集成测试**：
```python
# tests/integration/test_stiv_integration.py
class TestSTIVIntegration:
    def test_stiv_with_new_preprocessing(self):
        """测试STIV与新预处理模块的集成"""

    def test_result_consistency(self):
        """测试结果一致性（新旧系统对比）"""
```

**3.3 性能测试**：
```python
# tests/performance/test_preprocessing_performance.py
class TestPreprocessingPerformance:
    def test_processing_speed(self):
        """测试处理速度"""

    def test_memory_usage(self):
        """测试内存使用"""
```

#### 4. 文档和示例

**4.1 用户指南**：
```markdown
# docs/user_guide/preprocessing_guide.md
## 预处理模块使用指南

### 快速开始
```python
from src.preprocessing import UniversalVideoProcessor

# 1. 创建处理器
processor = UniversalVideoProcessor("config/preprocessing_config.yaml")

# 2. 处理视频
results = processor.process_with_algorithm(
    video_path="data/video/test.mp4",
    algorithm_name="stiv",
    algorithm_config=stiv_config
)
```

### 配置说明
...

### 算法集成
...
```

**4.2 开发者指南**：
```markdown
# docs/developer_guide/algorithm_integration.md
## 算法集成指南

### 创建新算法接口
```python
class MyAlgorithmInterface(BaseAlgorithm):
    def get_required_preprocessing(self) -> Dict:
        return {
            "distortion_correction": True,
            "coordinate_transform": True
        }

    def process_frames(self, frames, coordinate_grid, metadata):
        # 实现算法逻辑
        pass
```

### 配置格式规范
...
```

#### 5. 部署和发布

**5.1 版本管理**：
```
v2.0.0-alpha.1  # 预处理模块基础架构
v2.0.0-alpha.2  # STIV算法集成
v2.0.0-beta.1   # PIV/OTV算法集成
v2.0.0-rc.1     # 功能完整版本
v2.0.0          # 正式发布
```

**5.2 发布检查清单**：
- [ ] 所有单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试达标
- [ ] 文档完整
- [ ] 示例代码可运行
- [ ] 配置迁移工具测试
- [ ] 向后兼容性验证

### 总结

这个通用预处理模块重构方案基于PyORC的成熟架构设计，将为FlowSpeed项目带来以下核心价值：

1. **架构现代化**：从单体架构向模块化架构转变
2. **功能标准化**：统一的预处理流程和接口规范
3. **精度提升**：完整的相机校正和坐标转换体系
4. **开发效率**：算法开发者可专注核心逻辑
5. **维护性**：清晰的模块边界和职责分离
6. **扩展性**：标准化接口便于新算法集成

通过分阶段实施和渐进式迁移，可以在保证项目稳定性的同时，逐步实现架构升级，为项目的长期发展奠定坚实基础。

### 新架构设计
在`src/analysis_algorithms`目录下创建新的STIV实现：

```
src/analysis_algorithms/
├── stiv_v2/
│   ├── __init__.py
│   ├── coordinate_system.py      # 坐标系统管理
│   ├── time_manager.py          # 时间段管理
│   ├── analysis_lines.py        # 分析线管理
│   ├── stiv_core.py            # 核心STIV算法
│   ├── stiv_config.py          # 配置管理
│   ├── stiv_processor.py       # 主处理器
│   └── utils.py                # 工具函数
└── stiv_analyzer.py            # 统一接口
```

### 配置格式说明

STIV v2使用统一的YAML配置格式，详细的配置说明请参考：
- **配置文件格式指南**: `.augment/docs/config_format_guide.md`
- **示例配置文件**: `config/batch_config.yaml`

主要配置包括：
- **坐标系统配置**: 支持像素+距离和经纬度两种坐标系统
- **分析线配置**: 支持自适应和手动两种模式，新增center_point参数
- **算法参数**: 包含FFT和GMT两种运动计算方法
  - `method`: 运动计算方法配置，"fft"或"gmt"
  - 原始STIV算法只使用FFT方法（硬编码）
  - 当前实现增加了GMT方法选项，提供更多算法对比和研究可能性
  - 默认使用"fft"保持与原始算法的一致性
- **预处理参数**: 图像旋转、ROI、分辨率等预处理选项

### 实施计划
**第一阶段**：基础架构
- 创建模块结构
- 实现CoordinateSystem类
- 实现配置管理和向后兼容性

**第二阶段**：核心功能
- 实现TimeManager
- 实现AnalysisLines
- 重构STI生成算法

**第三阶段**：算法优化
- 移植和改进滤波算法
- 移植运动计算算法
- 性能优化

**第四阶段**：集成和测试
- 创建统一接口
- 编写测试用例
- 文档更新

## 历史AI建议

以下是之前分析中提出的优化建议，作为参考保留：

### 1. 慢流速场景优化
- 增大 `polar_filter_width` (18-25)
- 选择稳定的中间时段分析
- 优化ROI区域选择

### 2. 非水平河道适配
- 调整 `lines_range` 适应河道走向
- 考虑河道角度对速度分量的影响
- 优化分析线的位置选择

### 3. 噪声抑制
- 改进滤波算法
- 增强边缘检测的鲁棒性
- 多尺度分析融合

### 4. 实时性提升
- 优化FFT计算
- 并行处理多条STI
- 自适应参数调整

### 5. 模块化设计建议
```python
class STIVProcessor:
    def __init__(self, config):
        self.sti_generator = STIGenerator(config)
        self.sti_filter = STIFilter(config)
        self.motion_calculator = MotionCalculator(config)

    def process(self, video_path):
        stis = self.sti_generator.generate(video_path)
        filtered_stis = [self.sti_filter.filter(sti) for sti in stis]
        velocities = [self.motion_calculator.calculate(sti) for sti in filtered_stis]
        return velocities
```

### 6. 参数自适应
- 基于图像特征自动调整参数
- 多参数组合的效果评估
- 场景识别和参数推荐

### 7. 算法融合
- 结合FFT和GMT方法的优势
- 多尺度分析
- 置信度评估和结果融合

## STIV算法技术实现细节分析

基于当前重构版本的深入代码分析，以下是对STIV算法技术实现的详细解释：

### 1.1 STI形状计算逻辑

**问题**：当前处理720×1280分辨率的视频，为什么生成的STI形状是(900, 565)？

**解答**：
STI（Space-Time Image）的形状由两个维度决定：

1. **时间维度（第一维：900）**：
   - 来源：处理的视频帧数
   - 计算：根据配置的时间范围`['0:10', '0:40']`（30秒）和视频帧率计算
   - 公式：`帧数 = (结束时间 - 开始时间) × 帧率 = 30秒 × 30fps = 900帧`

2. **空间维度（第二维：565）**：
   - 来源：分析线的实际像素长度
   - 配置：`line_length: 600`像素
   - 实际：565像素（由于边界裁剪和坐标约束）
   - 原因：分析线在图像边界附近时，实际可提取的像素数会少于配置长度

**代码实现**：
```python
# 在STIGenerator.generate_stis()中
for i, line in enumerate(self.analysis_lines.lines):
    line_pixels = self.analysis_lines.get_line_pixels(line, frame)  # 提取565个像素
    stis[i].append(line_pixels)  # 每帧添加一行，最终900行
```

### 1.2 图像裁剪和调整逻辑

**问题**：解释为什么STI会从(900, 565)裁剪为(565, 565)的正方形，以及随后调整为(600, 600)的原因。

**解答**：

1. **裁剪为正方形 (900, 565) → (565, 565)**：
   - 目的：为后续的FFT和极坐标转换做准备
   - 逻辑：`x = min(sti.shape) = min(900, 565) = 565`
   - 实现：保留较小维度的尺寸，裁剪较大维度
   - 代码：`sti_cropped = sti[:, :x] if x == sti.shape[0] else sti[:x, :]`

2. **调整为标准尺寸 (565, 565) → (600, 600)**：
   - 目的：统一处理尺寸，符合论文标准
   - 原因：600×600是STIV论文中推荐的标准处理尺寸
   - 好处：确保滤波窗口和算法参数的一致性
   - 代码：`cv2.resize(sti_cropped, (600, 600), interpolation=cv2.INTER_LINEAR)`

**算法依据**：
- 正方形图像便于进行旋转不变的频域分析
- 标准尺寸确保滤波参数的有效性和可重复性

### 1.3 极坐标转换前的尺寸变化

**问题**：说明为什么在极坐标转换之前图像尺寸又变成了(537, 537)。

**解答**：

这个尺寸变化发生在频域滤波过程中：

1. **输入尺寸**：(600, 600) - 经过窗口滤波的STI
2. **FFT处理**：`np.fft.fft2(sti_windowed)` - 保持(600, 600)
3. **频域滤波**：去除垂直和水平模式后仍为(600, 600)
4. **极坐标转换输入**：(600, 600)
5. **极坐标转换输出**：(537, 537)

**尺寸变化原因**：
- OpenCV的`cv2.linearPolar`函数内部算法决定
- 计算公式：`max_radius = int(np.sqrt(row² + col²) / 2)`
- 对于600×600：`max_radius = int(np.sqrt(600² + 600²) / 2) = 424`
- 输出尺寸由OpenCV内部优化算法确定，通常为`(max_radius + padding, 角度分辨率)`

**目的**：
- 优化极坐标表示的精度和效率
- 避免边界效应和插值误差

### 1.4 极坐标转换原理

**问题**：详细解释什么是极坐标转换，在STIV算法中的作用和意义。

**解答**：

**极坐标转换定义**：
将笛卡尔坐标系(x, y)转换为极坐标系(r, θ)的数学变换：
- r = √(x² + y²)：距离中心的径向距离
- θ = arctan(y/x)：相对于水平轴的角度

**在STIV中的作用**：

1. **频域分析优化**：
   - 将频域中的径向和角度信息分离
   - 便于分析特定方向的频率成分
   - 简化角度相关的滤波操作

2. **运动方向提取**：
   - 水流运动在时空图像中形成倾斜条纹
   - 条纹在频域中表现为特定角度的能量集中
   - 极坐标转换将角度信息映射到一个维度上

3. **噪声抑制**：
   - 通过极坐标域的选择性滤波
   - 保留主要运动方向，抑制随机噪声
   - 提高信噪比和检测精度

**物理意义**：
- 径向分量(r)：对应频率的幅度
- 角度分量(θ)：对应运动的方向
- 极坐标滤波：选择性保留特定角度范围的频率成分

**代码实现**：
```python
def _to_polar_system(img: np.ndarray, option: str = "convert") -> NDArray:
    row, col = img.shape
    cent = (int(col / 2), int(row / 2))  # 中心点
    max_radius = int(np.sqrt(row**2 + col**2) / 2)  # 最大半径
    return cv2.linearPolar(img, cent, max_radius, flag)
```

### 1.5 调试图像含义分析

**问题**：解释'sti_00_filtered_20250904_093949.png'系列中4条分析线的original、filtered、result图像的实际物理含义和用途。

**解答**：

**1. Original图像（sti_XX_original_YYYYMMDD_HHMMSS.png）**：
- **物理含义**：原始时空图像，显示水流在特定分析线上的时间-空间变化模式
- **数据内容**：每行代表一个时间点，每列代表分析线上的一个空间位置
- **视觉特征**：
  - 水平条纹：静态物体或背景
  - 倾斜条纹：运动物体（水流、漂浮物等）
  - 条纹倾斜角度：与流速成正比
- **用途**：评估原始数据质量，识别明显的运动模式

**2. Filtered图像（sti_XX_filtered_YYYYMMDD_HHMMSS.png）**：
- **物理含义**：经过多级滤波处理的时空图像，去除了噪声和不相关的频率成分
- **处理过程**：
  - 窗口函数滤波：平滑处理
  - 频域滤波：去除垂直和水平模式
  - 极坐标滤波：选择性保留特定方向的信息
- **视觉特征**：
  - 更清晰的运动条纹
  - 减少的背景噪声
  - 增强的信噪比
- **用途**：验证滤波效果，确保保留了主要运动信息

**3. Result图像（sti_XX_result_YYYYMMDD_HHMMSS.png）**：
- **物理含义**：运动检测结果的可视化，叠加了算法检测到的运动方向信息
- **数据内容**：滤波后的STI + 角度掩码（mask）
- **视觉特征**：
  - 基础图像：滤波后的STI
  - 叠加元素：检测到的运动方向线条或区域
  - 颜色编码：不同颜色表示不同的运动方向或置信度
- **用途**：
  - 验证运动检测算法的准确性
  - 调试角度计算和方向识别
  - 评估算法在不同条件下的性能

**4条分析线的对比意义**：
- **空间分布**：不同位置的流速变化
- **一致性检验**：多条线结果的相互验证
- **流场分析**：整体流场的空间分布特征
- **异常检测**：识别局部异常或测量误差

### 1.6 配置参数解释

**问题**：解释当前配置文件中'k1'、'c'、'f'这三个参数的物理含义、作用机制和取值依据。

**解答**：

这三个参数位于`image_correction`配置中，用于镜头失真校正：

**1. k1参数（径向失真系数）**：
- **物理含义**：描述镜头径向失真的主要系数
- **作用机制**：
  - k1 > 0：桶形失真（图像边缘向内弯曲）
  - k1 < 0：枕形失真（图像边缘向外弯曲）
  - k1 = 0：无径向失真
- **数学模型**：`r_corrected = r * (1 + k1*r² + k2*r⁴ + ...)`
- **取值依据**：
  - 通过相机标定获得
  - 典型范围：-0.5 到 0.5
  - 当前配置：0.0（未启用失真校正）

**2. c参数（失真中心偏移）**：
- **物理含义**：失真中心相对于图像中心的偏移量
- **作用机制**：
  - 校正镜头光轴与图像中心的偏差
  - 影响失真校正的基准点位置
- **数学模型**：失真中心 = 图像中心 + c
- **取值依据**：
  - 相机标定过程中确定
  - 通常为像素单位的小数值
  - 当前配置：0.0（假设失真中心在图像中心）

**3. f参数（焦距参数）**：
- **物理含义**：相机的有效焦距，用于失真校正计算
- **作用机制**：
  - 确定失真校正的尺度因子
  - 影响径向距离的计算
  - 与像素尺寸和实际焦距相关
- **数学模型**：`r_normalized = r / f`
- **取值依据**：
  - 相机内参标定获得
  - 单位：像素
  - 典型值：几百到几千像素
  - 当前配置：0.0（未启用失真校正）

**配置状态分析**：
当前配置中`apply: false`，所有参数均为0.0，表示：
- 未启用镜头失真校正
- 假设使用的相机失真可忽略
- 或者视频已经过预处理校正

**启用建议**：
如果发现分析结果在图像边缘区域不准确，建议：
1. 进行相机标定获取准确参数
2. 启用失真校正：`apply: true`
3. 设置合适的k1、c、f值

## 相机畸变校正方案设计

基于对`lib/pyorc`库的深入分析，以下是优化当前STIV算法中图像校正功能的技术方案：

### PyORC相机畸变处理技术原理

#### 1. PyORC方案的核心参数体系

**1.1 相机内参矩阵 (camera_matrix)**：
```python
# 3x3相机内参矩阵
camera_matrix = [
    [fx,  0, cx],
    [ 0, fy, cy],
    [ 0,  0,  1]
]
```
- `fx, fy`：x和y方向的焦距（像素单位）
- `cx, cy`：主点坐标（光轴与图像平面的交点）
- 物理意义：描述相机的内在几何特性

**1.2 畸变系数 (dist_coeffs)**：
```python
# 5个畸变系数的完整模型
dist_coeffs = [[k1], [k2], [p1], [p2], [k3]]
```
- `k1, k2, k3`：径向畸变系数
- `p1, p2`：切向畸变系数
- 数学模型：
  ```
  x_corrected = x * (1 + k1*r² + k2*r⁴ + k3*r⁶) + 2*p1*x*y + p2*(r² + 2*x²)
  y_corrected = y * (1 + k1*r² + k2*r⁴ + k3*r⁶) + p1*(r² + 2*y²) + 2*p2*x*y
  ```

**1.3 外参向量 (rvec, tvec)**：
- `rvec`：3×1旋转向量（Rodrigues表示）
- `tvec`：3×1平移向量
- 作用：描述相机在世界坐标系中的位置和姿态

#### 2. PyORC校正算法流程

**2.1 自动相机标定**：
```python
def calibrate_camera(video_path, chessboard_size=(9,6)):
    # 1. 检测棋盘格角点
    # 2. 收集多帧角点数据
    # 3. 调用cv2.calibrateCamera()
    # 4. 返回camera_matrix和dist_coeffs
    return camera_matrix, dist_coeffs
```

**2.2 PnP求解**：
```python
def solvepnp(dst, src, camera_matrix, dist_coeffs):
    # 使用已知的3D-2D点对应关系
    # 求解相机的外参rvec和tvec
    return cv2.solvePnP(dst, src, camera_matrix, dist_coeffs)
```

**2.3 图像去畸变**：
```python
def undistort_img(img, camera_matrix, dist_coeffs):
    return cv2.undistort(img, camera_matrix, dist_coeffs)
```

### 与当前STIV简化参数的对比分析

#### 当前STIV方案的局限性

**当前参数模型**：
```yaml
image_correction:
  apply: false
  k1: 0.0    # 仅一个径向畸变系数
  c: 0.0     # 失真中心偏移
  f: 0.0     # 焦距参数
```

**主要问题**：
1. **参数不完整**：只有k1一个径向畸变系数，缺少k2、k3和切向畸变p1、p2
2. **缺少相机内参**：没有完整的3×3相机矩阵
3. **缺少外参支持**：无法处理相机姿态变化
4. **校正算法简化**：未实现完整的OpenCV畸变校正流程
5. **缺少自动标定**：需要手动设置参数，精度难以保证

#### PyORC方案的优势

1. **完整的畸变模型**：支持径向和切向畸变的完整数学模型
2. **自动相机标定**：通过棋盘格视频自动获取精确参数
3. **3D几何支持**：支持真实世界坐标系的转换
4. **成熟的算法实现**：基于OpenCV的标准计算机视觉算法
5. **高精度校正**：通过优化算法最小化重投影误差

### 集成PyORC方案到STIV算法的实施建议

#### 1. 配置文件格式改进

**新的配置格式**：
```yaml
preprocessing:
  image_correction:
    # 基础控制
    apply: true
    method: "pyorc"  # "simple" | "pyorc"

    # PyORC完整方案
    pyorc_config:
      # 相机内参矩阵 (3x3)
      camera_matrix:
        - [1500.0, 0.0, 640.0]
        - [0.0, 1500.0, 360.0]
        - [0.0, 0.0, 1.0]

      # 畸变系数 (5个)
      dist_coeffs:
        - [-0.2]   # k1: 径向畸变系数1
        - [0.05]   # k2: 径向畸变系数2
        - [0.001]  # p1: 切向畸变系数1
        - [0.002]  # p2: 切向畸变系数2
        - [0.0]    # k3: 径向畸变系数3

      # 外参（可选，用于3D校正）
      rvec: [0.1, 0.2, 0.3]  # 旋转向量
      tvec: [0.0, 0.0, 5.0]  # 平移向量

      # 自动标定（可选）
      calibration_video: "data/calibration/chessboard.mp4"
      chessboard_size: [9, 6]

    # 简化方案（向后兼容）
    simple_config:
      k1: 0.0
      c: 0.0
      f: 0.0
```

#### 2. 代码实现架构

**2.1 创建畸变校正模块**：
```python
# src/utils/distortion_correction.py
class DistortionCorrector:
    def __init__(self, config):
        self.method = config.get("method", "simple")
        if self.method == "pyorc":
            self._init_pyorc_corrector(config["pyorc_config"])
        else:
            self._init_simple_corrector(config["simple_config"])

    def correct_image(self, image):
        if self.method == "pyorc":
            return self._correct_pyorc(image)
        else:
            return self._correct_simple(image)
```

**2.2 集成到STIV预处理**：
```python
# 在stiv_processor.py的预处理函数中
def create_preprocessing_function(preprocessing_config):
    def preprocess_image(frame):
        # ... 现有预处理步骤 ...

        # 图像校正
        if preprocessing_config.image_correction.get("apply", False):
            corrector = DistortionCorrector(preprocessing_config.image_correction)
            processed = corrector.correct_image(processed)

        return processed
    return preprocess_image
```

#### 3. 实施路径和优先级

**阶段1：基础集成**
- 实现PyORC畸变校正的核心功能
- 支持手动配置camera_matrix和dist_coeffs
- 保持向后兼容性

**阶段2：自动标定**
- 集成棋盘格自动标定功能
- 实现标定视频处理流程
- 添加标定质量评估

**阶段3：高级功能**
- 支持3D几何校正（rvec, tvec）
- 实现实时畸变校正优化
- 添加多相机支持

#### 4. 向后兼容性考虑

**配置迁移策略**：
```python
def migrate_config(old_config):
    """将旧的简化配置迁移到新格式"""
    if "k1" in old_config:
        # 旧格式检测
        return {
            "method": "simple",
            "simple_config": {
                "k1": old_config["k1"],
                "c": old_config["c"],
                "f": old_config["f"]
            }
        }
    return old_config
```

**渐进式升级**：
1. 默认使用简化方案保持兼容性
2. 提供配置转换工具
3. 在文档中提供迁移指南
4. 保留简化方案作为备选

### 技术优势和预期效果

#### 1. 精度提升
- **完整畸变模型**：相比单一k1参数，5参数模型可处理复杂镜头畸变
- **自动标定**：消除手动参数设置的误差
- **重投影误差最小化**：通过优化算法确保最佳校正效果

#### 2. 适用性扩展
- **广角镜头支持**：能够处理严重的桶形/枕形畸变
- **多种相机类型**：支持不同焦距和传感器尺寸的相机
- **3D几何校正**：支持倾斜安装的相机

#### 3. 工程化改进
- **标准化流程**：基于成熟的OpenCV算法
- **可重复性**：标定参数可保存和复用
- **质量控制**：提供校正质量评估指标

### 实施建议总结

1. **优先实现PyORC核心校正功能**，提供显著的精度提升
2. **保持向后兼容性**，确保现有配置继续工作
3. **分阶段实施**，降低集成风险
4. **提供完整的迁移文档**，帮助用户升级配置
5. **建立标定数据库**，为常见相机型号提供预设参数

这个方案将显著提升STIV算法在复杂光学环境下的测量精度，特别是在使用广角镜头或相机安装角度较大的场景中。

## 分析线绘制模式重构

### 新增功能概述

为了解决不同场景下分析线绘制的需求，实现了3种可配置的绘制模式：

#### 模式1：默认模式（default）
- **功能**：保持现有的分析线绘制逻辑不变
- **特点**：使用固定长度限制，确保向后兼容性
- **适用场景**：标准的流速测量场景，已有成熟的配置参数

#### 模式2：无限延伸模式（infinite_with_roi）
- **功能**：基于中心点和角度生成尽可能长的分析线
- **ROI裁剪**：如果存在ROI区域，用ROI边界对分析线进行裁剪
- **回退机制**：如果没有ROI区域，回退到使用配置的长度限制
- **适用场景**：需要充分利用ROI区域的场景，提高分析线的有效长度

#### 模式3：智能分割模式（smart_segmentation）
- **功能**：在模式2基础上，对较长的分析线进行智能分割
- **分割策略**：根据 `optimal_length` 参数确定每段的最佳长度
- **间隔控制**：使用 `interval_distance` 参数控制分割后各段之间的垂直间隔
- **适用场景**：应对光线遮挡导致波纹特征不明显的问题，通过多条平行分析线获取更可靠的结果

### 配置参数说明

```yaml
analysis_lines:
  mode: "adaptive"
  drawing_mode: "smart_segmentation"  # 绘制模式选择
  flow_direction: 135.0
  line_count: 3
  line_spacing: 50
  line_length: 1000
  center_point: [800, 300]
  # 智能分割模式参数
  optimal_length: 200      # 分析线的最佳长度（像素）
  interval_distance: 30    # 分割后各段分析线之间的间隔距离（像素）
```

### 技术实现细节

#### 1. 几何计算模块（geometry_utils.py）
实现了以下核心算法：
- **Cohen-Sutherland算法**：线段与矩形的裁剪
- **Sutherland-Hodgman算法**：线段与多边形的裁剪
- **智能分割算法**：基于最佳长度和间隔距离的线段分割

#### 2. ROI裁剪支持
- **矩形ROI**：格式 `[[y1, x1], [y2, x2]]`
- **多边形ROI**：格式 `[[x1, y1], [x2, y2], [x3, y3], ...]`
- **自动识别**：根据坐标点数量自动判断ROI类型

#### 3. 智能分割逻辑
```python
# 分割段数计算
segments = max(1, int(math.ceil(line_length / optimal_length)))

# 垂直方向偏移计算
perp_angle = line_angle + math.pi / 2
offset = (i - (segments - 1) / 2) * interval_distance
```

### 向后兼容性

- **默认行为**：未指定 `drawing_mode` 时自动使用 "default" 模式
- **参数验证**：对新增参数进行有效性检查
- **错误处理**：提供清晰的错误信息和建议

### 测试覆盖

实现了完整的单元测试：
- 默认模式功能测试
- 无限延伸模式ROI裁剪测试
- 智能分割模式参数验证测试
- 配置验证和错误处理测试
- 向后兼容性测试

### 性能影响

- **模式1**：无性能影响，保持原有性能
- **模式2**：轻微的ROI裁剪计算开销
- **模式3**：可能生成更多分析线，增加后续STI处理时间，但提高分析可靠性

### 使用建议

1. **标准场景**：使用默认模式，保持现有配置
2. **ROI优化**：使用无限延伸模式，充分利用ROI区域
3. **复杂光照**：使用智能分割模式，提高分析鲁棒性
4. **参数调优**：
   - `optimal_length`：建议设置为200-400像素
   - `interval_distance`：建议设置为20-50像素

---

**文档版本**: v2.1
**分析基础**: src/analysis_algorithms/stiv/stiv_core.py, lib/pyorc/pyorc/cv.py, lib/pyorc/pyorc/api/
**重构方案**: 基于PyORC架构的通用预处理模块设计
**新增功能**: 分析线绘制模式重构（3种可配置模式）
**最后更新**: 2025-09-05
