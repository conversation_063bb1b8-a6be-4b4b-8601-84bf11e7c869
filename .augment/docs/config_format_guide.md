# 配置文件格式指南

本文档介绍项目中所有算法（PIV、OTV、STIV）的统一配置格式。

## 全局配置结构

项目使用统一的YAML配置文件`config/batch_config.yaml`，包含以下主要部分：

```yaml
# 全局调试配置
debug:
  save_debug_images: false
  log_level: "INFO"  # "DEBUG", "INFO", "WARNING", "ERROR"

# 文件目录相关配置
output_dir: "data/output"
result_csv: "flow_speed_results.csv"
pixel_to_meter: 0.0084  # 全局像素与米转换系数

# PIV算法参数
piv_params:
  # PIV算法配置...

# OTV算法参数
otv_params:
  # OTV算法配置...

# STIV算法参数
stiv_params:
  # STIV算法配置...
```

## PIV算法配置

```yaml
piv_params:
  window_size: 32
  overlap: 16
  search_area_size: 64
  dt: 0.1
  validation_method: "std_threshold"
  validation_threshold: 2.0
  interpolation_method: "linear"
  smoothing_method: "gaussian"
  smoothing_size: 3
```

### PIV参数说明
- `window_size`: 窗口大小（像素）
- `overlap`: 重叠度（像素）
- `search_area_size`: 搜索区域大小（像素）
- `dt`: 时间间隔
- `validation_method`: 验证方法
- `validation_threshold`: 验证阈值
- `interpolation_method`: 插值方法
- `smoothing_method`: 平滑方法
- `smoothing_size`: 平滑窗口大小

## OTV算法配置

```yaml
otv_params:
  # 光流算法参数
  optical_flow:
    method: "farneback"
    pyr_scale: 0.5
    levels: 3
    winsize: 15
    iterations: 3
    poly_n: 5
    poly_sigma: 1.2
    flags: 0
  
  # 轨迹跟踪参数
  tracking:
    max_corners: 100
    quality_level: 0.01
    min_distance: 10
    block_size: 3
    use_harris_detector: false
    k: 0.04
  
  # 速度计算参数
  velocity:
    time_window: 5
    min_track_length: 10
    outlier_threshold: 2.0
  
  # 特征点检测参数
  feature_params:
    maxCorners: 500
    qualityLevel: 0.05
    minDistance: 5
    blockSize: 7
```

### OTV参数说明
- `optical_flow`: 光流算法配置
- `tracking`: 轨迹跟踪配置
- `velocity`: 速度计算配置
- `feature_params`: 特征点检测配置

## 通用预处理配置

所有算法共享的预处理参数：

```yaml
# 通用预处理参数（所有算法共用）
preprocessing:
  rotate_image: false
  pre_roi: [[75, 600], [750, 1240]]  # 预处理ROI [[y1, x1], [y2, x2]] - 第一级裁剪
  roi: [[100, 650], [700, 1200]]     # 分析ROI [[y1, x1], [y2, x2]] - 第二级裁剪
  resolution: 1.0
  image_correction:
    apply: false
    k1: 0.0
    c: 0.0
    f: 0.0
```

### 预处理参数详解

#### ROI（感兴趣区域）配置
ROI配置用于限制算法的处理区域，提高计算效率并排除干扰区域。

**`pre_roi` - 预处理ROI（第一级裁剪）**
- **格式**: `[[y1, x1], [y2, x2]]` - 矩形区域的左上角和右下角坐标
- **用途**: 在图像预处理阶段进行第一级裁剪，移除图像边缘的无关区域
- **适用算法**: 主要用于STIV算法的矩形裁剪
- **示例**: `[[75, 600], [750, 1240]]` 表示从(600,75)到(1240,750)的矩形区域
- **设置为null**: 不进行预处理裁剪，使用完整图像

**`roi` - 分析ROI（第二级裁剪）**
- **格式**: `[[y1, x1], [y2, x2]]` - 矩形区域的左上角和右下角坐标
- **用途**: 在分析阶段进行第二级裁剪，进一步精确定位分析区域
- **适用算法**: 主要用于STIV算法的精确分析区域定位
- **示例**: `[[100, 650], [700, 1200]]` 表示从(650,100)到(1200,700)的矩形区域
- **设置为null**: 不进行分析裁剪，使用预处理后的图像

**坐标格式说明**
- 坐标格式为 `[[y1, x1], [y2, x2]]`，注意y坐标在前，x坐标在后
- `y1, x1`: 矩形区域的左上角坐标（行，列）
- `y2, x2`: 矩形区域的右下角坐标（行，列）
- 坐标原点(0,0)位于图像左上角
- 确保 `y1 < y2` 且 `x1 < x2`

**使用示例**
```yaml
# 示例1：阿育王站视频的ROI配置
preprocessing:
  pre_roi: [[75, 600], [750, 1240]]   # 预处理：移除图像上下边缘
  roi: [[100, 650], [700, 1200]]      # 分析：聚焦于水流主要区域

# 示例2：五乡站视频的ROI配置
preprocessing:
  pre_roi: [[50, 400], [800, 1500]]   # 预处理：移除图像边缘干扰
  roi: [[80, 450], [750, 1450]]       # 分析：聚焦于河道中心区域

# 示例3：不使用ROI裁剪
preprocessing:
  pre_roi: null                        # 不进行预处理裁剪
  roi: null                           # 不进行分析裁剪
```

#### 其他预处理参数
- `rotate_image`: 是否旋转图像（布尔值）
- `resolution`: 分辨率缩放因子（浮点数，1.0表示原始分辨率）
- `image_correction`: 图像校正参数（镜头失真校正等）

### ROI配置与算法的关系

**STIV算法**：
- 使用矩形ROI裁剪（`pre_roi`和`roi`）
- 支持两级裁剪：预处理裁剪 → 分析裁剪
- 裁剪后的图像用于生成Space-Time Images

**PIV/OTV算法**：
- 主要使用多边形掩码（`roi_points`）
- 可选择性使用矩形ROI作为预处理步骤
- `roi_points`格式：`[[x1,y1], [x2,y2], [x3,y3], [x4,y4]]`

### ROI设置最佳实践

1. **逐步缩小原则**：先设置较大的`pre_roi`，再设置较小的`roi`
2. **保留关键区域**：确保水流主要区域包含在ROI内
3. **排除干扰源**：移除岸边、桥梁、阴影等干扰区域
4. **考虑算法需求**：STIV需要足够的空间生成分析线
5. **测试验证**：使用调试模式查看ROI可视化图像，确认设置正确

## STIV算法配置

```yaml
stiv_params:
  # 坐标系统配置
  coordinate_system:
    type: "pixel_distance"  # "pixel_distance" 或 "geographic"
    calibration:
      # 像素+距离坐标系统
      pixels: [[590, 685], [125, 585], [950, 1050], [1110, 615]]
      distances:
        "(0,1)": 74.5
        "(0,2)": 54.6
        "(0,3)": 38.4
        "(1,2)": 127.4
        "(1,3)": 68.5
        "(2,3)": 86.7
      # 经纬度坐标系统示例（注释掉）
      # coordinates:
      #   - pixel: [1260, 180]
      #     lat: 29.858377
      #     lon: 121.746625
      #   - pixel: [1000, 130]
      #     lat: 29.858407
      #     lon: 121.746658
  
  # 分析线配置
  analysis_lines:
    mode: "adaptive"      # "adaptive" 或 "manual"
    flow_direction: 0.0   # 流向角度（度）
    line_count: 3         # 分析线数量
    line_spacing: 50      # 线间距（像素）
    line_length: 200      # 线长度（像素）
    center_point: null    # 中心点坐标 [x, y]，null时使用图像中心
    # 手动模式配置（向后兼容）
    # lines: [400, 450, 500]
    # lines_range: [[700, 1700], [700, 1700], [700, 1700]]
  
  # 算法参数
  algorithm:
    window_shape: [51, 51]
    filter_window: 64
    overlap: 0
    polar_filter_width: 10
    ksize: 7
    method: "fft"  # "fft" 或 "gmt"，默认"fft"与原始算法一致
  
  # 注意：预处理参数现在是全局配置，请参考"通用预处理配置"部分
```

### STIV参数详解

#### 坐标系统配置
- `type`: 坐标系统类型
  - `"pixel_distance"`: 像素+距离标定（向后兼容）
  - `"geographic"`: 经纬度坐标系统
- `calibration`: 标定数据
  - 经纬度模式：包含像素坐标和对应的经纬度
  - 像素+距离模式：包含像素坐标和距离信息

#### 分析线配置
- `mode`: 分析线模式
  - `"adaptive"`: 自适应模式，根据流向自动生成分析线
  - `"manual"`: 手动模式，手动指定分析线位置（向后兼容）
- `flow_direction`: 水流方向角度（度），0为水平向右
- `line_count`: 分析线数量
- `line_spacing`: 分析线间距（像素）
- `line_length`: 分析线长度（像素）
- `center_point`: 分析线中心点坐标 [x, y]，null时使用图像中心
  - **奇数线数**：一条线穿过中心点，其余线在两侧对称分布
  - **偶数线数**：以中心点为基准画虚拟中线，在中线两侧对称分布

#### 算法参数
- `window_shape`: GMT方法的窗口大小
- `filter_window`: 滤波窗口大小
- `overlap`: GMT方法的重叠度
- `polar_filter_width`: 极坐标滤波宽度
- `ksize`: GMT方法的核大小
- `method`: 运动计算方法，"fft"或"gmt"
  - 原始STIV算法只使用FFT方法
  - v2版本增加了GMT方法选项，提供更多算法对比和研究可能性
  - 默认使用"fft"保持与原始算法的一致性

#### 预处理参数
STIV算法使用全局的预处理配置，详细说明请参考"通用预处理配置"部分。

## 配置文件使用

### 加载配置
```python
from src.utils.config_manager import ConfigManager

# 加载配置
config_manager = ConfigManager('config/batch_config.yaml')

# 获取特定算法配置
piv_config = config_manager.get_algorithm_config('piv')
otv_config = config_manager.get_algorithm_config('otv')
stiv_config = config_manager.get_algorithm_config('stiv')

# 获取全局配置
debug_enabled = config_manager.is_debug_enabled()
log_level = config_manager.get_log_level()
```

### 配置验证
```python
# 验证配置
is_valid, errors = config_manager.validate_config('stiv')
if not is_valid:
    print("配置错误:", errors)
```

### 保存配置
```python
# 保存配置到新文件
config_manager.save_config('output/new_config.yaml')
```

## 配置最佳实践

1. **统一管理**：所有算法配置都放在同一个YAML文件中
2. **模块化**：每个算法有独立的配置节
3. **全局参数**：调试、输出等全局参数统一管理
4. **向后兼容**：保持对旧版配置格式的支持
5. **参数验证**：使用配置管理器进行参数验证
6. **文档同步**：配置变更时及时更新文档

## 配置示例

完整的配置示例请参考 `config/batch_config.yaml` 文件。
