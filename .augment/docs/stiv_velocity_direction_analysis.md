# STIV流速方向识别和箭头绘制问题分析

## 问题描述
当前STIV算法显示的流速箭头方向与肉眼观察到的实际流动方向相反。需要分析流速计算逻辑和箭头绘制逻辑，找出问题根源。

## 重要更正（2025-09-04更新）
经过多个权威来源的深入研究，确认：
**分析线应该平行于流向，这是STIV算法的正确实现**。之前认为分析线应垂直于流向的理解是错误的。

## 代码分析结果

### 1. 流速计算逻辑分析

#### 1.1 STI生成过程
- **空间轴**：沿分析线方向提取像素，形成STI的水平轴（x轴）
- **时间轴**：随视频帧序列累积，形成STI的垂直轴（y轴）
- **STI结构**：`sti_array.shape = (帧数, 分析线像素数)`

#### 1.2 角度计算（FFT方法）
```python
# 在 MotionCalculator.calculate_velocity_fft() 中
angle0 = 2 * math.pi * freq / sti_ft_polar.shape[0]  # 基于时间轴
angle1 = 2 * math.pi * freq / sti_ft_polar.shape[1]  # 基于空间轴
angle = (angle0 + angle1) / 2
```

#### 1.3 速度计算
```python
# 在 MotionCalculator._angle_to_velocity() 中
velocity = math.tan(angle) * fps / ppm
```

**关键发现**：
- `math.tan(angle)` 计算的是 `dx/dt`（空间变化率/时间变化率）
- 正角度表示物体沿分析线正方向移动
- 负角度表示物体沿分析线负方向移动

### 2. 分析线定义分析

#### 2.1 分析线起点和终点定义
```python
# 在 AnalysisLines._create_analysis_line() 中
ideal_start_x = center_x - line_dx  # 起点
ideal_start_y = center_y - line_dy
ideal_end_x = center_x + line_dx    # 终点  
ideal_end_y = center_y + line_dy
```

#### 2.2 分析线方向计算
```python
# 分析线垂直于流向
line_angle_rad = flow_angle_rad + math.pi / 2
line_dx = math.cos(line_angle_rad) * half_length
line_dy = math.sin(line_angle_rad) * half_length
```

**关键发现**：
- 分析线从起点指向终点的方向定义了"正方向"
- 流速正值表示物体沿起点→终点方向移动
- 流速负值表示物体沿终点→起点方向移动

### 3. 箭头绘制逻辑分析

#### 3.1 当前箭头绘制逻辑
```python
# 在 _draw_velocity_arrow() 中
if velocity >= 0:
    # 正流速：箭头指向分析线终点方向
    direction_x = line_unit_x
    direction_y = line_unit_y
else:
    # 负流速：箭头指向分析线起点方向  
    direction_x = -line_unit_x
    direction_y = -line_unit_y
```

### 4. 问题根源分析

#### 4.1 物理意义对应关系
1. **STI中的角度**：表示物体在时空图中的运动轨迹角度
2. **分析线方向**：定义了空间坐标系的正方向
3. **流速符号**：
   - 正值：物体沿分析线起点→终点方向移动
   - 负值：物体沿分析线终点→起点方向移动

#### 4.2 可能的问题点

**问题1：分析线方向与实际流向的关系**
- 分析线应该垂直于流向，但起点→终点的方向可能与期望的"正流向"相反

**问题2：STI角度计算的符号约定**
- `math.tan(angle)` 的符号可能与实际物理运动方向相反

**问题3：箭头绘制的方向映射**
- 当前逻辑：正流速→指向终点，负流速→指向起点
- 可能需要反向：正流速→指向起点，负流速→指向终点

## 修复建议

### 方案1：修改箭头绘制逻辑（推荐）
```python
def _draw_velocity_arrow(self, image: np.ndarray, line, velocity: float):
    # 计算分析线的方向向量
    line_dx = line.end_point[0] - line.start_point[0]
    line_dy = line.end_point[1] - line.start_point[1]
    line_length = math.sqrt(line_dx**2 + line_dy**2)
    
    if line_length == 0:
        return
    
    # 归一化分析线方向向量
    line_unit_x = line_dx / line_length
    line_unit_y = line_dy / line_length
    
    # 计算箭头长度
    arrow_length = min(60, max(25, abs(velocity) * 30))
    
    # 修改：反转箭头方向逻辑
    if velocity >= 0:
        # 正流速：箭头指向分析线起点方向（与当前相反）
        direction_x = -line_unit_x
        direction_y = -line_unit_y
        arrow_start_x = line.end_point[0] - line_unit_x * 30
        arrow_start_y = line.end_point[1] - line_unit_y * 30
    else:
        # 负流速：箭头指向分析线终点方向（与当前相反）
        direction_x = line_unit_x
        direction_y = line_unit_y
        arrow_start_x = line.start_point[0] + line_unit_x * 30
        arrow_start_y = line.start_point[1] + line_unit_y * 30
    
    # 其余代码保持不变...
```

### 方案2：修改速度计算符号
```python
def _angle_to_velocity(self, angle: float, fps: float) -> float:
    ppm = self.coordinate_system.calculate_ppm()
    # 添加负号反转速度符号
    return -math.tan(angle) * fps / ppm
```

### 方案3：修改分析线起点终点定义
在 `_create_analysis_line()` 中交换起点和终点的定义。

## 推荐解决方案

**推荐使用方案1**，原因：
1. 影响范围最小，只修改可视化部分
2. 不影响核心算法逻辑
3. 易于验证和回滚
4. 保持与现有配置的兼容性

## 权威研究结果（2025-09-04）

### 1. ChatGPT深度研究确认
- 文献明确指出："STIV分析（搜索）线应与河流主流方向平行，即'沿主流方向设置分析线'"
- "如果分析线垂直于水流方向，水面纹理会快速穿过分析线，时空图像中不会出现稳定的倾斜条纹"
- 来源：https://chatgpt.com/s/dr_68b955c4ff548191ae0e09dc939db316

### 2. Gemini思维模型搜索确认
- "分析线（或测速线）的方向应与河流主流方向在同一水平方向上，即相互平行"
- "为了准确捕捉水流方向上的流速信息，分析线必须沿水流的主要运动方向设置"
- "如果分析线垂直于水流方向，时空图像中就无法形成能够反映流速的有效倾斜条纹"

### 3. 学术论文确认
- MDPI论文明确提到："lines set parallel to the streamwise direction"
- 多个学术来源都指出："search line parallel to the main flow direction"

### 4. STIV算法原理重新确认
- **核心原理**：STIV算法必须使用平行于流向的分析线来检测运动
- **物理原理**：平行于流向的分析线能够检测沿流向运动的物体在时空图像中形成的倾斜条纹
- **数学基础**：条纹的倾斜角度与流速成正比关系

### 5. 代码修正
- 原代码 `line_angle_rad = flow_angle_rad + math.pi / 2` 是**错误的**
- 正确代码应为 `line_angle_rad = flow_angle_rad`（平行于流向）
- 这是基于多个权威来源确认的STIV算法标准实现

## 最终结论

**分析线方向需要修正**：
- **错误设置**：分析线与河流方向垂直
- **正确设置**：分析线与河流方向平行
- 修改：将 `line_angle_rad = flow_angle_rad + math.pi / 2` 改为 `line_angle_rad = flow_angle_rad`

## 验证方法

1. 使用已知流向的测试视频
2. 对比修改前后的箭头方向
3. 确认箭头方向与实际流动方向一致
4. 检查不同流速值（正负）的箭头显示是否正确
