# STIV算法频率检测统计分析报告

## 概述
本文档基于完整的STIV算法调试数据，对两个测试视频的频率检测结果进行深度统计分析，重点识别低速视频产生高估的数值模式和根本原因。

## 数据来源
- **低速视频**: 0812_speed_720.mp4 (实际流速约0.073 m/s)
- **正常视频**: ch01_20250903080000.mp4 (实际流速约0.331 m/s)
- **分析线数量**: 低速视频13条，正常视频15条
- **数据获取时间**: 2025-09-09 14:50:10

## 1. 频率值统计分析

### 1.1 低速视频频率统计
```
原始数据: 487, 570, 510, 539, 130, 527, 530, 573, 360, 0, 12, 0, 12
```

| 统计指标 | 数值 |
|----------|------|
| **平均值** | 288.5 |
| **中位数** | 360.0 |
| **标准差** | 244.4 |
| **最大值** | 573 |
| **最小值** | 0 |
| **数据范围** | 573 |

### 1.2 正常视频频率统计
```
原始数据: 375, 375, 282, 375, 375, 308, 321, 328, 375, 375, 360, 0, 29, 244, 187
```

| 统计指标 | 数值 |
|----------|------|
| **平均值** | 287.3 |
| **中位数** | 328.0 |
| **标准差** | 140.2 |
| **最大值** | 375 |
| **最小值** | 0 |
| **数据范围** | 375 |

### 1.3 频率值对比分析

| 特征 | 低速视频 | 正常视频 | 差异分析 |
|------|----------|----------|----------|
| **平均频率** | 288.5 | 287.3 | 基本相同 |
| **频率稳定性** | 标准差244.4 | 标准差140.2 | **低速视频变异性更大** |
| **高频率集中度** | 分散分布 | 375频率占40% | **正常视频有明显主导频率** |
| **零频率比例** | 23.1% (3/13) | 6.7% (1/15) | **低速视频零频率更多** |

## 2. 峰值比统计分析

### 2.1 低速视频峰值比统计
```
原始数据: 3.22, 5.83, 3.42, 3.00, 2.71, 2.58, 2.28, 4.26, 4.46, 2.50, 2.60, 5.52, 3.02
```

| 统计指标 | 数值 |
|----------|------|
| **平均值** | 3.42 |
| **中位数** | 3.02 |
| **标准差** | 1.18 |
| **最大值** | 5.83 |
| **最小值** | 2.28 |

### 2.2 正常视频峰值比统计
```
原始数据: 2.35, 3.36, 2.19, 3.09, 2.53, 1.93, 2.17, 2.46, 2.33, 3.05, 2.83, 3.64, 2.17, 2.45, 2.77
```

| 统计指标 | 数值 |
|----------|------|
| **平均值** | 2.62 |
| **中位数** | 2.46 |
| **标准差** | 0.52 |
| **最大值** | 3.64 |
| **最小值** | 1.93 |

### 2.3 峰值比对比分析

| 特征 | 低速视频 | 正常视频 | 差异分析 |
|------|----------|----------|----------|
| **平均峰值比** | 3.42 | 2.62 | **低速视频峰值比更高** |
| **峰值比稳定性** | 标准差1.18 | 标准差0.52 | **低速视频变异性更大** |
| **信号质量** | 不稳定 | 相对稳定 | **正常视频信号更可靠** |

## 3. 频率分布特征分析

### 3.1 频率分布模式

**低速视频频率分布**：
- **高频段(500-600)**: 6个值 (46.2%) - 487, 570, 510, 539, 527, 530, 573
- **中频段(100-400)**: 4个值 (30.8%) - 130, 360
- **低频段(0-50)**: 3个值 (23.1%) - 0, 12, 0, 12

**正常视频频率分布**：
- **高频段(300-400)**: 11个值 (73.3%) - 375×6, 308, 321, 328, 360
- **中频段(100-300)**: 3个值 (20.0%) - 282, 244, 187
- **低频段(0-50)**: 1个值 (6.7%) - 0, 29

### 3.2 异常频率检测

**定义合理频率范围**: 基于正常视频的主要分布，设定合理范围为[50, 400]

**低速视频异常频率**：
- **超出上限**: 487, 570, 510, 539, 527, 530, 573 (7个，53.8%)
- **低于下限**: 0, 12, 0, 12 (4个，30.8%)
- **正常范围**: 130, 360 (2个，15.4%)

**正常视频异常频率**：
- **超出上限**: 无
- **低于下限**: 0, 29 (2个，13.3%)
- **正常范围**: 13个 (86.7%)

## 4. 关键发现和问题根源

### 4.1 主要发现

1. **频率检测失效**: 低速视频中53.8%的频率值超出合理范围
2. **信号质量差异**: 低速视频峰值比变异性是正常视频的2.3倍
3. **噪声主导**: 低速视频缺乏明显的主导频率，信号被噪声掩盖
4. **检测算法局限**: `np.argmax(isd)`在低信噪比条件下选择错误峰值

### 4.2 问题根源分析

**物理层面**：
- 0.073 m/s的流速接近视觉检测下限
- 运动信号微弱，容易被图像噪声掩盖

**算法层面**：
- 简单的最大值选择无法区分真实信号和噪声峰值
- 缺乏信号质量评估和噪声过滤机制
- 频率合理性验证机制缺失

**数学层面**：
- 错误频率通过tan函数被非线性放大
- 高频率值导致大角度，产生大tan值
- 速度计算公式: velocity = tan(angle) × fps / ppm 中的放大效应

## 5. 结论

**核心问题确认**: 主频率检测环节确实是导致低速视频高估的根本原因。

**数值证据**: 低速视频中84.6%的频率值异常（53.8%过高 + 30.8%过低），而正常视频仅13.3%异常。

**改进方向**: 需要实现更智能的频率选择算法，包括信号质量评估、噪声过滤和频率合理性验证机制。
