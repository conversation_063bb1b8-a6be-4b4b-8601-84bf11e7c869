好 — 我会先按你给出的对应关系（公式编号）逐个说明公式里每个变量的含义与获取方法，然后给出基于这些公式的 **Python 实现（完整脚本）**，包含四个优化步骤的函数，能直接作用于一张 STI（灰度二维数组）。我会尽量简明、可直接运行并在代码注释里说明关键点与可调参数。说明中引用了你上传的论文。

---

# 一、公式与变量说明（对应你列出的编号）

**3.1 窗函数滤波 — 公式 (4),(5),(6)**

* 公式 (4)：一维 Hanning 窗

  * $W(n)=\tfrac{1}{2}\bigl[1-\cos(2\pi n/N)\bigr],\;0\le n\le N$
  * $W(m)=\tfrac{1}{2}\bigl[1-\cos(2\pi m/M)\bigr],\;0\le m\le M$
  * 变量：

    * $M,N$：STI 的空间尺寸（行数 M，对应时间轴长度，列数 N，对应空间轴长度），可由 `sti.shape` 获得。
    * $m,n$：像素索引。
* 公式 (5)：二维窗函数合成

  * $W(m,n)=W(m)\otimes W(n)$（论文写法为外积）
* 公式 (6)：窗函数加权

  * $I'(m,n)=W(m,n)\cdot I(m,n)$
* **获取方式**：直接用 NumPy `np.hanning(M)` / `np.hanning(N)` 生成，再做外积得到二维窗并逐元素相乘。

---

**3.2 傅里叶谱主方向检测 — 公式 (7),(8),(9),(10)**

* (7) 2D DFT：

  $$
  F(u,v)=\sum_{x=0}^{M-1}\sum_{y=0}^{N-1} f(x,y)\exp\!\bigl[-j2\pi(ux/M + vy/N)\bigr]
  $$

  * 变量：`f(x,y)` 是空间域图像（STI），`F(u,v)` 是复频谱。用 `np.fft.fft2` 实现。
* (8) 频谱幅值：
  $|F(u,v)|=\sqrt{R^2+I^2}=\lvert F\rvert$。用 `np.abs(F)`。
* (9) 极坐标下角度能量积分：
  $|F(\theta)|=\int_0^{\rho_{max}} |F(\rho,\theta)|\,d\rho$

  * 实现上等价于：把频谱像素按角度（θ）分箱，把该角度上所有径向的幅值求和（离散化积分）。
* (10) 主方向：
  $\theta = \arg\max_{\theta} |F(\theta)|$（论文以 1° 步长搜索 0–180°）。
* **获取方式/注意**：先做 `fftshift` 将频谱中心移到数组中点；构建与频谱对应的角度矩阵（`atan2`），把角度范围映射到 0–180°，在每个角度 bin 中对幅值求和得到 `|F(θ)|`，取最大值对应的角度即主方向（论文称为粗值，可用来确定滤波方向范围）。

---

**3.3 频域滤波 — 公式 (11)**

* (11)：$F'(u,v)=F(u,v)\times J(u,v)$

  * $J$ 是掩模（mask）：论文中只保留主方向附近（论文使用 $\theta\pm4^\circ$）的谱分量，其余设为 0。
* **实现要点**：在频域（`fftshift` 后）基于角度矩阵构建布尔掩模 `J = |angle - theta| <= width`（考虑角度环绕），然后与 `Fshift` 做逐元素相乘。

---

**3.4 得到无噪 ST I — 公式 (12)**

* (12) 逆 2D DFT（IDFT）：

  $$
  f(x,y)=\frac{1}{MN}\sum_{u=0}^{M-1}\sum_{v=0}^{N-1} F(u,v)\exp\!\bigl[j2\pi(ux/M + vy/N)\bigr]
  $$

  * 用 `np.fft.ifft2`（并在 `ifftshift` 后）得到空间域结果，取实部作为滤波后的 STI。
* **结果**：得到的 `f(x,y)` 是“几乎无噪声”的 STI，可以用于后续边缘检测或任何纹理方向检测算法（GTM/QESTA/FFT-based）。

---

**关于速度计算（如何用角度得到速度）**
论文给出：$v = \tan\alpha \cdot S_x \cdot fps$（等价于公式 (1) 的最后形式），其中：

* $\alpha$：STI 中的主方向角（MOT，单位°，需与频域得到的 θ 对齐——可能需做 $\alpha=\theta$ 或 $ \alpha=90^\circ-\theta$ 的坐标转换，取决于 STI 的坐标约定，需在实际数据上校验）。
* $S_x$：横轴空间分辨率（米/像素），来自相机标定 / 透视变换（用 GCP 或单应矩阵换算像素到物理长度）。
* `fps`：相机帧率（帧/秒），视频元数据。

> 注意：St（论文中 Sec/pixel）与 fps 互为倒数：St = 1 / fps（论文符号解释上有点混淆，使用前请根据你的 STI 时间轴构造确认）。

（以上论文相关说明参见你上传的文档。）

---

# 二、基于公式的 Python 实现（完整脚本）

下面给出一个完整且可直接运行的脚本（依赖：`numpy`, `scipy`, `opencv-python` 可选做 Canny, `matplotlib` 用于调试显示——非必须）。脚本包含四个函数，按步骤执行并返回中间结果，最后给出如何用角度计算速度的示例。

> 文件：`stiv_denoise.py`（把 STI 读成灰度二维 `float64` 矩阵后，按顺序调用即可）

```python
# stiv_denoise.py
import numpy as np
import cv2        # optional, 用于 Canny / 可视化；若不需要可删除
from scipy import ndimage
from typing import Tuple

def window_function_filter(sti: np.ndarray) -> np.ndarray:
    """
    Step 3.1: 用二维 Hanning 窗对 STI 加窗，防止 FFT 的边界断裂。
    输入:
        sti: 2D 灰度数组 (M x N), dtype float
    返回:
        sti_windowed: same shape, float
    """
    M, N = sti.shape
    w_m = np.hanning(M)    # length M
    w_n = np.hanning(N)    # length N
    W = np.outer(w_m, w_n)  # M x N
    sti_windowed = W * sti
    return sti_windowed

def detect_principal_direction(sti_windowed: np.ndarray, step_deg: int = 1) -> Tuple[float, np.ndarray, np.ndarray]:
    """
    Step 3.2: 计算频谱并按角度求幅值积分，返回主方向 theta（0-180度）。
    返回: theta_deg, Fshift (complex fftshifted), magnitude (abs of Fshift)
    """
    # FFT
    F = np.fft.fft2(sti_windowed)
    Fshift = np.fft.fftshift(F)
    mag = np.abs(Fshift)

    M, N = mag.shape
    # 构造频域坐标（以中心为原点）
    u = np.arange(-M//2, M//2) if M % 2 == 0 else np.arange(-M//2, M//2+1)
    v = np.arange(-N//2, N//2) if N % 2 == 0 else np.arange(-N//2, N//2+1)
    U, V = np.meshgrid(v, u)  # 注意 meshgrid 顺序: 列索引 v -> x 轴, 行索引 u -> y 轴

    # 计算角度矩阵（度），范围映射到 [0,180)
    ang = np.degrees(np.arctan2(V, U))  # arctan2(y, x)
    ang = np.mod(ang + 360.0, 180.0)    # 将 [-180,180) 映射到 [0,180)

    # 按角度分箱（0..179）
    ang_flat = ang.ravel()
    mag_flat = mag.ravel()
    bins = np.floor(ang_flat / step_deg).astype(int)
    max_bin = int(180 // step_deg)
    # 防止越界
    bins = np.clip(bins, 0, max_bin - 1)

    # 累加每个角度 bin 的幅值（离散化极坐标积分）
    angle_sum = np.bincount(bins, weights=mag_flat, minlength=max_bin)

    # 找到最大值对应角度
    theta_idx = int(np.argmax(angle_sum))
    theta_deg = theta_idx * step_deg  # 论文用步长 1°
    return float(theta_deg), Fshift, mag

def frequency_domain_filter(Fshift: np.ndarray, theta_deg: float, width_deg: float = 4.0) -> np.ndarray:
    """
    Step 3.3: 构建角度掩模 J，只保留 theta +/- width_deg 的谱分量。
    输入:
        Fshift: fftshifted 2D complex spectrum
        theta_deg: 主方向角度（0-180）
        width_deg: ±保留范围（默认 4°）
    返回:
        F_filtered_shift (complex)
    """
    M, N = Fshift.shape
    # 构造角度矩阵（和 detect 里一致的坐标系）
    u = np.arange(-M//2, M//2) if M % 2 == 0 else np.arange(-M//2, M//2+1)
    v = np.arange(-N//2, N//2) if N % 2 == 0 else np.arange(-N//2, N//2+1)
    U, V = np.meshgrid(v, u)
    ang = np.degrees(np.arctan2(V, U))
    ang = np.mod(ang + 360.0, 180.0)

    # 计算最小角度差（考虑环绕）
    diff = np.abs((ang - theta_deg + 90.0) % 180.0 - 90.0)  # 变换以得到对称最小差
    # 上面是一个常用 trick：使 diff ∈ [0,90]，但也可直接做 min(|a-b|, 180-|a-b|)
    mask = diff <= width_deg
    # 乘以掩模
    F_filtered = Fshift * mask.astype(float)
    return F_filtered

def get_noiseless_sti(F_filtered_shift: np.ndarray) -> np.ndarray:
    """
    Step 3.4: 逆移位 + 逆 FFT -> 得到无噪声 STI（实部）。
    返回 float64 数组，按原始尺度可选归一化或截断。
    """
    # 先 inverse shift
    F_unshift = np.fft.ifftshift(F_filtered_shift)
    f_rec = np.fft.ifft2(F_unshift)
    f_rec_real = np.real(f_rec)
    # 可将结果线性映射回原范围（视需要），这里返回实部
    return f_rec_real

# ---------- 速度计算辅助 ----------
def sti_angle_to_velocity(theta_deg: float, Sx_m_per_pixel: float, fps: float, angle_map='identity') -> float:
    """
    用论文给的公式 v = tan(alpha) * Sx * fps 计算速度。
    参数:
      - theta_deg: 由频域得到的角度（0-180），可能需与 STI 中 alpha 对齐。
      - angle_map: 若需要转换，可用 'identity'（直接用 theta）或 'rot90'（alpha = 90 - theta）等。
    返回: v (m/s)
    """
    if angle_map == 'identity':
        alpha_deg = theta_deg
    elif angle_map == 'rot90':
        alpha_deg = 90.0 - theta_deg
    else:
        alpha_deg = theta_deg

    alpha_rad = np.deg2rad(alpha_deg)
    v = np.tan(alpha_rad) * Sx_m_per_pixel * fps
    return float(v)

# ---------- 示例流程 ----------
def denoise_sti_pipeline(sti: np.ndarray, Sx=1.0, fps=30.0):
    """
    完整流程演示：窗函数 -> FFT 主方向检测 -> 频域滤波 -> 逆变换 -> 返回 (theta, sti_denoised)
    """
    sti_w = window_function_filter(sti)
    theta, Fshift, mag = detect_principal_direction(sti_w)
    Ff = frequency_domain_filter(Fshift, theta, width_deg=4.0)
    sti_denoised = get_noiseless_sti(Ff)
    v_est = sti_angle_to_velocity(theta, Sx, fps, angle_map='identity')  # 注意角度映射需验证
    return {'theta_deg': theta, 'sti_denoised': sti_denoised, 'v_est_m_s': v_est, 'Fshift': Fshift, 'mag': mag}

# ---------- 若要做边缘检测（可选） ----------
def edge_detection(img: np.ndarray, sigma=1.0, low=50, high=150):
    """对输出的无噪 STI 做 Canny 边缘检测（先模糊再检测）"""
    img_u8 = cv2.normalize(img, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
    blur = cv2.GaussianBlur(img_u8, (5,5), sigma)
    edges = cv2.Canny(blur, low, high)
    return edges

# 使用示例（假设你有 sti_array）
# if __name__ == '__main__':
#     sti = ...  # load your STI to a 2D numpy array (float)
#     out = denoise_sti_pipeline(sti, Sx=0.03, fps=25.0)
#     print('theta:', out['theta_deg'], 'v_est:', out['v_est_m_s'])
```

---

# 三、补充说明与实践建议（简要）

1. **坐标/角度对齐**：论文在实现上把极坐标角度映射为 0–180° 并直接把最大能量角度作为主方向（θ）。在把 θ 用作速度公式中的 α 前，务必在少量样例上**可视化对比**（把反变换后的 STI 中斜线方向与 θ 对应关系验证），必要时做 `alpha = 90 - theta` 或其它简单线性变换以对齐坐标系。
2. **窗函数选择**：论文用 Hanning（不引入新参数）。也可尝试 Hamming、Blackman 等，但 Hanning 通常足够且无额外调参。
3. **角度 bin 大小**：论文用 1° 步长（实现中 `step_deg=1`）。若噪声大可用 0.5° 更精细（代价是计算量）。
4. **保留角度宽度**：论文用了 ±4°；在弱噪声场景可减小到 ±2°，在强噪声或纹理较分散时可增大。
5. **FFT 的尺度/零频中心**：始终在 `fftshift` 坐标上做角度计算与掩模，再 `ifftshift` 回传入 `ifft2`。
6. **Sx（m/pixel）获取**：需要相机标定 + 图像到实世界的单应/透视变换（用论文中 GCP 方法或 OpenCV 的 `cv2.findHomography`/`solvePnP`）。这一步和 STI 的生成（像素空间）密切相关。
7. **速度稳定性**：论文指出 FFT + 新滤波在实验中对速度的相对误差最小（表格结果），建议把本脚本中 `theta` 的计算结果与其他方法（GTM/QESTA）交叉验证。


