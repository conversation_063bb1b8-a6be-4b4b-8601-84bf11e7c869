import cv2
import numpy as np
import json
from pathlib import Path
import os

CONFIG_PATH = Path("config/awive_config.json")
OUTPUT_DIR = Path("data/output")
OUTPUT_FILENAME = "pre_flow.mp4"

# --- Helper Functions ---

def load_config(config_fp: Path) -> dict:
    """Loads the JSON configuration file."""
    if not config_fp.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_fp}")
    with open(config_fp, 'r') as f:
        return json.load(f)

def draw_grid(frame, grid_step=50):
    """Draws a grid and coordinate axes on the frame."""
    h, w = frame.shape[:2]
    # Draw vertical lines and labels
    for x in range(0, w, grid_step):
        cv2.line(frame, (x, 0), (x, h), (200, 200, 200), 1)
        cv2.putText(frame, str(x), (x + 5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    # Draw horizontal lines and labels
    for y in range(0, h, grid_step):
        cv2.line(frame, (0, y), (w, y), (200, 200, 200), 1)
        cv2.putText(frame, str(y), (5, y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    return frame

def draw_roi(frame, roi_coords, label, color, thickness=2):
    """Draws a labeled rectangle for an ROI."""
    if not roi_coords or len(roi_coords) != 2 or len(roi_coords[0]) != 2 or len(roi_coords[1]) != 2:
        print(f"Warning: Invalid ROI coordinates format for label '{label}': {roi_coords}")
        return frame
    # Config uses [[y1, x1], [y2, x2]]
    # OpenCV uses (x, y)
    y1, x1 = map(int, roi_coords[0])
    y2, x2 = map(int, roi_coords[1])

    # Ensure coordinates are integers for drawing
    # pt1 = tuple(map(int, roi_coords[0])) # Original incorrect line: (y1, x1)
    # pt2 = tuple(map(int, roi_coords[1])) # Original incorrect line: (y2, x2)
    pt1 = (x1, y1) # Correct: (x, y) for top-left
    pt2 = (x2, y2) # Correct: (x, y) for bottom-right

    cv2.rectangle(frame, pt1, pt2, color, thickness)
    # Put label near the top-left corner of the ROI (using pt1 which is (x1, y1))
    label_pos = (pt1[0] + 5, pt1[1] + 20)
    cv2.putText(frame, label, label_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, thickness)
    return frame

def draw_lines(frame, lines_y, color, width, height, thickness=2):
    """Draws horizontal lines across the frame."""
    if not lines_y:
        return frame
    for y in lines_y:
        y_int = int(y) # Ensure y is integer
        cv2.line(frame, (0, y_int), (width, y_int), color, thickness)
        cv2.putText(frame, f"line: {y_int}", (5, y_int - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    return frame


# --- Main Processing Function ---

def visualize_configuration(config: dict):
    """
    Reads a video, draws configured ROIs and grid, saves as pre_flow.mp4.
    """
    # Extract paths and parameters
    video_path_str = config.get("dataset", {}).get("video_fp")
    if not video_path_str:
        print("Error: 'dataset.video_fp' not found in configuration.")
        return
    video_path = Path(video_path_str)
    if not video_path.exists():
         print(f"Error: Video file not found: {video_path}")
         return

    preprocessing_cfg = config.get("preprocessing", {})
    water_level_cfg = config.get("water_level", {})

    pre_roi = preprocessing_cfg.get("pre_roi")
    main_roi = preprocessing_cfg.get("roi")
    wl_roi = water_level_cfg.get("roi")
    wl_roi2 = water_level_cfg.get("roi2")
    lines = config.get("lines") # Expecting a list of y-coordinates

    # Initialize video capture
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    print(f"Input video: {video_path.name} ({width}x{height} @ {fps:.2f} FPS)")

    # Set up video output
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    output_path = OUTPUT_DIR / OUTPUT_FILENAME
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
    print(f"Outputting visualization to: {output_path}")

    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # Make a copy to draw on
        output_frame = frame.copy()

        # Draw grid
        output_frame = draw_grid(output_frame)

        # Draw ROIs with different colors and labels
        # Note: OpenCV uses (x, y) coordinates, config uses [[x, y], [x, y]]
        output_frame = draw_roi(output_frame, pre_roi, "Pre-ROI", (255, 0, 0)) # Blue
        output_frame = draw_roi(output_frame, main_roi, "Main-ROI", (0, 255, 0)) # Green
        output_frame = draw_roi(output_frame, wl_roi, "WL-ROI", (0, 0, 255)) # Red
        output_frame = draw_roi(output_frame, wl_roi2, "WL-ROI2", (255, 255, 0)) # Cyan

        # Draw horizontal lines
        output_frame = draw_lines(output_frame, lines, (0, 255, 255), width, height) # Yellow

        # Write frame to output video
        out.write(output_frame)

        frame_count += 1
        if frame_count % 100 == 0:
            print(f"Processed {frame_count} frames...")

    # Release resources
    cap.release()
    out.release()
    print(f"Finished processing. Output saved to {output_path}")
    # cv2.destroyAllWindows() # Comment out as running headlessly

# --- Script Execution ---

if __name__ == "__main__":
    try:
        config_data = load_config(CONFIG_PATH)
        visualize_configuration(config_data)
    except FileNotFoundError as e:
        print(e)
    except Exception as e:
        print(f"An unexpected error occurred: {e}") 