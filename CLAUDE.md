# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a water flow velocity calculation system using computer vision techniques to replace traditional flow meters. The system analyzes video footage from cameras installed at water stations to calculate flow velocity, reducing costs and enabling more comprehensive data collection.

## Architecture
The project implements multiple algorithms for flow velocity calculation:
- **OTV (Optical Tracking Velocimetry)**: Uses OpenCV's optical flow to track features
- **PIV (Particle Image Velocimetry)**: Uses OpenPIV library for cross-correlation analysis
- **AdaptivePIV**: Advanced PIV with adaptive window sizing
- **STIV (Space-Time Image Velocimetry)**: Space-time analysis methods

## Key Components

### Core Analysis Algorithms
- `src/analysis_algorithms/opencv_otv_analyzer.py`: OpenCV-based OTV implementation
- `src/analysis_algorithms/openpiv_piv_analyzer.py`: OpenPIV-based PIV implementation

### Utilities
- `src/utils/video_trimmer.py`: Video preprocessing and ROI cropping
- `src/utils/piv_preprocessor.py`: PIV-specific preprocessing
- `src/utils/logging_utils.py`: Centralized logging configuration
- `src/utils/adjust_config.py`: Configuration management utilities
- `src/utils/visualize_setup.py`: Visualization tools

### Libraries
- `lib/awive/`: Advanced WIld VElocimetry library (configurable algorithms)
- `lib/AdaptivePIV/`: Adaptive PIV implementation with Cython extensions
- `lib/pypiv/`: Basic PIV library
- `lib/PyPostPiv/`: Post-processing for PIV results

## Configuration System

### Batch Processing Configuration
- **File**: `config/batch_config.yaml`
- **Controls**: Video input/output paths, analysis method selection, ROI parameters
- **Key Parameters**:
  - `analysis_method`: "otv" or "piv"
  - `pixel_to_meter`: Scale factor for pixel-to-meter conversion
  - `roi_points`: Region of interest polygon coordinates
  - `time_ranges`: Video time segments to analyze

### Algorithm-Specific Configs
- **OTV**: Configured via YAML in batch_config.yaml under `otv_params`
- **PIV**: Uses JSON configuration at `config/piv_config.json`

## Program Flow and Core Components

### Key Workflow Steps

#### 1. **Batch Processing Entry Point**
- **File**: `batch_process_videos.py`
- **Main Function**: `main()` (line 212)
- **Purpose**: Orchestrates entire video processing pipeline
- **Key Methods**:
  - `load_config()`: Loads YAML configuration
  - `process_single_video()`: Handles individual video processing
  - `extract_filtered_speed_from_result_file()`: Extracts final speed results

#### 2. **Video Preprocessing**
- **File**: `src/utils/video_trimmer.py`
- **Core Method**: `trim_video()` (line 104)
- **Purpose**: Crops videos to specified time ranges and applies ROI
- **Key Methods**:
  - `trim_video()`: Main trimming function with ROI support
  - `time_to_seconds()`: Converts time strings to seconds
  - `sort_polygon_points_clockwise()`: Orders ROI polygon points

#### 3. **OTV Algorithm Processing**
- **File**: `src/analysis_algorithms/opencv_otv_analyzer.py`
- **Entry Point**: `analyze_video_segment_with_otv()` (line 799)
- **Purpose**: Optical Tracking Velocimetry using OpenCV
- **Key Methods**:
  - `analyze_video_segment_with_otv()`: Main OTV analysis interface
  - `process_video_real_mode()`: Actual OTV processing (line 258)
  - `process_video_pre_mode()`: Preview mode for ROI setup (line 755)
  - `calculate_angle()`: Computes flow direction angles (line 40)
  - `is_direction_valid()`: Filters vectors by direction (line 50)
  - `filter_by_quartile()`: Removes outliers using quartile method (line 209)

#### 4. **PIV Algorithm Processing**
- **File**: `src/analysis_algorithms/openpiv_piv_analyzer.py`
- **Entry Point**: `analyze_video_segment_with_openpiv()` (line 249)
- **Purpose**: Particle Image Velocimetry using OpenPIV library
- **Key Methods**:
  - `analyze_video_segment_with_openpiv()`: Main PIV analysis interface
  - `_perform_piv_for_frame_pair()`: Processes individual frame pairs (line 30)
  - Uses OpenPIV functions: `pyprocess.extended_search_area_piv()`, `validation.sig2noise_val()`, `filters.replace_outliers()`

### Configuration Management

#### 5. **Configuration Loading**
- **File**: `batch_process_videos.py`
- **Method**: `load_config()` (line 27)
- **Purpose**: Loads `config/batch_config.yaml` for global settings
- **Key Parameters**:
  - `analysis_method`: "otv" or "piv"
  - `pixel_to_meter`: Scale conversion factor
  - `roi_points`: Polygon coordinates for analysis region
  - `time_ranges`: Video segments to analyze

#### 6. **PIV-Specific Configuration**
- **File**: `config/piv_config.json`
- **Purpose**: OpenPIV-specific parameters
- **Key Sections**:
  - `common_params`: dt, scaling_factor, roi_points
  - `piv_params`: window_size, overlap, search_area_size, sig2noise_threshold

### Data Flow Summary

```
Input Videos → Video Trimmer → Algorithm Processor → Results CSV
     ↓              ↓               ↓              ↓
data/video/ → data/output/trimmed → data/output/otv or piv → flow_speed_results.csv
```

## Common Commands

### Setup and Installation
```bash
# Install dependencies
uv sync

# Or with pip
pip install -e .
```

### Running Analysis
```bash
# Batch process videos with OTV
python batch_process_videos.py

# Run OTV analyzer directly
python src/analysis_algorithms/opencv_otv_analyzer.py

# Run PIV analyzer directly  
python src/analysis_algorithms/openpiv_piv_analyzer.py
```

### Configuration Management
```bash
# Adjust ROI and parameters via config
# Edit: config/batch_config.yaml
# Edit: config/piv_config.json (for PIV)
```

## File Structure
```
flowspeed/
├── batch_process_videos.py     # Main batch processing script
├── config/                     # Configuration files
│   ├── batch_config.yaml      # Batch processing config
│   └── piv_config.json        # PIV-specific config
├── data/                      # Data storage
│   ├── video/                 # Input videos
│   ├── output/                # Analysis results
│   └── image/                 # Reference images
├── src/                       # Source code
│   ├── analysis_algorithms/   # Core analysis implementations
│   └── utils/                 # Utility functions
└── lib/                       # External libraries
    ├── awive/                 # Advanced velocimetry library
    ├── AdaptivePIV/           # Adaptive PIV implementation
    ├── pypiv/                 # Basic PIV library
    └── PyPostPiv/             # PIV post-processing
```

## Key Workflows

### 1. Initial Setup Process
1. Place videos in `data/video/`
2. Configure `config/batch_config.yaml` with video paths
3. Set `pixel_to_meter` based on calibration
4. Define `roi_points` for analysis region
5. Set expected flow direction in degrees

### 2. Batch Processing Flow
1. `batch_process_videos.py` loads configuration
2. Videos are trimmed to specified time ranges
3. Selected algorithm (OTV/PIV) processes each segment
4. Results saved to `data/output/flow_speed_results.csv`

### 3. Direct Algorithm Usage
```python
# OTV usage
from src.analysis_algorithms.opencv_otv_analyzer import analyze_video_segment_with_otv
results = analyze_video_segment_with_otv(video_path, config)

# PIV usage  
from src.analysis_algorithms.openpiv_piv_analyzer import analyze_video_segment_with_openpiv
results = analyze_video_segment_with_openpiv(video_path, config_path, pixel_to_meter)
```

## Important Configuration Notes

### Coordinate System
- All coordinates use [x, y] format
- Origin is top-left (0,0)
- ROI points must be provided clockwise or counter-clockwise
- Pixel-to-meter conversion is critical for accurate results

### Flow Direction Convention
- 0° = horizontal right
- 90° = vertical down  
- 180° = horizontal left
- 270° = vertical up

### Typical Parameter Values
- **pixel_to_meter**: 0.0065-0.09 (site-specific)
- **direction_threshold**: 30-60 degrees
- **roi_points**: 4+ polygon vertices defining analysis region
- **time_ranges**: "0:10" to "1:00" format

## Development Guidelines

### Adding New Algorithms
1. Create new analyzer in `src/analysis_algorithms/`
2. Follow existing interface patterns
3. Add configuration support in `batch_process_videos.py`
4. Update batch_config.yaml schema

### Testing
- Test with small video segments first
- Verify ROI coordinates with visualization
- Check pixel-to-meter calibration with known references
- Validate flow direction filtering

### Debugging
- Check logs in output directories
- Enable `check_feature_points: true` for OTV debugging
- Use `add_visual_elements_to_trimmed_video: true` for visual verification
- Verify video file paths and time ranges

## Data Flow
1. **Input**: Raw video files in `data/video/`
2. **Preprocessing**: Video trimming and ROI selection
3. **Analysis**: OTV/PIV algorithm processing
4. **Output**: CSV results + visualization videos
5. **Results**: `flow_speed_results.csv` with min/avg/max velocities